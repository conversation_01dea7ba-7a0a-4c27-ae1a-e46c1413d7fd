import './App.css'
import { FabricCanvas } from './libs/fabricjs-react'
import { FabricProvider, useFabric } from './contexts/fabric.context'
import { CanvasCacheChecker } from '@/components/CanvasCacheChecker'
import { CanvasMenu, EngravingMenuKeys, GeneralMenuKeys, PublicMenuKeys } from '@/components/CanvasMenu'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { sendMessageToNative } from '@/utils/message'
import React, { FC, Fragment, useCallback, useEffect, useMemo, useState } from 'react'
import { useNativeMessageListener } from '@/hooks/useNativeMessageListener'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { PageHeaderHeight } from '@/constants/styles'
import { DeviceWorkingStatus, MessageAction } from '@/libs/shared/types'
import { useTranslation } from 'react-i18next'
import toast, { Toaster } from 'react-hot-toast'
import { useDeviceStatusStore } from '@/stores/device-status'
import clsx from 'clsx'
import { LoadingProvider } from '@/contexts/loading'
import { isDeviceWorking } from '@/utils/device'
import { useCheckObjectOverbound } from '@/hooks/useCheckObjectOverbound'

import BackIcon from '@/assets/icons/back_white_bg.svg?react'
import ErrorIcon from '@/assets/icons/error.svg?react'
import CloseIcon from '@/assets/icons/close_danger.svg?react'
import SetIcon from '@/assets/icons/set.svg?react'
import { ZIndices } from '@/constants/z-index'
import { FontManagerProvider } from '@/contexts/font-manager.context'
import { useDeviceDeviceFaultKeyStore } from '@/stores/device-fault'
import { useCanvasAutoSaver } from '@/hooks/useCanvasAutoSaver'
import { cn } from '@/libs/utils'
import { useAutoLockActiveSelection } from '@/hooks/useAutoLockActiveSelection'
import { FadeInContainer } from './components/menu-content-container'
import { GeneralOperations } from './components/operations'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: false,
      refetchOnWindowFocus: true,
      staleTime: Number.POSITIVE_INFINITY,
      retry: false
    }
  }
})

const LayerOverboundHint: FC<{ top: number }> = ({ top }) => {
  const { t } = useTranslation()
  const { selection } = useFabric()
  const { activeMenuKey } = useCanvasMenu()
  const { selectionOverbound } = useCheckObjectOverbound()

  const [tipClosed, setTipClosed] = useState(false)

  const show = selectionOverbound && !tipClosed && activeMenuKey !== EngravingMenuKeys.parameter

  useEffect(() => {
    setTipClosed(false)
  }, [selection?.__version__])

  return (
    <div
      style={{ top, zIndex: ZIndices.BannerHint }}
      className={clsx('absolute w-screen bg-[#FFECE8] px-4 py-2 transition-opacity', !show && 'opacity-0 h-0')}
    >
      <div className={clsx('flex items-center justify-between transition-opacity', !show && 'hidden')}>
        <div className="flex items-center gap-3">
          <ErrorIcon />
          <div className="text-[#F53F3F] text-sm">{t('overbound-hint')}</div>
        </div>
        <div className="flex items-center gap-2">
          <CloseIcon onClick={() => setTipClosed(true)} />
        </div>
      </div>
    </div>
  )
}

const AppPageHeader: FC = () => {
  const { t } = useTranslation()
  const { safe_area_top, recordDataJson } = useURLSearchConfig()
  const { connected, deviceCurrentState } = useDeviceStatusStore()
  const { editor, derivedState: { objectTypeContainingSituation } } = useFabric()

  const { prepareToEngrave, startToEngrave, isEngravingPrepared, goBackwardToDesign, activeMenuKey, setActiveMenuKey } = useCanvasMenu()

  const deviceFaultKey = useDeviceDeviceFaultKeyStore(state => state.deviceFaultKey)

  const handleBack = useCallback(() => {

    if (isDeviceWorking(deviceCurrentState)) {
      toast(t('hints.device-is-working'))
      return
    }

    if (activeMenuKey === EngravingMenuKeys.engrave || activeMenuKey === EngravingMenuKeys.preview) {
      return prepareToEngrave()
    }

    if (activeMenuKey === EngravingMenuKeys.parameter) {
      return goBackwardToDesign()
    }

    return sendMessageToNative(MessageAction.IF_SAVE, !objectTypeContainingSituation)
  }, [editor, activeMenuKey, deviceCurrentState, isEngravingPrepared, objectTypeContainingSituation])

  const pageTitle = useMemo(() => {
    if (activeMenuKey === EngravingMenuKeys.engrave) {
      if (deviceCurrentState === DeviceWorkingStatus.FINISHED) return t('engraving-completed')

      if (deviceCurrentState !== DeviceWorkingStatus.IDLE) {
        return t('engraving')
      }

      return t('prepare-to-engrave')
    }

    return t('creative')
  }, [deviceCurrentState, isEngravingPrepared, activeMenuKey])

  const height = PageHeaderHeight + Number(safe_area_top)

  const handleNextStep = useCallback(() => {
    if (!objectTypeContainingSituation) {
      return
    }

    if (activeMenuKey === PublicMenuKeys.previewCard) {
      toast(t('hints.stop-preview-first'))
      return
    }

    if (connected || import.meta.env.DEV && import.meta.env.VITE_IGNORE_DEVICE_CONNECTION) {
      if (deviceFaultKey.length !== 0) {
        sendMessageToNative(MessageAction.DEVICE_IS_FAULT)
        return
      }
      editor.canvas.discardActiveObject()
      return prepareToEngrave()
    }

    return sendMessageToNative(MessageAction.DEVICE_NOT_CONNECT_WARNING)
  }, [objectTypeContainingSituation, connected, activeMenuKey, deviceFaultKey])

  const handlePreview = useCallback(() => {
    if (import.meta.env.DEV) {
      void editor.exportLayers().then(console.log)
    }

    if (!objectTypeContainingSituation) {
      return
    }

    if (connected || import.meta.env.DEV && import.meta.env.VITE_IGNORE_DEVICE_CONNECTION) {
      if (deviceFaultKey.length !== 0) {
        sendMessageToNative(MessageAction.DEVICE_IS_FAULT)
        return
      }

      if (isDeviceWorking(deviceCurrentState)) {
        return
      }

      setTimeout(() => {
        setActiveMenuKey(PublicMenuKeys.previewCard)
      }, 50)
      return editor.canvas.requestRenderAll()
    }

    return sendMessageToNative(MessageAction.DEVICE_NOT_CONNECT_WARNING)
  }, [objectTypeContainingSituation, connected, deviceFaultKey, deviceCurrentState])

  const handleSetting = useCallback(() => {
    if (isDeviceWorking(deviceCurrentState)) {
      toast(t('hints.device-is-working'))
      return
    }

    return sendMessageToNative(MessageAction.OPEN_DEVICE_CONFIG)
  }, [deviceCurrentState, isEngravingPrepared, deviceFaultKey])

  // 处理 APP 上的 "再次雕刻" 入口
  useEffect(() => {
    if (!recordDataJson) {
      return
    }

    // 判断是否有元素
    if (!objectTypeContainingSituation) {
      return
    }

    // 判断是否连接了设备
    if (connected) {
      editor.canvas.discardActiveObject()
      startToEngrave()
    }
  }, [recordDataJson, objectTypeContainingSituation, connected])

  return (
    <div style={{ paddingTop: `${safe_area_top}px`, height: `${height}px` }} className="bg-white relative">
      <div className="flex w-full justify-between items-center px-4 py-3" style={{ zIndex: ZIndices.Loading }}>
        <div className="flex-1 flex">
          <BackIcon onClick={handleBack} />
        </div>
        <div className="text-base font-bold">{pageTitle}</div>

        {!isEngravingPrepared ? (
          <div className="flex flex-1 justify-end items-center gap-4">
            <div
              className={cn(
                'text-primary font-semibold whitespace-nowrap text-nowrap',
                {
                  'text-gray-500': !objectTypeContainingSituation
                    || (!connected && !(import.meta.env.DEV && import.meta.env.VITE_IGNORE_DEVICE_CONNECTION))
                    || activeMenuKey === PublicMenuKeys.previewCard
                    || isDeviceWorking(deviceCurrentState)
                }
              )}
              onClick={activeMenuKey !== PublicMenuKeys.previewCard ? handlePreview : undefined}
            >
              {t('preview')}
            </div>

            <div
              className={clsx('text-primary font-semibold whitespace-nowrap text-nowrap', !objectTypeContainingSituation && 'grayscale')}
              onClick={handleNextStep}
            >
              {t('next-step')}
            </div>
          </div>
        ) : (activeMenuKey === EngravingMenuKeys.engrave ? (
          <div className="flex-1 flex justify-end">
            <SetIcon style={{ color: isDeviceWorking(deviceCurrentState) ? '#999' : '#333' }}  onClick={handleSetting} />
          </div>
        ) : <div className="flex-1 h-1" />)}

      </div>

      <LayerOverboundHint top={height} />
    </div>
  )
}

const AppCore: FC = () => {
  useCanvasAutoSaver()
  useNativeMessageListener()
  useAutoLockActiveSelection()

  return (
    <Fragment>
      <div
        className={clsx(
          'absolute bottom-0 w-full flex-col flex justify-end items-center',
        )}
      >
        <CanvasMenu />
      </div>

      <CanvasCacheChecker />
    </Fragment>
  )
}

function App() {
  const { safe_area_top } = useURLSearchConfig()

  useEffect(() => {

    if (window.ReactNativeWebView?.injectedObjectJson?.()) {
      const json = window.ReactNativeWebView.injectedObjectJson()
      window.injected = JSON.parse(json).injected

      useDeviceStatusStore.setState({
        connected: Boolean(window.injected?.device?.model_id),
        deviceModelId: window.injected?.device?.model_id?.toString()
      })

    }
  }, [])

  return (
    <div className="w-full h-screen flex flex-col relative">
      <QueryClientProvider client={queryClient}>
        <FontManagerProvider>
          <LoadingProvider>
            <FabricProvider>
              {initialize => (
                <Fragment>
                  <AppPageHeader />
                  <FabricCanvas
                    className="sample-canvas w-screen h-[calc(100vh-48px)]"
                    onReady={context => initialize(context)}
                    canvasOption={{
                      fireRightClick: true,
                      stopContextMenu: true,
                      controlsAboveOverlay: true,
                      preserveObjectStacking: true,
                    }}
                    absoluteSlot={
                      <div className="absolute top-0 right-0" style={{ zIndex: ZIndices.LayersPanel }}>
                        <FadeInContainer menuKey={GeneralMenuKeys.layers}>
                          <GeneralOperations.Layers />
                        </FadeInContainer>
                      </div>
                    }
                  />
                  <AppCore />
                </Fragment>
              )}
            </FabricProvider>
          </LoadingProvider>
        </FontManagerProvider>
      </QueryClientProvider>

      <Toaster
        containerStyle={{
          marginTop: `${Number(safe_area_top) + 24}px`,
        }}
        toastOptions={{
          position: 'top-center',
          duration: 1500,
          style: {
            background: '#FEF4E6',
            color: '#F18D00',
            fontWeight: 600,
            borderRadius: 4,
            fontSize: 12,
          },

          success: {
            style: {
              background: '#e8f9f5',
              color: '#333',
              fontWeight: 600,
            }
          }
        }}
      />
    </div>
  )
}

export default App
