import { FC, useMemo, useState } from 'react'
import { ButtonRadio, ButtonRadioItem } from '@/components/ui/ButtonRadio'
import clsx from 'clsx'
import useQueryCategory from '@/hooks/queries/useQueryCategory'
import { PostViewData, SocialTypeMap, SortTypeFilterKey } from '@/datasource/api/social/types'
import { useTranslation } from 'react-i18next'
import { useInfiniteQueryResources } from '@/hooks/queries/useInfiniteQueryResources'
import UseIcon from '@/assets/icons/use.svg?react'
import { useFabric } from '@/contexts/fabric.context'
import InfiniteScrollWithReactQuery from '@/components/InfiniteScrollComponent'
import Masonry from 'react-responsive-masonry'

const FontItemBox: FC<PostViewData & { value?: PostViewData, onClick: (data: PostViewData) => void }> = ({
  value,
  onClick,
  ...props
}) => {
  const { t } = useTranslation()
  const { editor } = useFabric()

  const handleUse = () => editor.addText(props.content)

  return (
    <div
      onClick={() => onClick(props)}
      className={clsx('w-full border p-3 bg-white rounded shadow-md', value?.id === props.id ? 'border-primary' : 'border-transparent')}
    >
      <div className="mb-3 text-left font-medium text-base leading-[22px]">{props.content}</div>

      <div className="flex justify-end">
        <div className="rounded-full border py-1.5 pl-2 pr-3 border-primary flex items-center gap-1" onClick={handleUse}>
          <UseIcon />
          <span className="text-primary text-xs">{t('common.use')}</span>
        </div>
      </div>
    </div>
  )
}

export const ContentList: FC<{ categoryId?: number | string, onChange: (val: string | null) => void }> = ({ categoryId, onChange }) => {
  const { data: childCategory } = useQueryCategory({ parentId: categoryId })
  const childCategoryIds: number[] = useMemo(() => childCategory?.map(i => i.id) || [], [childCategory])
  const [selectedContent, setSelectedContent] = useState<PostViewData>()

  const { data: cloudData, hasNextPage, fetchNextPage, isFetching, isLoading } = useInfiniteQueryResources({
    sortType: SortTypeFilterKey.new,
    typeIds: [SocialTypeMap.content.typeId],
    categoryIds: categoryId ? [categoryId, ...childCategoryIds] : undefined,
  })

  return (
    <div className="p-4">
      <InfiniteScrollWithReactQuery fetchNextPage={fetchNextPage} hasNextPage={hasNextPage} isFetchingNextPage={isFetching}>
        {!isLoading && (
          <Masonry columnsCount={2} gutter={16}>
            {cloudData.map((i, index) => (
              <FontItemBox
                key={index}
                {...i}
                value={selectedContent}
                onClick={data => {
                  setSelectedContent(data)
                  onChange(data.content)
                }}
              />
            ))}
          </Masonry>
        )}
      </InfiniteScrollWithReactQuery>
    </div>
  )
}

export const TextTemplateSelector: FC<{ onChange: (val: string | null) => void }> = ({ onChange }) => {
  const { t } = useTranslation()
  const { data: categories } = useQueryCategory({ parentId: SocialTypeMap.content.typeId })
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | number | undefined>(undefined)

  const sortedCategories = Array.isArray(categories) ? [...categories].sort((a, b) => a.sort - b.sort) : []
  return (
    <div>
      <div className="px-6">
        <ButtonRadio className="gap-2" value={selectedCategoryId} onChange={val => setSelectedCategoryId(val ?? undefined)}>
          <ButtonRadioItem value={null}>{t('common.all')}</ButtonRadioItem>
          {sortedCategories?.map(i => (
            <ButtonRadioItem key={i.id} value={i.id}>
              {i?.name}
            </ButtonRadioItem>
          ))}
        </ButtonRadio>
      </div>

      <ContentList categoryId={selectedCategoryId} onChange={val => onChange(val)} />
    </div>
  )
}
