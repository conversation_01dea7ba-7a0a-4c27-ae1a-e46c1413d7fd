import { useCallback, useEffect, useState } from 'react'
import { Dialog, DialogActions, DialogTitle } from '@mui/material'
import { useFabric } from '@/contexts/fabric.context'
import { Button } from './ui/button'

export const CanvasCacheChecker = () => {
  const [open, setOpen] = useState(false)
  const { state, editor } = useFabric()

  const handleClose = () => {
    setOpen(false)
  }

  const handleRecoverCanvas = useCallback(() => {
    editor.restoreCanvasFromCache()
    setOpen(false)
  }, [editor])

  useEffect(() => {
    if (state.canvasCache) {
      handleRecoverCanvas()
      // setOpen(true)
    }
  }, [state.canvasCache])

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        检测到有暂存的画布内容，是否恢复？
      </DialogTitle>
      <DialogActions>
        <Button onClick={handleClose}>否</Button>
        <Button onClick={handleRecoverCanvas}>是</Button>
      </DialogActions>
    </Dialog>
  )
}
