import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Toggle<PERSON><PERSON><PERSON>,
  ToggleButtonGroup as _ToggleButtonGroup
} from '@mui/material'
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { RowFormControl } from '@/components/ui/form'
import { ContainerHeader } from '@/components/menu-content-container'
import { useFabric } from '@/contexts/fabric.context'
import { ObjectType, VECTOR_FILL_MODES, WORKSPACE_SIZE_PIXELS } from '@/libs/fabricjs-react/constants'
import styled from '@emotion/styled'
import { FontStyleProperties } from '@/libs/fabricjs-react/plugins/text.plugin'
import { pull, throttle, toPairs } from 'lodash'
import { useTranslation } from 'react-i18next'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'

import FontBoldIcon from '@/assets/icons/font_bold.svg?react'
import FontItalicIcon from '@/assets/icons/font_italic.svg?react'
import FontFillIcon from '@/assets/icons/font_fill.svg?react'
import FontStrokeIcon from '@/assets/icons/font_stroke.svg?react'
import ArrowRightIcon from '@/assets/icons/arrow_right.svg?react'
import Picker from 'react-mobile-picker'
import { wrapWithSvgRoot } from '@/libs/fabricjs-react/utils'
import { FontConfig, useFontManager } from '@/contexts/font-manager.context'
import { useQuery } from '@tanstack/react-query'
import { QueryKeys } from '@/constants/query-keys'
import localforage from 'localforage'
import clsx from 'clsx'
import toast from 'react-hot-toast'
import { Slider } from '@/components/ui/slider'
import { Textarea } from '@/components/ui/textarea'
import { Loading } from '@/components/ui/Loading'

async function checkFontAvailable(option: FontConfig): Promise<boolean> {
  if (option.localUri || option.isCustom) return true

  return localforage
    .getItem(option.fontFamily)
    .then(r => r !== null)
}

const ToggleButtonGroup = styled(_ToggleButtonGroup)({
  '.MuiToggleButton-root': {
    padding: 4
  }
})

const FontStyles = () => {
  const { selection, editor } = useFabric()

  const toggleGroupValues = useMemo(() => {
    if (selection?.type !== ObjectType.TEXT) {
      return []
    }

    const { object: { fontStyle, fontWeight, underline, linethrough, fill: _fill } } = selection

    const italic = fontStyle === 'italic'
    const bold = fontWeight === 'bold'
    const fill = _fill !== VECTOR_FILL_MODES.NO_FILL
    const stroke = !fill

    const dict = { underline, italic, bold, linethrough, fill, stroke  }

    return toPairs(dict)
      .filter(([_, value]) => !!value)
      .map(([key, _]) => key)
  }, [selection])

  const handleStyleChange = useCallback((val: string[]) => {
    if (val.includes('fill') && val.includes('stroke')) {
      editor.setFontStyles(pull(val, ...toggleGroupValues.filter(o => o === 'fill' || o === 'stroke')) as FontStyleProperties[])
    } else {
      editor.setFontStyles(val as FontStyleProperties[])
    }
  }, [toggleGroupValues, editor])

  return (
    <ToggleButtonGroup
      value={toggleGroupValues}
      onChange={(_, val) => handleStyleChange(val)}
      aria-label="text formatting"
    >
      <ToggleButton value="stroke">
        <FontStrokeIcon />
      </ToggleButton>
      <ToggleButton value="fill">
        <FontFillIcon />
      </ToggleButton>
      <ToggleButton value="bold" aria-label="bold">
        <FontBoldIcon />
      </ToggleButton>
      <ToggleButton value="italic" aria-label="italic">
        <FontItalicIcon />
      </ToggleButton>
      {/*<ToggleButton value="underline" aria-label="underlined">*/}
      {/*  <FontUnderlineIcon />*/}
      {/*</ToggleButton>*/}
      {/*<ToggleButton value="linethrough" aria-label="underlined">*/}
      {/*  <FontLinethroughIcon />*/}
      {/*</ToggleButton>*/}
    </ToggleButtonGroup>
  )
}

const FontSize: FC<{ fontSize?: number }> = ({ fontSize }) => {
  const { editor } = useFabric()

  const handleFontSizeChangeCommit = useCallback(throttle(
    (val: number) => editor.setFontSize(val, true),
    200, { trailing: false }
  ), [])

  return (
    <div className="flex items-center gap-4">
      <Slider
        className="w-[60vw]"
        min={8}
        max={256}
        value={[fontSize || 0]}
        onValueChange={([val]) => editor.setFontSize(val)}
        onValueCommit={([val]) => handleFontSizeChangeCommit(val)}
      />
      <div className="text-secondary text-xs min-w-10">{fontSize} pt.</div>
    </div>
  )
}

const FontPickerItem: FC<FontConfig> = option => {
  const { data: available } = useQuery({
    queryKey: [QueryKeys.REMOTE_FONT_AVAILABLE, option.fontFamily],
    queryFn: async () => {
      return checkFontAvailable(option)
    },
    refetchInterval: query => {
      return query.state.data ? false : 2000
    }
  })

  return (
    <Picker.Item disabled={!available} value={option.fontFamily} key={option.fontFamily}>
      {selected => (
        <div
          style={{
            fontFamily: available ? option.fontFamily : undefined,
            opacity: selected ? 1 : 0.1,
            height: 36
          }}
        >
          <div className={clsx('flex items-center gap-1', !available && 'text-gray-500')}>
            {option.label}
            {!available && <Loading className="size-4 border-2" />}
          </div>
        </div>
      )}
    </Picker.Item>
  )
}

export default function Font() {
  const { t } = useTranslation()
  const { selection, editor } = useFabric()
  const { fonts, removeFont } = useFontManager()

  const [inputValue, setInputValue] = useState<string>('')
  const [fontFamilyDrawerVisible, setFontFamilyDrawerVisible] = useState(false)

  const { fontSize, text, fontFamily } = useMemo(() => {
    if (selection?.type !== ObjectType.TEXT) return {}

    const { fontSize, text, fontFamily } = selection.object

    return {
      fontSize: editor.pixelToPoint(fontSize),
      text,
      fontFamily
    }
  }, [selection])

  useEffect(() => {
    setInputValue(text || '')
  }, [text])

  return (
    <div className="flex flex-col fadein-popup-handler-content">
      <ContainerHeader layoutMode={2}>
        {t('common.text')}
      </ContainerHeader>

      <div className="flex flex-col px-6 pb-3 gap-3">
        <RowFormControl>
          <FormLabel>{t('text.font-family')}</FormLabel>
          <div className="flex items-center gap-3">
            <div style={{ fontFamily }}
              className="text-gray-400 text-xs"
              onClick={() => setFontFamilyDrawerVisible(true)}
            >
              {fonts.find(option => option.fontFamily === fontFamily)?.label || fontFamily}
            </div>
            <ArrowRightIcon className="size-3" />
          </div>
        </RowFormControl>

        <RowFormControl>
          <FormLabel>{t('text.style')}</FormLabel>

          <div className="flex">
            <button
              className="opacity-0"
              onClick={() => sendMessageToNative(
                MessageAction.__DEV__EXPORT_CURRENT_LAYER_AS_SVG,
                wrapWithSvgRoot(editor.canvas.getActiveObject()!.toSVG(), WORKSPACE_SIZE_PIXELS, WORKSPACE_SIZE_PIXELS)
              )}
            >
              download
            </button>
            <FontStyles />
          </div>
        </RowFormControl>

        <RowFormControl>
          <FormLabel>{t('text.size')}</FormLabel>
          <FontSize fontSize={fontSize} />
        </RowFormControl>

        <RowFormControl>
          <FormLabel>{t('text.content')}</FormLabel>
          <Textarea
            className="flex-1"
            value={inputValue}
            rows={3}
            placeholder={t('text.input-placeholder')}
            onChange={event => {
              setInputValue(event.target.value)
              // editor.setTextContent(event.target.value)
            }}
            onBlur={() => editor.setTextContent(inputValue, true)}
          />
        </RowFormControl>

        {import.meta.env.DEV && (
          <button
            onPointerDown={() => editor.canvas.getActiveObject()?.set({ preview: true }) && editor.canvas.requestRenderAll()}
            onPointerUp={() => editor.canvas.getActiveObject()?.set({ preview: false }) && editor.canvas.requestRenderAll()}
          >
            PREVIEW
          </button>
        )}
      </div>

      <Drawer
        anchor="bottom"
        open={fontFamilyDrawerVisible}
        onClose={() => {
          if (fontFamily) editor.setFontFamily(fontFamily, true)
          setFontFamilyDrawerVisible(false)
        }}
      >
        <ContainerHeader
          SubmitElement={
            <div className="flex gap-3 justify-end">
              {import.meta.env.DEV && fontFamily && (
                <div className="text-gray-500" onClick={() => removeFont(fontFamily)}>
                  {t('common.delete')}
                </div>
              )}
              <div
                className="text-primary"
                onClick={() => sendMessageToNative(MessageAction.OPEN_DEVICE_FILE_PICKER, { type: 'font' })}
              >
                {t('common.import')}
              </div>
            </div>
          }
          onSubmit={() => void 0}
        >
          {t('text.font-family')}
        </ContainerHeader>

        <Picker
          value={{ fontFamily: fontFamily || '' }}
          itemHeight={36}
          onChange={async value => {
            const targetFont = fonts.find(o => o.fontFamily === value.fontFamily)
            if (!targetFont) {
              return toast.error(t('hints.font-not-exist'))
            }

            if (!(await checkFontAvailable(targetFont))) {
              return toast.error(t('hints.font-still-loading'))
            }

            editor.setFontFamily(value.fontFamily)
          }}
        >
          <Picker.Column name="fontFamily">
            {fonts.map(option => (
              <FontPickerItem {...option} key={option.fontFamily} />
            ))}
          </Picker.Column>
        </Picker>
      </Drawer>
    </div>
  )
}
