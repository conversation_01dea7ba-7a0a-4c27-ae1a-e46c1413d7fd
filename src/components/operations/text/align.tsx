import { useMemo } from 'react'

import align_left from '@/assets/icons/align_left.svg?react'
import align_center from '@/assets/icons/align_center.svg?react'
import align_right from '@/assets/icons/align_right.svg?react'
import { useFabric } from '@/contexts/fabric.context'
import { ObjectType } from '@/libs/fabricjs-react/constants'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'

export default function Align() {
  const { t } = useTranslation()
  const { selection, editor } = useFabric()

  const align = useMemo(() => {
    if (selection?.type !== ObjectType.TEXT) {
      return undefined
    }

    return selection.object.textAlign
  }, [selection])

  const options = [
    { label: t('text.align.left'), icon: align_left, value: 'left' },
    { label: t('text.align.center'), icon: align_center, value: 'center' },
    { label: t('text.align.right'), icon: align_right, value: 'right' },
  ]

  return (
    <div className="flex gap-6 p-3 fadein-popup-handler-content">
      {options.map(({ icon: Icon, ...option }) => (
        <div
          key={option.value}
          className="flex flex-col gap-3 items-center"
          onClick={() => editor.setTextAlign(option.value)}
        >
          <Icon className="size-6" fill={align === option.value ? '#f18d00' : '#333'} />
          <div className={clsx('text-xs', align === option.value ? 'text-primary' : 'text-secondary')}>
            {option.label}
          </div>
        </div>
      ))}
    </div>
  )
}
