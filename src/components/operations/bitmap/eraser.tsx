import React, { useEffect } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import { ContainerHeader } from '@/components/menu-content-container'
import { useTranslation } from 'react-i18next'
import { Slider } from '@/components/ui/slider'

export default function Eraser() {
  const { editor } = useFabric()
  const { t } = useTranslation()

  useEffect(() => {
    if (editor) {
      editor.toggleEraser(true)
    }

    return () => {
      editor.toggleEraser(false)
    }
  }, [editor])

  const min = Math.round(editor.state.workspaceWidth! / 100)

  return (
    <div className="flex flex-col fadein-popup-handler-content">
      <ContainerHeader layoutMode={2} onClose={() => editor.toggleEraser(false)}>
        {t('eraser')}
      </ContainerHeader>

      <div className="px-6 flex items-center !flex-row gap-5 pb-4">
        <div className="text-nowrap text-xs w-12 text-start text-secondary">{t('size')}</div>
        <Slider
          min={min}
          max={Math.round(editor.state.workspaceWidth! / 10)}
          defaultValue={[min]}
          onValueChange={([value]) => {
            editor.setBrushSize(value as number)
          }}
        />
      </div>
    </div>
  )
}
