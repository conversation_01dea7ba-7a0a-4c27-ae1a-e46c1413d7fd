import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import { ObjectType } from '@/libs/fabricjs-react/constants'
import { PercentCrop, ReactCrop } from 'react-image-crop'
import * as fabric from 'fabric'

import CloseIcon from '@/assets/icons/close_white_bg.svg?react'
import SubmitIcon from '@/assets/icons/submit_white_bg.svg?react'

import aspect_original from '@/assets/icons/aspect_original.svg?react'
import aspect_free from '@/assets/icons/aspect_free.svg?react'
import aspect_square from '@/assets/icons/aspect_square.svg?react'
import aspect_circle from '@/assets/icons/aspect_circle.svg?react'
import aspect_rect_3_2 from '@/assets/icons/aspect_rect_3_2.svg?react'
import aspect_rect_4_3 from '@/assets/icons/aspect_rect_4_3.svg?react'
import aspect_rect_16_9 from '@/assets/icons/aspect_rect_16_9.svg?react'

import aspect_original_active from '@/assets/icons/aspect_original_active.svg?react'
import aspect_free_active from '@/assets/icons/aspect_free_active.svg?react'
import aspect_square_active from '@/assets/icons/aspect_square_active.svg?react'
import aspect_circle_active from '@/assets/icons/aspect_circle_active.svg?react'
import aspect_rect_3_2_active from '@/assets/icons/aspect_rect_3_2_active.svg?react'
import aspect_rect_4_3_active from '@/assets/icons/aspect_rect_4_3_active.svg?react'
import aspect_rect_16_9_active from '@/assets/icons/aspect_rect_16_9_active.svg?react'


import 'react-image-crop/dist/ReactCrop.css'
import clsx from 'clsx'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { useTranslation } from 'react-i18next'

const useCropStates = (selectedImage?: fabric.FabricImage) => {
  const { t } = useTranslation()

  const [crop, setCrop] = useState<PercentCrop | undefined>(() => {
    if (!selectedImage) return undefined

    const {
      _originalElement: { width: originalWidth, height: originalHeight },
      cropX, cropY, width, height, flipX, flipY
    } = selectedImage

    let x = cropX / originalWidth * 100
    let y = cropY / originalHeight * 100
    const widthPercent = width / originalWidth * 100
    const heightPercent = height / originalHeight * 100

    if (flipX) x = 100 - x - widthPercent
    if (flipY) y = 100 - y - heightPercent

    return {
      unit: '%',
      x,
      y,
      width: widthPercent,
      height: heightPercent
    } satisfies PercentCrop
  })

  const [circularCrop, setCircularCrop] = useState(() => {
    if (!selectedImage) return false

    return selectedImage.clipPath instanceof fabric.Circle
  })

  const [activeAspectRatioIndex, setActiveAspectRatioIndex] = useState(0)

  const [aspect, _setAspect] = useState(() => {
    if (!selectedImage) return undefined

    return selectedImage.clipPath instanceof fabric.Circle ? 1 : undefined
  })

  const setAspect = useCallback((newAspect: number | undefined) => {
    if (!selectedImage) return

    _setAspect(newAspect)
    setCircularCrop(false)

    const { width: originW, height: originH } = selectedImage._originalElement
    let crop: PercentCrop

    if (!newAspect) {
      // 自由比例，默认全图
      crop = {
        unit: '%',
        x: 0,
        y: 0,
        width: 100,
        height: 100
      }
    } else {
      // 固定比例，最大化适配并居中
      const originRatio = originW / originH
      let cropWidth, cropHeight
      if (originRatio > newAspect) {
        cropHeight = 100
        cropWidth = (newAspect / originRatio) * 100
      } else {
        cropWidth = 100
        cropHeight = (originRatio / newAspect) * 100
      }
      crop = {
        unit: '%',
        width: cropWidth,
        height: cropHeight,
        x: (100 - cropWidth) / 2,
        y: (100 - cropHeight) / 2
      }
    }
    setCrop(crop)
    // 不做实际裁剪
  }, [selectedImage])

  const keepOriginAspect = useCallback(() => {
    if (selectedImage) {
      return setAspect(selectedImage._originalElement.width / selectedImage._originalElement.height)
    }
  }, [setAspect, selectedImage])

  const aspectRatioOptions = useMemo(
    () => ([
      {
        name: t('free'),
        icon: aspect_free,
        activeIcon: aspect_free_active,
        handler: () => setAspect(undefined),
      },
      {
        name: t('original-image-ratio'),
        icon: aspect_original,
        activeIcon: aspect_original_active,
        handler: keepOriginAspect
      },
      {
        name: t('circle'),
        icon: aspect_circle,
        activeIcon: aspect_circle_active,
        handler: () => {
          setAspect(1)
          setCircularCrop(true)
        },
      },
      {
        name: t('square'),
        icon: aspect_square,
        activeIcon: aspect_square_active,
        handler: () => setAspect(1),
      },
      {
        name: '3:2',
        icon: aspect_rect_3_2,
        activeIcon: aspect_rect_3_2_active,
        handler: () => setAspect(3 / 2),
      },
      {
        name: '4:3',
        icon: aspect_rect_4_3,
        activeIcon: aspect_rect_4_3_active,
        handler: () => setAspect(4 / 3),
      },
      {
        name: '16:9',
        icon: aspect_rect_16_9,
        activeIcon: aspect_rect_16_9_active,
        handler: () => setAspect(16 / 9),
      },
    ]),
    [aspect, setAspect, keepOriginAspect]
  )

  useEffect(() => {
    aspectRatioOptions[activeAspectRatioIndex].handler()
  }, [activeAspectRatioIndex, aspectRatioOptions])

  return {
    crop,
    aspect,
    circularCrop,
    aspectRatioOptions,
    activeAspectRatioIndex,
    setCrop,
    setActiveAspectRatioIndex
  }
}

export default function Crop() {
  const { inactivate } = useCanvasMenu()
  const { t } = useTranslation()
  const parentElRef = useRef<HTMLDivElement | null>(null)

  const containerElRef = useRef<HTMLDivElement>(null)

  const { selection, editor } = useFabric()
  const { safe_area_bottom, safe_area_top } = useURLSearchConfig()

  const selectedImage = useMemo(() => {
    if (selection?.type !== ObjectType.IMAGE) return undefined

    return selection.object
  }, [selection])

  const {
    circularCrop, crop, aspectRatioOptions, activeAspectRatioIndex, aspect,
    setCrop, setActiveAspectRatioIndex,
  } = useCropStates(selectedImage)

  const src = useMemo(() => {
    if (selection?.type !== ObjectType.IMAGE) return undefined

    return selection.object.getSrc()
  }, [selection])

  const [displaySize, setDisplaySize] = useState<{ displayWidth: number, displayHeight: number } | null>(null)

  // 使用 ResizeObserver 监听 container 容器尺寸变化
  useLayoutEffect(() => {
    const el = containerElRef.current
    if (!selectedImage || !el) return

    const observer = new ResizeObserver(() => {
      const { clientWidth, clientHeight } = el
      if (!clientWidth || !clientHeight) return

      const { _originalElement: { width: originWidth, height: originHeight } } = selectedImage

      const maxWidth = clientWidth - 64
      const maxHeight = clientHeight - 64

      const scaleX = maxWidth / originWidth
      const scaleY = maxHeight / originHeight

      const scale = Math.min(1, scaleX, scaleY)

      setDisplaySize({
        displayWidth: originWidth * scale,
        displayHeight: originHeight * scale,
      })
    })

    observer.observe(el)
    return () => observer.disconnect()
  }, [selectedImage])

  const handleSubmit = useCallback(() => {
    const FULL_CROP: PercentCrop = { unit: '%', width: 100, height: 100, x: 0, y: 0 }

    editor.cropImage(crop || FULL_CROP, false, false, circularCrop)

    inactivate()
  }, [crop, editor, circularCrop])

  return (
    <div
      className="h-screen flex flex-col bg-[#858585]"
      ref={parentElRef}
      style={{ paddingTop: `${safe_area_top || 0}px` }}
    >
      <div ref={containerElRef} className="flex flex-1 items-center justify-center p-8 overflow-y-hidden">
        {displaySize && src && (
          <ReactCrop
            className=""
            crop={crop}
            aspect={aspect}
            circularCrop={circularCrop}
            onChange={(_, v) => setCrop(v)}
            style={{
              width: displaySize.displayWidth,
              height: displaySize.displayHeight,
            }}
          >
            <img
              src={src}
              className="max-h-full aspect-auto object-contain"
              style={{
                width: displaySize.displayWidth,
                height: displaySize.displayHeight,
                transition: 'transform .18s',
              }}
            />
          </ReactCrop>
        )}
      </div>

      <div className="py-3 px-4 flex justify-start gap-8 rounded-t-lg overflow-x-auto bg-white scrollbar-hidden">
        {aspectRatioOptions
          .map(({ name, icon: Icon, activeIcon: ActiveIcon }, index) => (
            <div
              className={clsx('flex flex-col items-center gap-2')}
              key={name}
              onClick={() => setActiveAspectRatioIndex(index)}
            >
              {
                index === activeAspectRatioIndex
                  ? <ActiveIcon className="size-6" />
                  : <Icon className="size-6" />
              }
              <span
                className={clsx('text-xs text-nowrap whitespace-nowrap', index === activeAspectRatioIndex && 'text-primary')}
              >
                {name}
              </span>
            </div>
          ))}
      </div>

      <div>
        <div className="py-3 px-4 bg-[#F7F7F7] flex justify-between">
          <CloseIcon onClick={inactivate} />
          {/*<TabMenu*/}
          {/*  value={activeTab}*/}
          {/*  onChange={({ key }) => setActiveTab(key)}*/}
          {/*  items={[{ name: 'crop', key: 'crop' }, { name: 'flip', key: 'flip' }]}*/}
          {/*/>*/}
          <div>
            {t('common.crop')}
          </div>
          <SubmitIcon onClick={handleSubmit} />
        </div>
        <div style={{ height: `${safe_area_bottom}px` }} />

      </div>
    </div>
  )
}
