import { ContainerHeader } from '@/components/menu-content-container'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { BitmapMenuKeys, NameByMenuKey } from '@/components/CanvasMenu'
import CounterClockwiseIcon from '@/assets/icons/counterclockwise.svg?react'
import ClockwiseIcon from '@/assets/icons/clockwise.svg?react'
import clsx from 'clsx'
import { useFabric } from '@/contexts/fabric.context'

export default function Rotate() {
  const { t } = useTranslation()
  const { editor } = useFabric()

  const options = [
    {
      name: t('rotate.counterclockwise', { degree: '90°' }),
      icon: CounterClockwiseIcon,
      handler: () => editor.rotateWithDegreeDelta(-90)
    },
    {
      name: t('rotate.clockwise', { degree: '90°' }),
      icon: ClockwiseIcon,
      handler: () => editor.rotateWithDegreeDelta(+90)
    },
  ]

  return (
    <div className="flex flex-col fadein-popup-handler-content">
      <ContainerHeader layoutMode={2}>
        {t(NameByMenuKey[BitmapMenuKeys.rotate])}
      </ContainerHeader>

      <div className="pb-3 px-6 flex justify-start gap-6 rounded-t-lg overflow-x-auto bg-white">
        {options.map(({ name, icon: Icon, handler }) => (
          <div
            className={clsx('flex flex-col items-center gap-2')}
            key={name}
            onClick={handler}
          >
            <Icon className="size-5" />
            <span className={clsx('text-2xs text-nowrap opacity-90')}>
              {name}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}
