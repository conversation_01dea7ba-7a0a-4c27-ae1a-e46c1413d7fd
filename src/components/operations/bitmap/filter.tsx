import clsx from 'clsx'
import { FC, useMemo } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import { ObjectType } from '@/libs/fabricjs-react/constants'
import { ImageFilters } from '@/libs/fabricjs-react/types/filters'
import { ContainerHeader } from '@/components/menu-content-container'

import filter_original from '@/assets/images/filter_original.png'
import filter_blackwhite from '@/assets/images/filter_blackwhite.png'
import filter_halftone from '@/assets/images/filter_halftone.png'
import filter_painting from '@/assets/images/filter_painting.png'
import filter_sketch from '@/assets/images/filter_sketch.png'
import CheckedIcon from '@/assets/icons/checked.svg?react'
import { useTranslation } from 'react-i18next'

const FilterItem: FC<{ name: string, filterType: ImageFilters; image: string }> = ({
  name, filterType, image
}) => {
  const { editor, selection } = useFabric()

  const usingFilter = useMemo(() => {
    if (selection?.type !== ObjectType.IMAGE) return null

    return selection.object.__filter_type__ || ImageFilters.ORIGINAL
  }, [selection])

  const isActive = usingFilter === filterType

  return (
    <div
      onClick={() => editor.applyFilter(filterType)}
      className={clsx(
        'flex flex-col items-center gap-1.5 relative',
      )}
    >
      <img
        src={image}
        className={clsx('rounded h-18 w-16 border', isActive ? 'border-primary' : 'border-transparent')}
      />
      <div className="text-xs text-gray-500">{name}</div>

      <CheckedIcon
        className={clsx(
          'absolute right-1 top-1 size-3',
          !isActive && 'opacity-0'
        )}
      />
    </div>
  )
}

export default function Filter() {
  const { t } = useTranslation()
  const FILTERS: { type: ImageFilters, name: string, image: any }[] = [
    { type: ImageFilters.ORIGINAL, name: t('filters.grayscale'), image: filter_original },
    { type: ImageFilters.BLACK_WHITE, name: t('filters.black-white'), image: filter_blackwhite },
    // { type: ImageFilters.PIXELATE, name: t('pixelate'), image: filter_pixelate },
    { type: ImageFilters.HALFTONE, name: t('filters.halftone'), image: filter_halftone },
    { type: ImageFilters.PAINTING, name: t('filters.painting'), image: filter_painting },
    { type: ImageFilters.PENCIL_SKETCH, name: t('filters.sketch'), image: filter_sketch },
  ]

  return (
    <div className="fadein-popup-handler-content">
      <ContainerHeader layoutMode={2}>
        {t('filter')}
      </ContainerHeader>
      <div className="flex gap-2 w-full overflow-scroll px-6 pb-4">
        {
          FILTERS.map(filter => (
            <FilterItem
              name={filter.name}
              filterType={filter.type}
              key={filter.type}
              image={filter.image}
            />
          ))
        }
      </div>
    </div>
  )
}
