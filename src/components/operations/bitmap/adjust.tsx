import React, { useEffect, useState } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import { ContainerHeader } from '@/components/menu-content-container'
import { ObjectType } from '@/libs/fabricjs-react/constants'
import * as fabric from 'fabric'
import { useTranslation } from 'react-i18next'
import { FabricSelection } from '@/libs/fabricjs-react'
import { BitmapMenuKeys, NameByMenuKey } from '@/components/CanvasMenu'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'

export default function Adjust() {
  const { editor, selection } = useFabric()
  const { t } = useTranslation()

  const getValueFromSelection = (selection?: FabricSelection) => {
    if (!selection || selection.type !== ObjectType.IMAGE) {
      return {
        brightness: 0,
        contrast: 0,
        invert: false
      }
    }

    const filters = selection.object.filters

    return filters.reduce(
      (result, filter) => {
        if (filter instanceof fabric.filters.Brightness) {
          result.brightness = (filter as fabric.filters.Brightness).brightness
        }
        if (filter instanceof fabric.filters.Contrast) {
          result.contrast = (filter as fabric.filters.Contrast).contrast
        }
        if (filter instanceof fabric.filters.Invert) {
          result.invert = (filter as fabric.filters.Invert).invert
        }

        return result
      },
      {
        brightness: 0,
        contrast: 0,
        invert: false
      })
  }

  const [{ brightness, contrast, invert }, setState] = useState(getValueFromSelection(selection))

  useEffect(() => {
    setState(getValueFromSelection(selection))
  }, [selection])

  return (
    <div className="flex flex-col fadein-popup-handler-content">
      <ContainerHeader layoutMode={2}>
        {t(NameByMenuKey[BitmapMenuKeys.adjust])}
      </ContainerHeader>

      <div className="px-6 grid grid-rows-3 pb-4 gap-3">
        <div className="flex items-center !flex-row gap-5">
          <div className="text-nowrap text-xs w-12 text-start">{t('brightness')}</div>
          <Slider
            min={-1.0}
            max={+1.0}
            step={0.01}
            value={[brightness]}
            onValueChange={([v]) => {
              setState(prev => ({ ...prev, brightness: v }))
              editor.setBrightness(v)
            }}
            onValueCommit={([value]) => editor.setBrightness(value, true)}
          />
          <div className="text-secondary text-xs min-w-10">{(brightness * 100).toFixed()}</div>
        </div>

        <div className="flex items-center !flex-row gap-5">
          <div className="text-nowrap text-xs w-12 text-start">{t('contrast')}</div>
          <Slider
            min={-1.0}
            max={+1.0}
            step={0.01}
            value={[contrast]}
            onValueChange={([v]) => {
              setState(prev => ({ ...prev, contrast: v }))
              editor.setContrast(v as number)
            }}
            onValueCommit={([value]) => editor.setContrast(value, true)}
          />
          <div className="text-secondary text-xs min-w-10">{(contrast * 100).toFixed()}</div>
        </div>

        <div className="flex items-center justify-between !flex-row gap-5">
          <div className="text-nowrap text-xs w-12 text-start">{t('invert')}</div>
          <Switch
            checked={invert}
            onCheckedChange={() => editor.toggleInvert()}
          />
        </div>
      </div>
    </div>
  )
}
