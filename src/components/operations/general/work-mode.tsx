import { FormLabel } from '@mui/material'
import { RowFormControl } from '@/components/ui/form'
import { useFabric } from '@/contexts/fabric.context'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { useTranslation } from 'react-i18next'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'
import { LengthUnit, MessageAction, MessagePayload, WorkMode } from '@/libs/shared/types'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { sendMessageToNative } from '@/utils/message'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { NumberInput } from '@/components/ui/number-input'
import { throttle } from 'lodash'

const mmToInch = (mm: number) => parseFloat((mm * 0.0393701).toFixed(1))

const getStep = (unit: LengthUnit) => (unit === LengthUnit.mm ? 1 : 0.1)

const formatForDisplay = (num: number) => {
  return Number.isInteger(num) ? num : Math.round(num * 10) / 10
}

const getRange = (unit: LengthUnit, minMM: number, maxMM: number) => {
  return unit === LengthUnit.mm
    ? { min: minMM, max: maxMM }
    : { min: mmToInch(minMM), max: mmToInch(maxMM) }
}

export default function CanvasConfig() {
  const { editor } = useFabric()
  const { inactivate } = useCanvasMenu()
  const { t, i18n } = useTranslation()
  const { workMode, unit, diameter, length, setFormItem } = useEngravingParametersStore()
  const [localLength, setLocalLength] = useState(length)
  const [localDiameter, setLocalDiameter] = useState(diameter)

  const diameterRange = useMemo(() => getRange(unit, 8, 90), [unit])
  const lengthRange = useMemo(() => getRange(unit, 10, 600), [unit])

  const handleWorkModeChange = useCallback((workMode: WorkMode) => {
    setFormItem('workMode', workMode)
    if (workMode === WorkMode.TABLET) {
      editor.canvas.fire('workspace:length:changed', { workMode, length })
    } else {
      editor.canvas.fire('workspace:changed', { workMode, diameter })
    }
    sendMessageToNative(MessageAction.UPDATE_DEVICE_NATIVE_CONFIG, { workMode: workMode })
  }, [diameter, length])

  const throttledUpdateDeviceNativeConfig = useCallback(
    throttle(
      (payload: MessagePayload<MessageAction.UPDATE_DEVICE_NATIVE_CONFIG>) => {
        sendMessageToNative(MessageAction.UPDATE_DEVICE_NATIVE_CONFIG, payload)
      },
      1000,
      { trailing: true, leading: true }
    ),
    [workMode]
  )

  const handleLengthChanged = useCallback(
    (value: number | null) => {
      if (value === null) return
      const length = formatForDisplay(value)
      setLocalLength(length) // 先更新本地显示
      setFormItem('length', length)
      editor.canvas.fire('workspace:length:changed', { workMode, length })
      throttledUpdateDeviceNativeConfig({ length })
    },
    [throttledUpdateDeviceNativeConfig]
  )

  const handleDiameterChanged = useCallback(
    (value: number | null) => {
      if (value === null) return
      const diameter  = formatForDisplay(value)
      setLocalDiameter(diameter) // 先更新本地显示
      setFormItem('diameter', diameter)
      editor.canvas.fire('workspace:changed', { workMode, diameter })
      throttledUpdateDeviceNativeConfig({ diameter })
    },
    [throttledUpdateDeviceNativeConfig]
  )

  useEffect(() => {
    setLocalLength(length)
  }, [length])

  useEffect(() => {
    setLocalDiameter(diameter)
  }, [diameter])

  useEffect(() => {
    const canvas = editor?.canvas
    if (!canvas) return

    const handleMouseDown = () => {
      const activeObject = canvas.getActiveObject()
      const activeGroup = canvas.getActiveObjects?.()?.length > 1
      if (!activeObject && !activeGroup) {
        inactivate()
      }
    }

    canvas.on('mouse:down', handleMouseDown)

    return () => {
      canvas.off('mouse:down', handleMouseDown)
    }
  }, [editor, inactivate])

  const RadioItem = ({ value, label }: { value: WorkMode, label: string }) => {
    const id = `workmode-${value}`
    return (
      <div className="flex items-center gap-1 flex-1  whitespace-nowrap text-center">
        <RadioGroupItem className="size-3" value={value} id={id} />
        <Label htmlFor={id} className="text-sm">{label}</Label>
      </div>
    )
  }

  return (
    <div className="flex flex-col fadein-popup-handler-content">
      <div className="flex flex-col max-xs:px-3 px-6 gap-3 pb-3 pt-3">
        <RowFormControl className="flex w-full items-start">
          <FormLabel
            className={`text-sm text-start max-w-[120px] break-all  ${
              i18n.language === 'zh-CN' ? 'inline-block' : 'hidden'
            }`}
          >
            {t('processing-mode')}
          </FormLabel>

          <RadioGroup
            value={workMode}
            onValueChange={v => handleWorkModeChange(v as WorkMode)}
            className="overflow-x-auto flex flex-1"
          >
            <div className="flex gap-4 pb-2 min-w-max ml-auto whitespace-nowrap">
              <RadioItem value={WorkMode.PLANE} label={t('plane')} />
              <RadioItem value={WorkMode.ROTATION} label={t('rotation')} />
              <RadioItem value={WorkMode.TABLET} label={t('tablet')} />
              <RadioItem value={WorkMode.HANDHELD} label={t('handheld')} />
            </div>
          </RadioGroup>
        </RowFormControl>

        {workMode === WorkMode.ROTATION && (
          <RowFormControl>
            <FormLabel className="text-nowrap">{t('product-diameter')}</FormLabel>
            <div className="flex gap-3 items-center">
              <NumberInput
                emitChangeOnEnterKey
                min={diameterRange.min}
                max={diameterRange.max}
                step={getStep(unit)}
                value={formatForDisplay(localDiameter)}
                onValueChange={v => handleDiameterChanged(v)}
              />
              <span className="text-sm text-secondary">{unit}</span>
            </div>
          </RowFormControl>
        )}

        {workMode === WorkMode.TABLET && (
          <RowFormControl>
            <FormLabel className="text-nowrap">{t('product-length')}</FormLabel>
            <div className="flex gap-3 items-center">
              <NumberInput
                emitChangeOnEnterKey
                min={lengthRange.min}
                max={lengthRange.max}
                step={getStep(unit)}
                value={formatForDisplay(localLength)}
                onValueChange={v => handleLengthChanged(v)}
              />
              <span className="text-sm text-secondary">{unit}</span>
            </div>
          </RowFormControl>
        )}

      </div>
    </div>
  )
}
