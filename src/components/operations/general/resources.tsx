import { ButtonRadio, ButtonRadioItem } from '@/components/ui/ButtonRadio'
import { ContainerHeader } from '@/components/menu-content-container'
import { useTranslation } from 'react-i18next'
import { useInfiniteQueryResources } from '@/hooks/queries/useInfiniteQueryResources'
import {
  MediaType,
  PostTopType,
  PostViewData,
  SocialEnums,
  SocialTypeMap,
  SortTypeFilterKey
} from '@/datasource/api/social/types'
import { FC, useMemo, useState } from 'react'
import useQueryCategory from '@/hooks/queries/useQueryCategory'
import { useFabric } from '@/contexts/fabric.context'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { useGlobalLoading } from '@/contexts/loading'
import toast from 'react-hot-toast'
import { readBlobAsDataUrl } from '@/utils/string'
import InfiniteScrollWithReactQuery from '@/components/InfiniteScrollComponent'

type ResourcesProps = {
  resourceType: SocialEnums
}

type ResourcesListProps = {
  categoryId: number | string | null,
  resourceType: SocialEnums
}

const MaterialItem: FC<PostViewData> = ({ headImage, title }) => {
  const { t } = useTranslation()
  const { editor } = useFabric()
  const { setLoading } = useGlobalLoading()
  const { inactivate } = useCanvasMenu()

  const handleImport = async () => {
    if (!headImage) return

    setLoading(true, t('hints.generating'))
    const { type, url } = headImage

    try {
      const response = await fetch(url)

      if (type === MediaType.vector) {
        await response
          .text()
          .then(text => editor.addVectorFromString(text))
      } else if (type === MediaType.photo) {
        await response
          .blob()
          .then(blob => readBlobAsDataUrl(blob))
          .then(dataUrl => editor.addBitmapFromUrl(dataUrl))
      }

      inactivate()
    } catch (e) {
      toast(t('hints.failed-to-load-resource'))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div
      className="flex-1 aspect-square bg-gray-100 flex flex-col align-center justify-center rounded-sm"
      onClick={handleImport}
    >
      <img style={{ objectFit: 'cover', width: '100%', height: '100%' }} src={headImage?.url} alt={title} />
    </div>
  )
}

export const ResourcesList: FC<ResourcesListProps> = ({ categoryId, resourceType }) => {
  const { data: childCategory } = useQueryCategory({ parentId: categoryId ?? undefined })
  const childCategoryIds: number[] = useMemo(() => childCategory?.map(i => i.id) || [], [childCategory])

  const { data: cloudData, hasNextPage, fetchNextPage, isFetching,  } = useInfiniteQueryResources({
    sortType: SortTypeFilterKey.new,
    typeIds: [SocialTypeMap[resourceType].typeId],
    categoryIds: categoryId ? [categoryId, ...childCategoryIds] : undefined,
    top: !categoryId ? PostTopType.suggest : undefined
  })

  return (
    <div className="h-[300px] w-full overflow-scroll mt-4">
      <InfiniteScrollWithReactQuery fetchNextPage={fetchNextPage} hasNextPage={hasNextPage} isFetchingNextPage={isFetching}>
        <div className="grid grid-cols-4 gap-2 w-full px-4 pb-3">
          {cloudData.map((i, index) => (
            <MaterialItem key={index} {...i} />
          ))}
        </div>
      </InfiniteScrollWithReactQuery>
    </div>
  )
}

export default function Resources({ resourceType }: ResourcesProps) {
  const { t } = useTranslation()
  const { data: categories } = useQueryCategory({ parentId: SocialTypeMap[resourceType].typeId })
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | number | null>(null)
  const sortedCategories = Array.isArray(categories) ? [...categories].sort((a, b) => a.sort - b.sort) : []

  return (
    <div className="fadein-popup-handler-content">
      <ContainerHeader>
        {t(`${resourceType}-material`)}
      </ContainerHeader>

      <div className="px-6 ">
        <ButtonRadio value={selectedCategoryId} onChange={val => setSelectedCategoryId(val)}>
          <ButtonRadioItem value={null}>
            {t('suggest')}
          </ButtonRadioItem>
          {
            sortedCategories?.map(i => <ButtonRadioItem key={i.id} value={i.id}>{i?.name}</ButtonRadioItem>)
          }
        </ButtonRadio>
      </div>

      <ResourcesList categoryId={selectedCategoryId} resourceType={resourceType} />
    </div>
  )
}
