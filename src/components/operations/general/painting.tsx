import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import * as fabric from 'fabric'
import { useFabric } from '@/contexts/fabric.context'

import UndoIcon from '@/assets/icons/undo.svg?react'
import RedoIcon from '@/assets/icons/redo.svg?react'
import PencilIcon from '@/assets/icons/pencil.svg?react'
import EraserIcon from '@/assets/icons/eraser.svg?react'
import BackIcon from '@/assets/icons/back_white_bg.svg?react'
import DownloadIcon from '@/assets/icons/download.svg?react'

import clsx from 'clsx'
import { RectDrawer } from '@/libs/fabricjs-react/shape_drawer/rect'
import { TriangleDrawer } from '@/libs/fabricjs-react/shape_drawer/triangle'
import { CircleDrawer } from '@/libs/fabricjs-react/shape_drawer/circle'
import { StarDrawer } from '@/libs/fabricjs-react/shape_drawer/star'
import { FreeDrawer } from '@/libs/fabricjs-react/shape_drawer/free'
import { FabricHistory } from '@/libs/fabricjs-react/plugins/history.plugin'

import { EraserBrush } from '@erase2d/fabric'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { ShapeDrawer } from '@/libs/fabricjs-react/shape_drawer/abstract'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { PROPERTIES_TO_INCLUDE_WHEN_EXPORT, WORKSPACE_SIZE_PIXELS } from '@/libs/fabricjs-react/constants'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'
import toast from 'react-hot-toast'
import { Slider } from '@/components/ui/slider'

type MenuProps = {
  onBrushSizeChange: (val: number) => void
  canvas?: fabric.Canvas
  brushSize: number
  eraserEnable: boolean
}

const Menu: FC<MenuProps> = ({ canvas, onBrushSizeChange, brushSize, eraserEnable }) => {
  const [activeShape, setActiveShape] = useState<string>()
  const [drawer, _setDrawer] = useState<ShapeDrawer | undefined>(undefined)
  const { t } = useTranslation()

  const setDrawer = (drawer?: ShapeDrawer) => {
    _setDrawer(prev => {
      if (prev) prev.dispose()
      return drawer
    })
  }

  const shapeOptions = useMemo(() => {
    if (!canvas) return []

    return ([
      {
        key: 'line',
        getDrawer: () => new FreeDrawer(canvas),
        Icon: '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="48" height="48" rx="4" fill="#F7F7F7"/><line x1="12.2929" y1="33.2929" x2="33.5061" y2="12.0797" stroke="#CCCCCC" stroke-width="2"/></svg>',
      },
      {
        key: 'triangle',
        getDrawer: () => new TriangleDrawer(canvas),
        Icon: '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="48" height="48" rx="4" fill="#F7F7F7"/><path d="M24 12L36.9904 34.5H11.0096L24 12Z" fill="#CCCCCC"/></svg>',
      },
      {
        key: 'square',
        getDrawer: () => new RectDrawer(canvas, true),
        Icon: '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="48" height="48" rx="4" fill="#F7F7F7"/><rect x="12" y="12" width="24" height="24" fill="#CCCCCC"/></svg>',
      },
      {
        key: 'rect',
        getDrawer: () => new RectDrawer(canvas),
        Icon: '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="48" height="48" rx="4" fill="#F7F7F7"/><rect x="9" y="16" width="30" height="16" fill="#CCCCCC"/></svg>',
      },
      {
        key: 'star',
        getDrawer: () => new StarDrawer(canvas),
        Icon: '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="48" height="48" rx="4" fill="#F7F7F7"/><path d="M24 9L27.3677 19.3647H38.2658L29.4491 25.7705L32.8168 36.1353L24 29.7295L15.1832 36.1353L18.5509 25.7705L9.73415 19.3647H20.6323L24 9Z" fill="#CCCCCC"/></svg>',
      },
      {
        key: 'circle',
        getDrawer: () => new CircleDrawer(canvas),
        Icon: '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="48" height="48" rx="4" fill="#F7F7F7"/><circle cx="24" cy="24" r="12" fill="#CCCCCC"/></svg>',
      },
    ])
  }, [canvas])

  const setShape = useCallback(option => {
    if (!canvas || option.key === activeShape) return

    setActiveShape(option.key)
    setDrawer(option.getDrawer())
  }, [activeShape, canvas])

  useEffect(() => {
    if (drawer) {
      drawer.setBrushSize(brushSize)
    }
  }, [drawer, brushSize])

  useEffect(() => {
    if (canvas) {
      if (!activeShape && shapeOptions.length && !eraserEnable) {
        setShape(shapeOptions[0])
      }
    }
  }, [canvas, activeShape, setShape, shapeOptions, eraserEnable])

  useEffect(() => {
    if (eraserEnable) {
      setDrawer(undefined)
      setActiveShape(undefined)
    }
  }, [eraserEnable])

  return (
    <div className="max-w-full flex flex-col gap-4 mt-4 mb-6 mx-6">
      <div className="flex items-center gap-3 pr-4 py-3">
        <span className="text-sm text-nowrap">{eraserEnable ? t('eraser-size') : t('pencil-size')}</span>
        <Slider
          className="flex-1"
          min={1}
          max={20}
          defaultValue={[8]}
          onValueChange={([value]) => {
            onBrushSizeChange(value)
          }}
        />
        <div className="text-secondary text-xs">{brushSize}px</div>
      </div>

      <div className="flex gap-3 overflow-x-auto scrollbar-hidden">
        {
          (!eraserEnable ? shapeOptions : [])
            .map(option => (
              <div
                className={clsx(
                  'rounded-md border-2',
                  activeShape === option.key ? 'border-orange-500' : 'border-transparent'
                )}
                key={option.key}
                onClick={() => setShape(option)}
              >
                <div
                  className="rounded-md"
                  dangerouslySetInnerHTML={{ __html: option.Icon }}
                  // name={`shape_${option.key}`}
                />
              </div>

            ))
        }
      </div>
    </div>
  )
}

const usePaintingCanvas = (isStandalonePage: boolean) => {
  const { t } = useTranslation()
  const canvasElRef = useRef<HTMLCanvasElement | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)

  const [brushSize, setBrushSize] = useState(4)
  const [canvas, setCanvas] = useState<fabric.Canvas>()
  const [eraserEnable, setEraserEnable] = useState(false)
  const historyHandleRef = useRef<FabricHistory>()

  useEffect(() => {
    const _canvas = new fabric.Canvas(canvasElRef.current!, {
      width: containerRef.current!.clientWidth - 12 * 2,
      height: containerRef.current!.clientHeight - 12 * 2
    })

    _canvas.isDrawingMode = true

    setCanvas(_canvas)

    historyHandleRef.current = new FabricHistory(_canvas)
    historyHandleRef.current.init()

    sendMessageToNative(MessageAction.PREPARED)

    return () => {
      void _canvas.dispose()
    }
  }, [])

  const exportContent = useCallback((): Partial<{ svg: string, dataURL: string, eraserUsed: boolean; json: string }> => {
    if (!canvas) {
      throw new Error('No canvas instance')
    }

    const objects = canvas.getObjects()
    if (!objects.length) {
      throw new Error(t('hints.nothing-drew'))
    }

    const canvasWidth = containerRef.current!.clientWidth - 12 * 2
    const canvasHeight = containerRef.current!.clientHeight - 12 * 2
    const multiplier = Math.min(WORKSPACE_SIZE_PIXELS / canvasWidth, WORKSPACE_SIZE_PIXELS / canvasHeight)

    const group = new fabric.Group(objects)
    group.scale(multiplier)
    const width = group.getScaledWidth()
    const height = group.getScaledHeight()

    if (isStandalonePage) {
      const scale = Math.max(512 / width, 512 / height)
      if (scale > 1) {
        group.scale(multiplier * scale)
      }
    }

    const svg = canvas.toSVG({
      viewBox: {
        x: 0,
        y: 0,
        width: canvasWidth,
        height: canvasHeight
      },
    })

    return {
      svg,
      json: canvas.toDatalessJSON(PROPERTIES_TO_INCLUDE_WHEN_EXPORT),
      dataURL: group.toDataURL({
        format: 'jpeg',
        multiplier: 1,
      }),
      eraserUsed: svg.includes('clip-path=')
    }
  }, [canvas])

  const toggleEraser = useCallback(() => {
    if (!canvas) return

    setEraserEnable(prev => {
      if (prev) {
        canvas.freeDrawingBrush = undefined
      } else {
        const eraser = new EraserBrush(canvas)
        eraser.width = brushSize
        eraser.on('end', e => {
          e.preventDefault() // prevent erasing being committed to the tree
          eraser.commit(e.detail)
            .then(() => {
              canvas.fire('object:modified', { target: e.detail.targets[0] as unknown as fabric.FabricObject })
            })
        })
        canvas.freeDrawingBrush = eraser
      }

      return !prev
    })
  }, [canvas, brushSize])

  useEffect(() => {
    if (canvas?.freeDrawingBrush) {
      canvas.freeDrawingBrush.width = brushSize
    }
  }, [brushSize, canvas])

  return {
    containerRef,
    canvasRef: canvasElRef,
    exportContent,
    canvas,
    historyHandleRef,
    eraserEnable,
    toggleEraser,
    brushSize,
    setBrushSize
  }
}

export default function Painting() {
  const { t } = useTranslation()
  const { pathname } = useLocation()
  const isStandalonePage = useMemo(() => pathname.includes('/painting'), [pathname])

  const {
    canvasRef, containerRef, historyHandleRef,
    brushSize, setBrushSize,
    exportContent, canvas, eraserEnable, toggleEraser
  } = usePaintingCanvas(isStandalonePage)

  const { editor } = useFabric()
  const { inactivate } = useCanvasMenu()
  const { safe_area_top, safe_area_bottom } = useURLSearchConfig()

  const handleGoBack = useCallback(() => {
    if (isStandalonePage) {
      sendMessageToNative(MessageAction.BACKWARD)
    } else {
      inactivate()
    }
  }, [isStandalonePage, inactivate])

  const handleExport = useCallback(() => {
    try {
      const { dataURL, eraserUsed, svg } = exportContent()

      if (isStandalonePage) {
        return sendMessageToNative(MessageAction.EXPORT_PAINTING_RESULT, dataURL)
      }

      // if (dataURL) void editor.addBitmapFromUrl(dataURL)
      if (eraserUsed && dataURL) {
        void editor.addBitmapFromUrl(dataURL)
      } else if (svg) {
        void editor.addVectorFromString(svg)
      }

      inactivate()
    } catch (e) {
      toast(e.message)
    }
  }, [isStandalonePage, exportContent, editor, inactivate])

  return (
    <div className="flex flex-col h-screen max-h-screen overflow-y-hidden pb-4 w-screen">
      <div style={{ paddingTop: `${safe_area_top}px` }} className="bg-white">
        <div className="h-14 w-full px-4 flex items-center justify-between">
          <BackIcon onClick={handleGoBack} />
          <div>{t('common.draw')}</div>
          <DownloadIcon onClick={handleExport} />
        </div>
      </div>

      <div className="relative bg-[#F7F7F7] flex flex-col flex-1">
        <div className="p-3 pb-0 flex-1" ref={containerRef}>
          <canvas ref={canvasRef} className="border border-gray-400 border-dashed" />
        </div>

        <div className="flex justify-between items-center mx-3 my-2">
          <div className="flex gap-4">
            <UndoIcon className="size-5" onClick={() => historyHandleRef.current?.undo()} />
            <RedoIcon className="size-5" onClick={() => historyHandleRef.current?.redo()} />
          </div>
          <div className="rounded-full bg-black opacity-60 py-1.5 px-3 flex gap-1.5" onClick={() => toggleEraser()}>
            <PencilIcon opacity={eraserEnable ? 0.6 : 1} />
            <div className="w-px h-5 bg-gray-500" />
            <EraserIcon opacity={eraserEnable ? 1 : 0.6} />
          </div>
        </div>
      </div>

      <div className="justify-between flex flex-col shadow-default" style={{ paddingBottom: `${Number(safe_area_bottom || '0') + 16}px` }}>
        <Menu
          canvas={canvas}
          eraserEnable={eraserEnable}
          brushSize={brushSize}
          onBrushSizeChange={setBrushSize}
        />
      </div>
    </div>
  )
}
