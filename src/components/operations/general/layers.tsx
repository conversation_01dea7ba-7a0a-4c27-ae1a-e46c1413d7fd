import React, { FC, memo, useCallback, useEffect, useState } from 'react'
import { DragDropContext, Draggable, Droppable, DropResult, OnDragEndResponder } from '@hello-pangea/dnd'
import { useFabric } from '@/contexts/fabric.context'
import { LayerData } from '@/libs/fabricjs-react/plugins/layer.plugin'


import LayerMoveIcon from '@/assets/icons/layer_move.svg?react'
import LayerLockIcon from '@/assets/icons/layer_lock.svg?react'
import LayerUnlockIcon from '@/assets/icons/layer_unlock.svg?react'
import LayerVisibleIcon from '@/assets/icons/layer_visible.svg?react'
import LayerInvisibleIcon from '@/assets/icons/layer_invisible.svg?react'

import CheckedIcon from '@/assets/icons/checked.svg?react'

import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import clsx from 'clsx'
import { ContainerHeader } from '@/components/menu-content-container'
import { useTranslation } from 'react-i18next'
import { PageHeaderHeight } from '@/constants/styles'

const DraggableLayer: FC<{
  item: LayerData;
  index: number;
  isLastItem: boolean;
}> = ({ item, index, isLastItem }) => {
  const { editor } = useFabric()

  return (
    <Draggable draggableId={item.id} index={index}>
      {(provided, snapshot) => (
        <div
          className="w-full flex flex-row items-center gap-2"
          style={{ backgroundColor: snapshot.isDragging && 'rgb(235,235,235)' || undefined }}
          ref={provided.innerRef}
          {...provided.draggableProps}
        >
          <div
            onClick={() => {
              if (editor.toggleSelection(item._object)) {
                item.active = !item.active
              }
            }}
          >
            {item.active
              ? <CheckedIcon className="size-5" />
              : <div className="size-5 rounded-full border" />}
          </div>

          <div className={clsx('flex-1 flex justify-between items-center py-2', !isLastItem && 'border-b')}>
            <div className="flex gap-2">
              <img src={item.image} alt="" className="size-12" />
            </div>

            <div className="flex gap-1">
              <div onClick={() => editor.toggleVisible(item._object)}>
                {item.visible ? <LayerVisibleIcon /> : <LayerInvisibleIcon />}
              </div>

              <div onClick={() => editor.toggleLocking(item._object)}>
                {item.locked ? <LayerLockIcon /> : <LayerUnlockIcon />}
              </div>

              <div {...provided.dragHandleProps} >
                <LayerMoveIcon />
              </div>
            </div>
          </div>

        </div>
      )}
    </Draggable>
  )
}

const DraggableLayers: FC<{
  items: LayerData[];
  onDragEnd: OnDragEndResponder;
}> = memo(
  function DraggableLayers({ items, onDragEnd }) {
    return (
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="droppable-list">
          {provided => (
            <div ref={provided.innerRef} {...provided.droppableProps} className="w-full flex flex-col gap-2">
              {
                [...items]
                  .reverse()
                  .map((item: LayerData, index, arr) => (
                    <DraggableLayer
                      item={item}
                      index={index}
                      key={item.id}
                      isLastItem={index === arr.length - 1}
                    />
                  ))
              }
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    )
  }
)

const LayersList: FC<{ layers?: LayerData[] }> = ({ layers = [] }) => {
  const { editor } = useFabric()

  const onDragEnd = useCallback(({ destination, source }: DropResult) => {
    if (!destination) return

    const from = layers.length - 1 - source.index
    const to = destination.index < source.index
      ? layers.length - destination.index
      : layers.length - 1 - destination.index
    editor.moveLayer(from, to)

  }, [layers])

  return (
    <div className="flex-1 flex flex-wrap overflow-y-auto">
      <div className="flex flex-1 m-4 w-full">
        <DraggableLayers
          items={layers}
          onDragEnd={onDragEnd}
        />
      </div>
    </div>
  )
}

const useCanvasMenuElementHeight = () => {
  const [height, setHeight] = useState(0)

  useEffect(() => {
    const el = document.getElementById('CanvasMenu') // 根据 ID 获取元素
    if (!el) return

    // 初始高度设置
    setHeight(el.offsetHeight)

    // 创建 ResizeObserver
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        // @ts-ignore
        const newHeight = entry.target.offsetHeight
        setHeight(newHeight)
      }
    })

    // 开始观察
    resizeObserver.observe(el)

    // 清理函数
    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  return height
}

export default function Layers() {
  const { t } = useTranslation()
  const { editor } = useFabric()
  const { safe_area_top, safe_area_bottom } = useURLSearchConfig()
  const canvasMenuElementHeight = useCanvasMenuElementHeight()

  const [layers, setLayers] = useState<LayerData[]>(() => editor.getLayers())

  useEffect(() => {
    const handler = () => setLayers(editor.getLayers())

    const disposers = [
      editor.canvas.on('object:added', handler),
      editor.canvas.on('object:modified', handler),
      editor.canvas.on('object:removed', handler),
      editor.canvas.on('selection:updated', handler),
      editor.canvas.on('selection:cleared', handler),
      editor.canvas.on('selection:created', handler),
    ]

    return () => {
      disposers.forEach(dispose => dispose())
    }
  }, [])

  return (
    <div className="flex flex-col h-full">
      <div className="relative flex-1">
        <div
          style={{
            width: 'calc(min(320px, 60vw))',
            height: window.innerHeight
              - Number(safe_area_top)
              - PageHeaderHeight
              - canvasMenuElementHeight
              - Number(safe_area_bottom),
          }}
        >
          <div
            className="w-full h-full bg-white flex flex-col"
            style={{
              boxShadow: '-5px 0px 10px 0px #0000001A'
            }}
          >
            <ContainerHeader layoutMode={3}>
              {t('layer-manager')}({layers.length})
            </ContainerHeader>

            <LayersList layers={layers} />
          </div>
        </div>
      </div>
    </div>
  )
}
