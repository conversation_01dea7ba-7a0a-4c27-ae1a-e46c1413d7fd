import {
  Box
} from '@mui/material'
import { FC, useCallback, useEffect, useState } from 'react'
import { TabMenu } from '@/components/ui/TabMenu'

import { CustomTabPanel } from '@/components/ui/form'
import { TextTemplateSelector } from '@/components/TextTemplateSelector'
import { useFabric } from '@/contexts/fabric.context'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { ContainerHeader } from '@/components/menu-content-container'
import Barcode from 'jsbarcode'
import { useTranslation } from 'react-i18next'
import { useGlobalLoading } from '@/contexts/loading'
import { sleep } from '@/utils'
import toast from 'react-hot-toast'
import { generateQrcodeSvg } from '@/utils/qrcode'

import { FontResolver } from '@/libs/fabricjs-react/utils/font-resolver'
import { Textarea } from '@/components/ui/textarea'

const CustomInput: FC<{ onChange: (val: string) => void; numericMode?: boolean }> = ({
  onChange, numericMode = false
}) => {
  const [value, setValue] = useState('')
  const { t } = useTranslation()
  const limit = 150
  const length = value.length

  return (
    <Box component="form" position="relative" noValidate autoComplete="off" px="24px">
      <Textarea
        className="w-full text-xs"
        value={value}
        rows={4}
        id="outlined-multiline-static"
        onChange={e => {
          const text = e.target.value.slice(0, limit)
          setValue(text)
          onChange(text)
        }}
        placeholder={t('text.input-placeholder')}
        style={{ background: '#F7F7F7' }}
        inputMode={numericMode ? 'numeric' : undefined}
      />

      <div className="absolute right-8 bottom-2 text-xs text-secondary">
        {length}/{limit}
      </div>
    </Box>
  )
}

export default function Content() {
  const { t } = useTranslation()
  const { setLoading } = useGlobalLoading()
  const { editor } = useFabric()
  const { inactivate } = useCanvasMenu()

  const [tab, setTab] = useState<string>('text')
  const [textTemplateContent, setTextTemplateContent] = useState('')
  const [inputText, setInputText] = useState('')
  const [qrcodeContent, setQrcodeContent] = useState('')
  const [barCodeContent, setBarCodeContent] = useState('')

  const handleSubmit = useCallback(async () => {
    if (tab === 'template' && textTemplateContent) {
      return editor.addText(textTemplateContent)
    }

    if (tab === 'text' && inputText) {
      return editor.addText(inputText)
    }

    if (tab === 'qrcode' && qrcodeContent) {
      setLoading(true, t('hints.generating'))
      await sleep(500)

      return editor
        .addVectorFromString(generateQrcodeSvg(qrcodeContent), true)
        .finally(() => setLoading(false))
    }

    if (tab === 'barcode' && barCodeContent) {
      setLoading(true, t('hints.generating'))
      await sleep(500)

      const svgEl = document.createElement('svg') as unknown as SVGElement
      Barcode(svgEl, barCodeContent, {
        format: 'CODE128',
        background: undefined,
        margin: 0,
        displayValue: false,
        width: 10,
        height: 128
      })
      document.body.append(svgEl)

      const str = new XMLSerializer().serializeToString(svgEl).replace('xmlns="http://www.w3.org/1999/xhtml"', '')

      return editor.addVectorFromString(str, true)
        .finally(() => {
          setLoading(false)
          svgEl.remove()
        })
    }

    toast(t('hints.empty-content'))
    return Promise.reject()
  }, [tab, textTemplateContent, inputText, qrcodeContent, barCodeContent])

  useEffect(() => {
    void FontResolver.loadFallbackFont()
  })

  return (
    <div className="flex flex-col h-[50vh] fadein-popup-handler-content">
      <ContainerHeader onClose={inactivate} onSubmit={() => handleSubmit().then(inactivate)}>
        <TabMenu
          value={tab}
          className="mt-1"
          onChange={v => setTab(v.key)}
          items={[
            { name: t('common.text'), key: 'text' },
            { name: t('menus.text.qrcode'), key: 'qrcode' },
            { name: t('menus.text.barcode'), key: 'barcode' },
            { name: t('menus.text.template'), key: 'template' },
          ]}
        />
      </ContainerHeader>

      <div className="flex-1 overflow-y-scroll scrollbar-hidden">
        <CustomTabPanel value={tab} index="template">
          <TextTemplateSelector onChange={v => setTextTemplateContent(v ?? '')} />
        </CustomTabPanel>
        <CustomTabPanel value={tab} index="text">
          <CustomInput onChange={setInputText} />
        </CustomTabPanel>
        <CustomTabPanel value={tab} index="qrcode">
          <CustomInput onChange={setQrcodeContent} />
        </CustomTabPanel>
        <CustomTabPanel value={tab} index="barcode">
          <CustomInput onChange={setBarCodeContent} numericMode />
        </CustomTabPanel>
      </div>
    </div>
  )
}

