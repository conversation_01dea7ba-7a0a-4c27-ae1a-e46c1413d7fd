import React, { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useFabric } from '@/contexts/fabric.context'
import {
  ObjectType,
  VECTOR_MAX_FILL_ANGLE,
  VECTOR_MAX_FILL_SPACE,
  VECTOR_NIN_FILL_ANGLE
} from '@/libs/fabricjs-react/constants'
import * as fabric from 'fabric'
import { FabricSelection } from '@/libs/fabricjs-react'
import { Slider } from '@/components/ui/slider'

const getValueFromSelection = (selection?: FabricSelection) => {
  if (selection && (selection.type === ObjectType.VECTOR || selection.type === ObjectType.VECTOR_GROUP)) {
    return {
      space: (selection.object as fabric.FabricObject)._fill_space_,
      angle: (selection.object as fabric.FabricObject)._fill_angle_
    }
  } else {
    return {
      space: null, angle: null
    }
  }
}

const formatSpaceLabel = (value: number) => {
  if (value < 0.1) return value.toFixed(2) + 'mm'
  if (value < 1) return value.toFixed(1) + 'mm'
  return value.toFixed(1) + 'mm'
}

export default function PathFill() {
  const { t } = useTranslation()
  const { selection, editor } = useFabric()

  const { space: defaultSpace, angle: defaultAngle } = getValueFromSelection(selection)

  const [space, setSpace] = useState<number | null>(defaultSpace)
  const [angle, setAngle] = useState<number | null>(defaultAngle)

  useEffect(() => {
    const { space, angle } = getValueFromSelection(selection)
    setSpace(space)
    setAngle(angle)
  }, [selection, editor])

  const available = useMemo(() => selection && !selection?.isLocked, [selection])

  return (
    <div className="gap-2 fadein-popup-handler-content px-6 max-xs:px-3 py-3">
      <div className="flex flex-col items-center gap-3 ">
        <div className="flex items-center justify-between w-full flex-1 gap-4">
          <div className="text-nowrap whitespace-nowrap text-start text-sm text-disabled">
            {t('fill-space')}
          </div>
          <div className="flex-1 flex w-full ">
            <Slider
              min={0.1}
              max={VECTOR_MAX_FILL_SPACE}
              step={0.1}
              value={[space ?? 1.0]}
              disabled={!available}
              onValueChange={([val]) => {
                setSpace(val)
                editor.setFillSpace(val)
              }}
              onValueCommit={([val]) => editor.setFillSpace(val, true)}
            />
            <div className="text-secondary text-xs min-w-10">
              {typeof space === 'number' && formatSpaceLabel(space)}
            </div>
          </div>
        </div>

        <div className="flex  items-center justify-between w-full flex-1 gap-4">
          <div className="text-nowrap whitespace-nowrap text-start text-sm text-disabled">
            {t('fill-angle')}
          </div>
          <div className="flex-1 flex w-full ">
            <Slider
              value={[angle ?? 0]}
              min={VECTOR_NIN_FILL_ANGLE}
              max={VECTOR_MAX_FILL_ANGLE}
              step={1}
              disabled={!available}
              onValueChange={([val]) => {
                setAngle(val)
                editor.setFillAngle(val)
              }}
              onValueCommit={([val]) => editor.setFillAngle(val, true)}
            />

            <div className="text-secondary text-xs min-w-10">
              {angle}°
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
