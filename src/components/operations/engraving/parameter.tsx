import React, { FC, Fragment, PropsWithChildren, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import BackIcon from '@/assets/icons/back_white_bg.svg?react'
import { EngravingMenuKeys, EngravingParameterMenuKeys, MenuKeys, NameByMenuKey } from '@/components/CanvasMenu'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { PageHeaderHeight } from '@/constants/styles'
import { useTranslation } from 'react-i18next'
import { ButtonRadio, ButtonRadioItem2, ButtonRadioItem3 } from '@/components/ui/ButtonRadio'
import { ExportedLayerType, SUPPORTED_DPI_OPTIONS, WorkMode } from '@/libs/shared/types'
import { EngravingParametersFormData, useEngravingParametersStore } from '@/stores/engraving-paramters'
import {
  CreateParameterProps,
  Material,
  MaterialModule,
  MaterialParameter,
  MaterialTypeCode,
  MaterialTypeEnum
} from '@/datasource/api/material'
import { CNumberInput2 } from '@/components/ui/CNumberInput2'
import { Controller, useForm, UseFormReturn } from 'react-hook-form'

import DeleteIcon from '@/assets/icons/delete.svg?react'
import SaveIcon from '@/assets/icons/save.svg?react'
import clsx from 'clsx'
import { InputBase } from '@mui/material'
import { useDeviceStatusStore } from '@/stores/device-status'
import toast from 'react-hot-toast'
import { DrawerContainer } from '@/components/menu-content-container'
import { styled } from '@mui/material/styles'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import ChipDeleteIcon from '@/assets/icons/chip-delete-icon.svg?react'
import ChipCheckedIcon from '@/assets/icons/chip-checked-icon.svg?react'
import useQueryParameters from '@/hooks/queries/useQueryParameters'
import { ObjectTypeContainingSituation } from '@/libs/fabricjs-react/constants'
import { useFabric } from '@/contexts/fabric.context'
import { isNil } from 'lodash'
import { useStateToRef } from '@/hooks/useStateToRef'
import { useGlobalLoading } from '@/contexts/loading'
import { useQueryMaterials } from '@/hooks/queries/useQueryMaterials'
import { useQueryTemplate } from '@/hooks/queries/useQueryTemplate'
import { safeParseJson } from '@/utils'
import { Loading } from '@/components/ui/Loading'
import { RECENT_ENGRAVING_PARAMS } from '@/constants/query-keys'
import { Input } from '@/components/ui/input'

type FormType =
  & Pick<CreateParameterProps, 'name' | 'materialId'>
  & Pick<EngravingParametersFormData, 'bitmapPower' | 'bitmapMarkTime' | 'vectorSpeed' | 'vectorPower' | 'bitmapDPI'>
  & {
    parameterId: number | null
  }

const CustomMaterialInput = styled(InputBase)(({ theme }) => ({
  '& .MuiInputBase-input': {
    textAlign: 'center',
    borderRadius: 4,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#CCCCCC',
    backgroundColor: theme.palette.grey['100'],
    fontSize: 12,
    padding: '0px 12px',
    height: 38,

    '&:focus': {
      boxShadow: 'none',
      borderColor: 'none'
    },
  },
}))

const MIN_VECTOR_POWER = 1
const MIN_VECTOR_SPEED = 2
const MIN_BITMAP_POWER = 1
const MIN_BITMAP_MARK_TIME = 5

const defaultValue = {
  vectorPower: MIN_VECTOR_POWER,
  vectorSpeed: MIN_VECTOR_SPEED,
  bitmapPower: MIN_BITMAP_POWER,
  bitmapMarkTime: MIN_BITMAP_MARK_TIME,
}

// 最近使用排序工具
function sortByRecent<T extends { id: number | string }>(
  list: T[],
  recentList: { materialId: number, parameterId: number | string }[],
  key: 'materialId' | 'parameterId'
) {
  if (!recentList.length) return list
  const usedIds = new Set<string | number>()

  return [
    ...recentList
      .map(recent => {
        const found = list.find(item => item.id === recent[key])
        if (found && !usedIds.has(found.id)) {
          usedIds.add(found.id)
          return found
        }
        return null
      })
      .filter(Boolean) as T[],
    ...list.filter(item => !recentList.some(recent => item.id === recent[key]))
  ]
}

const Wrapper: FC<{ title: string, returnMenuKey?: MenuKeys } & PropsWithChildren> = ({
  children,
  title,
  returnMenuKey = EngravingMenuKeys.parameter
}) => {
  const { safe_area_top, safe_area_bottom } = useURLSearchConfig()
  const { setActiveMenuKey } = useCanvasMenu()

  return (
    <div className="flex flex-col h-screen max-h-screen overflow-y-hidden pb-4">
      <div style={{ paddingTop: `${safe_area_top}px` }} className="bg-white">
        <div className="h-14 w-full px-4 flex items-center justify-center">
          <div className="absolute flex items-center justify-start w-full px-4">
            <BackIcon onClick={() => setActiveMenuKey(returnMenuKey)} />
          </div>
          <div className="font-semibold">{title}</div>
        </div>
      </div>

      <div
        className="relative bg-white flex flex-col"
        style={{ height: `calc(100vh - 127px - 56px - ${safe_area_top || 0}px - ${safe_area_bottom || 0}px)` }}
      >
        <div className="px-6 py-6 flex flex-col gap-4">
          {children}
        </div>
      </div>
    </div>
  )
}

const FormItem: FC<PropsWithChildren<{ title: string, onClickMore?: VoidFunction, hidden?: boolean }>> = ({
  title,
  children,
  onClickMore,
  hidden = false,
}) => {
  const { t } = useTranslation()

  return (
    <div className={clsx('flex  flex-col gap-2', hidden && 'hidden')}>
      <div className="flex items-center justify-between">
        <div className={clsx('text-sm font-semibold',)}>{title}</div>
        {onClickMore && (
          <div className="text-xs text-gray-400" onClick={onClickMore}>{t('more')}</div>
        )}
      </div>
      {children}
    </div>
  )
}

const FormItemForParameter: FC<PropsWithChildren<{ title: string, hidden?: boolean, disabled?: boolean }>> = ({
  title,
  children,
  hidden = false,
  disabled = false
}) => {

  return (
    <div className={clsx('flex flex-col gap-2 w-full', hidden && 'hidden')}>
      <div className="flex items-center justify-between">
        <div className={clsx('text-xs text-gray-700', disabled && 'opacity-25')}>
          {title}
        </div>
      </div>
      {children}
    </div>
  )
}

const MoreMaterials: FC<{ onSelect: (id: number) => void }> = ({ onSelect }) => {
  const { t } = useTranslation()
  const store = useEngravingParametersStore()
  const { setActiveMenuKey } = useCanvasMenu()
  const { setLoading } = useGlobalLoading()
  const [customName, setCustomName] = useState('')
  const { data: materials, refetch: refetchMaterial } = useQueryMaterials()
  const [recentList, setRecentList] = useState<any[]>([])


  const onCreateCustomMaterial = async (name?: string) => {
    if (!name) return

    setLoading(true)
    MaterialModule
      .createMaterial({ name })
      .then(() => {
        toast.success(t('saved-success'))
        return refetchMaterial()
      })
      .finally(() => setLoading(false))
  }

  const onDeleteCustomMaterial = async (id: number) => {
    setLoading(true)
    MaterialModule
      .deleteMaterial(id)
      .then(() => {
        toast(t('delete-success'))
        return refetchMaterial()
      })
      .finally(() => setLoading(false))
  }

  const sortedMaterials = useMemo(() => {
    return materials ? sortByRecent(materials, recentList, 'materialId') : materials
  }, [materials, recentList])

  useEffect(() => {
    const stored = JSON.parse(localStorage.getItem(RECENT_ENGRAVING_PARAMS) || '[]')
    setRecentList(stored)
  }, [])


  return (
    <Wrapper title={t('more-materials')}>
      <div>
        <div className="font-semibold mb-2 text-[14px] color-black">{t('custom-material')}</div>
        <div className="flex flex-wrap gap-2">
          <CustomMaterialInput
            // onBlur={event => onCreateCustomMaterial(event.target.value)}
            value={customName}
            onChange={e => setCustomName(e.target.value)}
            onBlur={() => {
              onCreateCustomMaterial(customName)
              setCustomName('') // 清空
            }}
            placeholder={t('custom')}
            className="w-[72px]"
          />

          {
            sortedMaterials
              ?.filter(item => item.type === MaterialTypeCode[MaterialTypeEnum.customize])
              .map(material => (
                <div
                  key={material.id}
                  className={clsx(
                    'bg-[#F7F7F7] px-2 py-3 text-xs text-disabled min-w-[72px]',
                    store.material?.id === material.id && 'bg-[#FEF4E6]'
                  )}
                  onClick={() => {
                    onSelect(material.id)
                    setActiveMenuKey(EngravingMenuKeys.parameter)
                  }}
                >
                  <div className="flex items-center justify-between gap-1">
                    {material.name}
                    {store.material?.id === material.id
                      ? <ChipCheckedIcon />
                      : (
                        <ChipDeleteIcon
                          onClick={e => {
                            e.stopPropagation()
                            return onDeleteCustomMaterial(material.id)
                          }}
                        />
                      )}
                  </div>
                </div>
              ))
          }
        </div>
      </div>

      <div>
        <div className="mb-2 font-semibold text-[14px] color-black">{t('preset-material')}</div>
        <ButtonRadio
          value={store.material?.id}
          className="flex flex-wrap gap-2"
          onChange={v => {
            onSelect(Number(v))
          }}
        >
          {materials
            ?.filter(item => item.type === MaterialTypeCode[MaterialTypeEnum.default])
            .map(material => (
              <div key={material.id} onClick={() => setActiveMenuKey(EngravingMenuKeys.parameter)}>
                <ButtonRadioItem2 value={material.id}>
                  {material.name}
                </ButtonRadioItem2>
              </div>
            ))}
        </ButtonRadio>
      </div>
    </Wrapper>
  )
}

const MoreParameters: FC<{ onSelect: (id: number) => void }> = ({ onSelect }) => {
  const { t } = useTranslation()
  const { setLoading } = useGlobalLoading()
  const { setActiveMenuKey } = useCanvasMenu()
  const { deviceModelId } = useDeviceStatusStore()
  const store = useEngravingParametersStore()

  const { data: parameters, refetch } = useQueryParameters({ material: store.material, deviceModelId })

  const [recentList, setRecentList] = useState<any[]>([])

  const onDeleteCustomParameter = async (id: number) => {
    setLoading(true)

    MaterialModule
      .deleteParameter(id)
      .then(() => {
        toast(t('delete-success'))
        return refetch()
      })
      .finally(() => setLoading(false))
  }

  const sortedParameters = useMemo(() => {
    return parameters ? sortByRecent(parameters, recentList, 'parameterId') : parameters
  }, [parameters, recentList])

  const { customized, preset } = useMemo(() => {
    return (sortedParameters || []).reduce((result, next) => {
      if (next.type === MaterialTypeCode[MaterialTypeEnum.customize]) {
        result.customized.push(next)
      } else if (next.type === MaterialTypeCode[MaterialTypeEnum.default]) {
        result.preset.push(next)
      }

      return result
    }, { customized: [] as MaterialParameter[], preset: [] as MaterialParameter[] })
  }, [sortedParameters])

  useEffect(() => {
    const stored = JSON.parse(localStorage.getItem(RECENT_ENGRAVING_PARAMS) || '[]')
    setRecentList(stored)
  }, [])

  return (
    <Wrapper title={t('more-parameters')}>
      <div className={clsx(customized.length <= 0 && 'hidden')}>
        <div className="font-semibold mb-2 text-[14px] color-black">{t('custom-parameters')}</div>
        <div className="flex flex-wrap gap-2">
          {customized
            .map(item => (
              <div
                key={item.id}
                className={clsx(store.parameter?.id === item.id && 'bg-[#FEF4E6]', 'bg-[#F7F7F7] px-2 py-3 text-xs text-disabled min-w-[72px]')}
                onClick={() => {
                  onSelect(item.id)
                  setActiveMenuKey(EngravingMenuKeys.parameter)
                }}
              >
                <div className="flex items-center justify-between gap-1">
                  {item.name}
                  {
                    store.parameter?.id === item.id
                      ? <ChipCheckedIcon />
                      : (
                        <ChipDeleteIcon
                          onClick={e => {
                            e.stopPropagation()
                            return onDeleteCustomParameter(item.id)
                          }}
                        />
                      )
                  }
                </div>
              </div>
            ))}
        </div>
      </div>

      <div className={clsx(preset.length <= 0 && 'hidden')}>
        <div className="mb-2 font-semibold text-[14px] color-black">{t('preset-parameters')}</div>
        <ButtonRadio
          value={store.parameter?.id}
          className="flex flex-wrap gap-2"
          onChange={v => onSelect(Number(v))}
        >
          {preset
            .map(param => (
              <div key={param.id} onClick={() => setActiveMenuKey(EngravingMenuKeys.parameter)}>
                <ButtonRadioItem2 value={param.id}>
                  {param.name}
                </ButtonRadioItem2>
              </div>
            ))}
        </ButtonRadio>
      </div>
    </Wrapper>
  )
}

const Footer: FC<{ parameter?: MaterialParameter }> = ({ parameter }) => {
  const { t } = useTranslation()
  const store = useEngravingParametersStore()
  const { initialized, editor, derivedState: { layersBoundarySize } } = useFabric()

  return (
    <div className="bg-white flex flex-col gap-2 pt-3 pb-2">
      <div className="w-full flex items-start justify-between gap-4 px-6">
        <div className="flex-col flex justify-start gap-2 flex-1 text-xs pl-3 font-light">
          <div className="flex justify-between items-center">
            <div className="text-disabled text-start w-14">{t('engraving-mode')}</div>
            <div className="text-[#666] text-end">
              {t(store.workMode)}
            </div>
          </div>

          {store.workMode === WorkMode.ROTATION && store.diameter && (
            <div className="flex justify-between items-center">
              <div className="text-disabled text-start w-14">{t('common.diameter')}</div>
              <div className="text-[#666] text-end">
                {store.diameter} {t(store.unit)}
              </div>
            </div>
          )}

          {store.workMode === WorkMode.TABLET && store.length && (
            <div className="flex justify-between items-center">
              <div className="text-disabled text-start w-14">{t('common.diameter')}</div>
              <div className="text-[#666] text-end">
                {store.length} {t(store.unit)}
              </div>
            </div>
          )}

          <div className="flex justify-between items-center">
            <div className="text-disabled text-start w-14">{t('size-of-content')}</div>
            <div className="text-[#666] text-end text-nowrap">
              {initialized
                ? `${editor.pixelsToUnit(layersBoundarySize.width).toFixed(2)} × ${editor.pixelsToUnit(layersBoundarySize.height).toFixed(2)}${store?.unit}`
                : '-'}
            </div>
          </div>
        </div>

        <div className="w-[35vw] h-[23vw] max-h-[100px] bg-[#F7F7F7]">
          {
            (parameter?.bitmapImageUrl || parameter?.vectorImageUrl)
              ? (
                <img
                  style={{ objectFit: 'contain', width: '100%', height: '100%' }}
                  src={store.objectType === ExportedLayerType.bitmap ? parameter?.bitmapImageUrl : parameter?.vectorImageUrl}
                  alt={parameter?.name}
                />
              )
              : <div className="flex justify-center items-center h-full" />
          }
        </div>
      </div>

      <div className="text-2xs px-4 text-gray-400/40">
        {t('hints.parameter-disclaimer')}
      </div>
    </div>
  )
}

type ParameterBodyProps = {
  form: UseFormReturn<FormType>
  customMode: boolean
  currentParameter: MaterialParameter | undefined

  onFocus(): void
  onBlur(): void
  onParameterRecover(createdId: string | number | null, data?: MaterialParameter[]): void
  onMaterialSelected(id: number): void

  onCustomModeChange(v: boolean): void
}

const ParameterBody: FC<ParameterBodyProps> = ({
  form,
  onFocus,
  onBlur,
  customMode,
  currentParameter,
  onCustomModeChange,
  onParameterRecover,
  onMaterialSelected,
}) => {
  const CUSTOM_SYMBOL = Symbol('custom-button')

  const focusHandler = { onFocus, onBlur }
  const { control, watch, reset: resetForm, handleSubmit, formState: { isDirty } } = form

  const { t } = useTranslation()
  const { setLoading } = useGlobalLoading()
  const { setActiveMenuKey } = useCanvasMenu()
  const { deviceModelId } = useDeviceStatusStore()
  const { templateId, recordDataJson } = useURLSearchConfig()
  const store = useEngravingParametersStore()
  const { derivedState: { objectTypeContainingSituation } } = useFabric()

  const { data: materials, isFetching } = useQueryMaterials()
  const { data: templateDetail } = useQueryTemplate(templateId ?? '')
  const { data: parameters, refetch: refetchParameters } = useQueryParameters({ material: store.material, deviceModelId })

  const [parameterId, materialId] = watch(['parameterId', 'materialId'])

  const parsedRecordData = useMemo(() => safeParseJson(recordDataJson), [recordDataJson])

  const bitmapFormItems = useMemo(() => {
    const disabled = (store.workMode !== WorkMode.PLANE && store.workMode !== WorkMode.HANDHELD) ? false : !(objectTypeContainingSituation & ObjectTypeContainingSituation.BITMAP_ONLY)

    return (
      <div className={clsx('flex flex-col gap-5 justify-start items-start w-full')}>
        <div className="flex flex-col gap-3 items-start w-full ">
          <div
            className={clsx('text-xs text-white font-semibold px-3 py-1 rounded-full', disabled ? 'bg-primary/30' : 'bg-primary')}
          >
            {t('common.bitmap')}
          </div>

          <FormItemForParameter title={t('engraving-power') + '(%)'} disabled={disabled}>
            <Controller
              name="bitmapPower"
              control={control}
              render={({ field: { value, onChange } }) => (
                <CNumberInput2
                  disabled={disabled}
                  min={MIN_BITMAP_POWER}
                  max={100}
                  value={value ?? defaultValue.bitmapPower}
                  onChange={(e, v) => onChange(v)}
                />
              )}
            />
          </FormItemForParameter>

          <FormItemForParameter title={t('mark-time') + '(μs)'} disabled={disabled}>
            <Controller
              name="bitmapMarkTime"
              control={control}
              render={({ field: { value, onChange } }) => (
                <CNumberInput2
                  disabled={disabled}
                  min={MIN_BITMAP_MARK_TIME}
                  max={3000}
                  value={value ?? defaultValue.bitmapMarkTime}
                  onChange={(e, v) => onChange(v)}
                  {...focusHandler}
                />
              )}
            />
          </FormItemForParameter>

          <FormItemForParameter title={t('resolution')} disabled={disabled}>
            <Controller
              name="bitmapDPI"
              control={control}
              render={({ field }) => (
                <ButtonRadio
                  disabled={disabled}
                  value={field.value}
                  onChange={v => field.onChange(v)}
                  className={clsx('grid grid-cols-2 p-1 bg-[#F7F7F7] rounded')}
                >
                  {SUPPORTED_DPI_OPTIONS.map(option => (
                    <ButtonRadioItem3 value={option} key={option}>
                      {option} DPI
                    </ButtonRadioItem3>
                  ))}
                </ButtonRadio>
              )}
            />
          </FormItemForParameter>
        </div>
      </div>
    )
  }, [objectTypeContainingSituation, store.workMode])

  const vectorFormItems = useMemo(() => {
    const disabled = (store.workMode !== WorkMode.PLANE && store.workMode !== WorkMode.HANDHELD) ? true : !(objectTypeContainingSituation & ObjectTypeContainingSituation.VECTOR_ONLY)

    return (
      <div className={clsx('flex flex-col gap-5  items-end justify-end w-full')}>
        <div className="flex flex-col gap-3 items-start justify-end  ">
          <div
            className={clsx('text-xs text-white font-semibold px-3 py-1 rounded-full', disabled ? 'bg-primary/30' : 'bg-primary')}
          >
            {t('common.vector')}
          </div>

          <FormItemForParameter title={t('engraving-power', { type: '' }) + '(%)'} disabled={disabled}>
            <Controller
              name="vectorPower"
              control={control}
              render={({ field: { value, onChange } }) => (
                <CNumberInput2
                  min={MIN_VECTOR_POWER}
                  max={100}
                  value={value ?? defaultValue.vectorPower}
                  disabled={disabled}
                  onChange={(e, v) => onChange(v)}
                  {...focusHandler}
                />
              )}
            />
          </FormItemForParameter>

          <FormItemForParameter title={t('engraving-speed', { type: '' }) + '(mm/s)'} disabled={disabled}>
            <Controller
              name="vectorSpeed"
              control={control}
              render={({ field }) => (
                <CNumberInput2
                  min={MIN_VECTOR_SPEED}
                  max={6000}
                  value={field.value ?? defaultValue.vectorSpeed}
                  disabled={disabled}
                  onChange={(e, v) => field.onChange(v)}
                  {...focusHandler}
                />
              )}
            />
          </FormItemForParameter>
        </div>
      </div>
    )
  }, [objectTypeContainingSituation, store.workMode])

  const handleCreateParameter = async (form: FormType) => {
    setLoading(true)
    MaterialModule
      .createParameters({
        deviceModelId,
        materialId: store.material?.id,
        imageUrl: store.material?.imageUrl,
        description: store.material?.description,
        bitmapEnabled: 1,
        vectorEnabled: 1,
        name: form.name,
        vectorPower: form.vectorPower,
        vectorSpeed: form.vectorSpeed,
        bitmapPower: form.bitmapPower,
        bitmapSpeed: form.bitmapMarkTime,
        bitmapResolution: form.bitmapDPI,
      })
      .then(createdId => {
        toast.success(t('saved-success'))
        return refetchParameters()
          .then(result => onParameterRecover(createdId, result.data))
      })
      .finally(() => setLoading(false))
  }

  const handleUpdateParameter = async (form: FormType) => {
    setLoading(true)
    MaterialModule
      .updateParameter({
        id: form.parameterId,
        deviceModelId,
        materialId: store.material?.id,
        imageUrl: store.material?.imageUrl,
        description: store.material?.description,
        bitmapEnabled: 1,
        vectorEnabled: 1,
        name: form.name,
        vectorPower: form.vectorPower,
        vectorSpeed: form.vectorSpeed,
        bitmapPower: form.bitmapPower,
        bitmapSpeed: form.bitmapMarkTime,
        bitmapResolution: form.bitmapDPI,
      })
      .then(createdId => {
        toast.success(t('saved-success'))
        return refetchParameters()
          .then(result => onParameterRecover(createdId, result.data))
      })
      .finally(() => setLoading(false))
  }

  const handleDeleteParameter = useCallback(async () => {
    if (!parameterId) return
    setLoading(true)

    try {
      await MaterialModule.deleteParameter(parameterId)
      await refetchParameters()
      resetForm({
        materialId,
        parameterId: null,
        vectorPower: defaultValue.vectorPower,
        vectorSpeed: defaultValue.vectorSpeed,
        bitmapPower: defaultValue.bitmapPower,
        bitmapMarkTime: defaultValue.bitmapMarkTime,
        bitmapDPI: 0
      })
      toast(t('delete-success'))
    } finally {
      setLoading(false)
    }
  }, [parameterId, materialId])

  const enableCustomMode = useCallback(() => {
    onCustomModeChange(true)
    if (templateId && templateDetail?.engraveParam) {
      resetForm({
        materialId: Number(templateDetail.engraveParam.materialId),
        parameterId: Symbol('custom-button').toString() as any,
        vectorPower: templateDetail.engraveParam?.vectorPower ?? defaultValue.vectorPower,
        vectorSpeed: templateDetail.engraveParam?.vectorSpeed ?? defaultValue.vectorSpeed,
        bitmapPower: templateDetail.engraveParam?.bitmapPower ?? defaultValue.bitmapPower,
        bitmapMarkTime: templateDetail.engraveParam?.bitmapSpeed ?? defaultValue.bitmapMarkTime,
        bitmapDPI: templateDetail.engraveParam?.bitmapResolution ?? 0,
        name: '',
      })
    } else if (parsedRecordData) {
      resetForm({
        materialId: store.material?.id,
        parameterId: Symbol('custom-button').toString() as any,
        vectorPower: parsedRecordData.vectorPower ?? defaultValue.vectorPower,
        vectorSpeed: parsedRecordData.vectorSpeed ?? defaultValue.vectorSpeed,
        bitmapPower: parsedRecordData.bitmapPower ?? defaultValue.bitmapPower,
        bitmapMarkTime: parsedRecordData.bitmapSpeed ?? defaultValue.bitmapMarkTime,
        bitmapDPI: Number(parsedRecordData.dpi) ?? 0,
        name: '',
      })
    } else {
      resetForm({
        vectorPower: -1,
        vectorSpeed: -1,
        bitmapPower: -1,
        bitmapMarkTime: -1,
        bitmapDPI: null
      })

      resetForm({
        materialId: store.material?.id,
        parameterId: CUSTOM_SYMBOL.toString() as any,
        name: '',
        vectorPower: defaultValue.vectorPower,
        vectorSpeed: defaultValue.vectorSpeed,
        bitmapPower: defaultValue.bitmapPower,
        bitmapMarkTime: defaultValue.bitmapMarkTime,
        bitmapDPI: null,
      })
    }
  }, [templateId, templateDetail, parsedRecordData, store.material])
  const recentList = JSON.parse(localStorage.getItem(RECENT_ENGRAVING_PARAMS) || '[]')

  const sortedMaterials = useMemo(() => materials ? sortByRecent(materials, recentList, 'materialId') : materials, [materials, recentList])
  const sortedParameters = useMemo(() => parameters ? sortByRecent(parameters, recentList, 'parameterId') : parameters, [parameters, recentList])


  if (isFetching) return (
    <div className="flex justify-center items-center">
      <Loading />
    </div>
  )

  return (
    <div className="overflow-hidden flex flex-col bg-white px-6 py-6">
      <div className="overflow-scroll flex flex-col gap-5">
        <FormItem
          title={t('common.material-type')}
          onClickMore={() => setActiveMenuKey(EngravingParameterMenuKeys.moreMaterial)}
        >
          <div className="w-full">
            <Controller
              name="materialId"
              control={control}
              render={({ field: { value } }) => (
                <ButtonRadio
                  value={value}
                  className="gap-3"
                  onChange={v => onMaterialSelected(Number(v))}
                >
                  {sortedMaterials?.map(material => (
                    <ButtonRadioItem2
                      key={material.id}
                      value={material.id}
                    >
                      {material.name}
                    </ButtonRadioItem2>
                  ))}
                </ButtonRadio>
              )}
            />
          </div>
        </FormItem>

        <FormItem
          hidden={isNil(materialId)}
          title={t('engraving-parameter')}
          onClickMore={() => {
            if (!store.material?.id) return toast(t('parameter-message-hint'))
            if (!parameters || parameters.length === 0) return toast(t('no-parameters-of-material'))
            setActiveMenuKey(EngravingParameterMenuKeys.moreParameters)
          }}
        >
          <div className="w-full flex flex-row">
            <Controller
              name="parameterId"
              control={control}
              render={({ field: { value } }) => (
                <ButtonRadio
                  value={value}
                  className="gap-3"
                  onChange={
                    v => v === CUSTOM_SYMBOL.toString()
                      ? enableCustomMode()
                      : onParameterRecover(v)
                  }
                >
                  {sortedParameters?.map(parameter => (
                    <ButtonRadioItem2 key={parameter.id} value={parameter.id}>
                      {parameter.name}
                    </ButtonRadioItem2>
                  ))}
                  <ButtonRadioItem2 value={CUSTOM_SYMBOL.toString()}>
                    + {t('custom')}
                  </ButtonRadioItem2>
                </ButtonRadio>
              )}
            />
          </div>
        </FormItem>

        {(parameterId || customMode) && (
          <div className="flex flex-row justify-between items-start gap-4">
            {bitmapFormItems}
            {vectorFormItems}
          </div>
        )}
      </div>

      <div className="flex flex-col gap-3 pt-2 relative">
        {(parameterId || customMode) && (
          <div className={clsx((customMode || isDirty) ? 'flex-col' : 'hidden')}>
            <FormItem title={t('parameter-name')}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    className="mt-1"
                    placeholder={t('placeholder', { name: t('parameter-name') })}
                    value={field.value || ''}
                    onChange={e => {
                      if (e.target.value.length > 20) {
                        return toast(t('max-length-hint', { max: 20 }))
                      }
                      field.onChange(e.target.value)

                    }}
                    {...focusHandler}
                  />
                )}
              />
            </FormItem>
          </div>
        )}

        <div className="flex justify-end gap-3 absolute top-1 right-0">
          {currentParameter?.type === MaterialTypeCode[MaterialTypeEnum.customize] && (
            <div className="flex flex-row gap-3">
              <button
                className="bg-transparent rounded-full border border-gray-400 text-gray-500 flex items-center justify-center text-xs px-1.5 py-1 gap-0.5"
                onClick={() => handleDeleteParameter()}
              >
                <DeleteIcon className="size-4" />
                {t('common.delete')}
              </button>

              {isDirty && (
                <button
                  onClick={handleSubmit(handleUpdateParameter)}
                  className="bg-transparent rounded-full border border-primary flex items-center justify-center text-primary text-xs px-1.5 py-1 gap-0.5"
                >
                  <SaveIcon className="size-4" />
                  {t('save')}
                </button>
              )}
            </div>
          )}

          {(isDirty && currentParameter?.type === MaterialTypeCode[MaterialTypeEnum.default] || customMode) && (
            <>
              {/*<button*/}
              {/*  className="bg-transparent rounded-full border border-primary flex items-center justify-center text-primary text-xs px-1.5 py-1 gap-0.5"*/}
              {/*  onClick={() => recoverParameter(parameterId)}*/}
              {/*>*/}
              {/*  <RecoverIcon className="size-4" />*/}
              {/*  {t('recover')}*/}
              {/*</button>*/}
              <button
                onClick={handleSubmit(handleCreateParameter)}
                className="bg-transparent rounded-full border border-primary flex items-center justify-center text-primary text-xs px-1.5 py-1 gap-0.5"
              >
                <SaveIcon className="size-4" />
                {customMode ? t('save') : t('saveAs')}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default function Parameter() {
  const { goBackwardToDesign } = useCanvasMenu()
  const { t } = useTranslation()
  const store = useEngravingParametersStore()
  const { deviceModelId } = useDeviceStatusStore()
  const { templateId, recordDataJson } = useURLSearchConfig()
  const form = useForm<FormType>({})
  const { watch, reset: resetForm } = form

  // @ts-ignore
  const [parameterId] = watch(['parameterId'])

  const [inputFocused, setInputFocused] = useState(false)
  const [customMode, setCustomMode] = useState<boolean>(false)

  const { data: materials, refetch: refetchMaterial } = useQueryMaterials()
  const { data: parameters, isFetched } = useQueryParameters({ material: store.material, deviceModelId })
  const { data: templateDetail } = useQueryTemplate(templateId ?? '')
  const parametersRef = useStateToRef(parameters)

  const [pendingParameterId, setPendingParameterId] = useState<number | null>(null)
  const hasCreatedMaterial = useRef(false)

  const recoverParameter = (parameterId: string | number | null, _parameters?: MaterialParameter[]) => {
    if (!_parameters) {
      _parameters = parametersRef.current as MaterialParameter[] | undefined
    }

    const found = _parameters?.find(p => String(p.id) === String(parameterId))

    if (found) {
      store.setFormItem('parameter', found)
      setCustomMode(false)

      resetForm({
        vectorPower: -1,
        vectorSpeed: -1,
        bitmapPower: -1,
        bitmapMarkTime: -1,
        bitmapDPI: null
      })

      resetForm({
        name: found?.name,
        parameterId: found.id,
        materialId: store.material?.id,
        bitmapDPI: found?.bitmapResolution,
        vectorPower: found.vectorPower ?? undefined,
        vectorSpeed: found.vectorSpeed ?? undefined,
        bitmapPower: found.bitmapPower ?? undefined,
        bitmapMarkTime: found.bitmapSpeed ?? undefined,
      }, { keepDirty: false })
    }
  }

  const handleSelectMaterial = useCallback((id: number, parameterId?: number, newMaterials?: Material[]) => {
    const diameterData = store.diameter ? store.diameter : -1

    resetForm({
      materialId: id,
      parameterId: null,
      vectorPower: defaultValue.vectorPower,
      vectorSpeed: defaultValue.vectorSpeed,
      bitmapPower: defaultValue.bitmapPower,
      bitmapMarkTime: defaultValue.bitmapMarkTime,
      bitmapDPI: 0
    })
    store.reset()

    if (diameterData !== -1) {
      store.setFormItem('diameter', diameterData)
    }

    if (parameterId === -99) {
      store.setFormItem('material', newMaterials?.find(m => m.id === id))
      store.setFormItem('parameter', null)
      setPendingParameterId(parameterId)
    } else if (parameterId === -1 || parameterId) {
      store.setFormItem('material', materials?.find(m => m.id === id))
      store.setFormItem('parameter', null)
      setPendingParameterId(parameterId)
    } else {
      store.setFormItem('material', materials?.find(m => m.id === id))
      store.setFormItem('parameter', null)
    }

  }, [store.setFormItem, store.reset, store.diameter, materials])

  const currentParameter = useMemo(() => parameters?.find(o => o?.id === parameterId), [parameters, parameterId])

  useEffect(() => {
    if (pendingParameterId && isFetched && recordDataJson) {
      const parsedRecordData = safeParseJson(recordDataJson)

      const resetToCustomForm = customData => {
        setCustomMode(true)
        resetForm({
          materialId: store.material?.id ?? undefined,
          parameterId: Symbol('custom-button').toString() as any,
          vectorPower: customData.vectorPower ?? defaultValue.vectorPower,
          vectorSpeed: customData.vectorSpeed ?? defaultValue.vectorSpeed,
          bitmapPower: customData.bitmapPower ?? defaultValue.bitmapPower,
          bitmapMarkTime: customData.bitmapSpeed ?? defaultValue.bitmapMarkTime,
          bitmapDPI: Number(customData.dpi) ?? 0,
        })
      }

      // 实际雕刻参数与预设参数具体数值是否一致
      const isUnsavedParameterChange = (existingParameter, actualParameters) => {
        return (
          existingParameter.vectorPower !== actualParameters.vectorPower ||
                existingParameter.vectorSpeed !== actualParameters.vectorSpeed ||
                existingParameter.bitmapPower !== actualParameters.bitmapPower ||
                existingParameter.bitmapSpeed !== actualParameters.bitmapSpeed ||
                existingParameter.bitmapResolution !== Number(actualParameters.dpi)
        )
      }

      if (parameters && parameters.some(parameter => parameter.id === pendingParameterId) || !parsedRecordData) {
        // 参数存在当前参数列表中
        recoverParameter(pendingParameterId)

        // 选择了预设参数，但改变了数值后没有保存直接进行雕刻
        if (parsedRecordData && parameters) {
          const recordParameters = parameters.find(item => item.id === parsedRecordData.parameterInfo.id)
          if (isUnsavedParameterChange(recordParameters, parsedRecordData)) {
            resetToCustomForm(parsedRecordData)
          }
        }

      } else {
        // 参数不存在当前参数列表中，作为自定义参数显示
        resetToCustomForm(parsedRecordData)
      }

      setPendingParameterId(null)
    }
  }, [isFetched, parameters, pendingParameterId, recordDataJson])

  useEffect(() => {
    const { unsubscribe } = watch(formValue => {
      const { bitmapMarkTime, bitmapPower, vectorSpeed, vectorPower, bitmapDPI } = formValue
      store.setFormItem('vectorSpeed', vectorSpeed)
      store.setFormItem('vectorPower', vectorPower)
      store.setFormItem('bitmapPower', bitmapPower)
      store.setFormItem('bitmapMarkTime', bitmapMarkTime)
      store.setFormItem('bitmapDPI', bitmapDPI)
    })

    return unsubscribe
  }, [watch])

  useEffect(() => {
    if (!templateId || !templateDetail?.engraveParam?.materialId) return
    handleSelectMaterial(Number(templateDetail.engraveParam.materialId))
    setCustomMode(true)
    resetForm({
      materialId: Number(templateDetail.engraveParam.materialId),
      parameterId: Symbol('custom-button').toString() as any,
      vectorPower: templateDetail.engraveParam?.vectorPower ?? defaultValue.vectorPower,
      vectorSpeed: templateDetail.engraveParam?.vectorSpeed ?? defaultValue.vectorSpeed,
      bitmapPower: templateDetail.engraveParam?.bitmapPower ?? defaultValue.bitmapPower,
      bitmapMarkTime: templateDetail.engraveParam?.bitmapSpeed ?? defaultValue.bitmapMarkTime,
      bitmapDPI: templateDetail.engraveParam?.bitmapResolution ?? 0
    })
  }, [templateId, templateDetail])

  useEffect(() => {
    if (!recordDataJson || !materials || hasCreatedMaterial.current) return

    const beforeCreateMaxTime = Math.max(...(materials ?? []).map(m => m.createTime ?? 0))
    const timer = requestAnimationFrame(() => {
      try {
        const {
          materialInfo,
          materialName,
          parameterInfo
        } = JSON.parse(recordDataJson)

        if (materials.some(material => material.id === materialInfo.id)) {  // 是预设参数
          if (parameterInfo) { // 参数存在
            handleSelectMaterial(Number(materialInfo.id), Number(parameterInfo.id))
          } else { // 参数已删除
            handleSelectMaterial(Number(materialInfo.id), -1)
          }
        } else {
          hasCreatedMaterial.current = true

          //自定义材料
          MaterialModule
            .createMaterial({ name: materialName })
            .then(() => refetchMaterial())
            .then(res => {
              const newMaterials = res.data

              const created = newMaterials
                ? newMaterials.find(m => m.createTime > beforeCreateMaxTime && m.name === materialName)
                : undefined

              if (created) {
                store.setFormItem('material', created)
                handleSelectMaterial(created.id, -99, newMaterials)
              } else {
                console.warn('未能根据 createTime 和 name 找到新创建的材料')
              }
            }).catch(e => {
              console.error('创建材料失败:', e)
              toast.error(t('save-failed'))
            })
        }
      } catch (e) {
        console.error('recordDataJson 解析失败:', e)
      }
    })

    return () => cancelAnimationFrame(timer)
  }, [recordDataJson, materials])

  return (
    <div className="bg-[#FAFAFA] flex flex-col max-h-screen overflow-y-hidden h-full">
      <div className="bg-white" style={{ height: `${PageHeaderHeight}px` }}>
        <div className="flex w-full justify-between items-center px-4 py-3">
          <div className="flex-1">
            <BackIcon onClick={goBackwardToDesign} />
          </div>
          <div className="text-base font-bold">{t(NameByMenuKey[EngravingMenuKeys.parameter])}</div>
          <div className="flex-1 h-1" />
        </div>
      </div>

      <ParameterBody
        form={form}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        onParameterRecover={recoverParameter}
        onMaterialSelected={handleSelectMaterial}
        customMode={customMode}
        currentParameter={currentParameter}
        onCustomModeChange={setCustomMode}
      />

      {!inputFocused && (
        <Fragment>
          <div className="h-5 w-full" />
          <div className="flex-1 flex flex-col justify-end">
            <Footer parameter={customMode && templateId ? templateDetail?.engraveParam : currentParameter} />
          </div>
        </Fragment>
      )}

      <Fragment>
        <DrawerContainer duration={0} menuKey={EngravingParameterMenuKeys.moreMaterial}>
          <MoreMaterials onSelect={id => handleSelectMaterial(id)} />
        </DrawerContainer>

        <DrawerContainer duration={0} menuKey={EngravingParameterMenuKeys.moreParameters}>
          <MoreParameters onSelect={id => recoverParameter(id)} />
        </DrawerContainer>
      </Fragment>
    </div>
  )
}
