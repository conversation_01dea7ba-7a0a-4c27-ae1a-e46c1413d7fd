import React, { FC, PropsWithChildren, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDeviceCommandSender } from '@/hooks/useDeviceCommandSender'
import { PageHeaderHeight } from '@/constants/styles'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { useTranslation } from 'react-i18next'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'
import { DeviceWorkingStatus, MessageAction, TransmitMode, WorkMode } from '@/libs/shared/types'
import clsx from 'clsx'
import { useDeviceStatusStore } from '@/stores/device-status'

import UploadIcon from '@/assets/icons/upload.svg?react'
import CompleteIcon from '@/assets/icons/complete.svg?react'
import { sendMessageToNative } from '@/utils/message'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { RowFormControl } from '@/components/ui/form'
import { ObjectTypeContainingSituation } from '@/libs/fabricjs-react/constants'
import { useFabric } from '@/contexts/fabric.context'
import toast from 'react-hot-toast'
import { formatBytes } from '@/utils/string'
import { throttle } from 'lodash'
import { useQueryTemplate } from '@/hooks/queries/useQueryTemplate'
import { NumberInput } from '@/components/ui/number-input'

const buttonCommonClassNames = 'rounded-full w-full text-sm font-semibold h-10'

const InfoItem: FC<PropsWithChildren<{ label: string }>> = ({ label, children }) => {
  return (
    <div className="grid grid-cols-[1fr_4px_auto] justify-between items-center" key={label}>
      <div className="text-xs text-disabled font-light text-start max-w-[90px]">{label}</div>
      <div />
      {
        typeof children === 'string'
          ? <div className="text-xs text-[#666] font-light text-end max-xs:w-16">{children}</div>
          : children
      }
    </div>
  )
}

const useLabelByParameterFieldName = () => {
  const { t } = useTranslation()

  return useMemo(() => ({
    vectorSpeed: t('engraving-speed-of', { type: t('common.vector') }),
    vectorPower: t('engraving-power-of', { type: t('common.vector') }),
    bitmapMarkTime: t('mark-time-of', { type: t('common.bitmap') }),
    bitmapPower: t('engraving-power-of', { type: t('common.bitmap') }),
    bitmapDPI: t('resolution')
  }), [])
}

const EngravingCoreParameterItems: FC = () => {
  const parameters = useEngravingParametersStore()
  const labelByParameterFieldName = useLabelByParameterFieldName()
  const { derivedState: { objectTypeContainingSituation: contained } } = useFabric()

  const isPlaneMode = parameters.workMode === WorkMode.PLANE || parameters.workMode === WorkMode.HANDHELD

  const { containVector, containBitmap } = useMemo(() => {
    return {
      containVector: !!(contained & ObjectTypeContainingSituation.VECTOR_ONLY),
      containBitmap: !!(contained & ObjectTypeContainingSituation.BITMAP_ONLY)
    }
  }, [contained])

  return (
    <>
      {!isPlaneMode ? (
        <>
          <InfoItem label={labelByParameterFieldName.bitmapPower}>
            {`${parameters.bitmapPower || 0}%`}
          </InfoItem>
          <InfoItem label={labelByParameterFieldName.bitmapMarkTime}>
            {`${parameters.bitmapMarkTime || 0} μs`}
          </InfoItem>
          <InfoItem label={labelByParameterFieldName.bitmapDPI}>
            {parameters.bitmapDPI ? `${parameters.bitmapDPI} DPI` : null}
          </InfoItem>
        </>
      ) : (
        <>
          {containVector && (
            <>
              <InfoItem label={labelByParameterFieldName.vectorPower}>
                {`${parameters.vectorPower || 0}%`}
              </InfoItem>
              <InfoItem label={labelByParameterFieldName.vectorSpeed}>
                {`${parameters.vectorSpeed || 0} mm/s`}
              </InfoItem>
            </>
          )}

          {containBitmap && (
            <>
              <InfoItem label={labelByParameterFieldName.bitmapPower}>
                {`${parameters.bitmapPower || 0}%`}
              </InfoItem>
              <InfoItem label={labelByParameterFieldName.bitmapMarkTime}>
                {`${parameters.bitmapMarkTime || 0} μs`}
              </InfoItem>
              <InfoItem label={labelByParameterFieldName.bitmapDPI}>
                {parameters.bitmapDPI ? `${parameters.bitmapDPI} DPI` : null}
              </InfoItem>
            </>
          )}
        </>
      )}
    </>
  )
}

const EngravingTimeCostingInfoItems: FC = () => {
  const { t } = useTranslation()

  const {
    estimatedEngravingTime,
    engravingTime,
    deviceCurrentState,
  } = useDeviceStatusStore()

  const formattedTime = useMemo(() => {
    return deviceCurrentState === DeviceWorkingStatus.FINISHED
      ? (engravingTime / 1000).toFixed(1)
      : Math.floor(engravingTime / 1000).toFixed(0)
  }, [engravingTime, deviceCurrentState])

  return (
    <>
      <InfoItem label={t('common.time-consuming', { type: t('common.estimated') })}>
        {`${(estimatedEngravingTime / 1000).toFixed(1)} ${t('seconds')}`}
      </InfoItem>

      <InfoItem label={t('common.time-consuming', { type: t('common.actual') })}>
        {`${formattedTime} ${t('seconds')}`}
      </InfoItem>
    </>
  )
}

const StopButton: FC<PropsWithChildren> = ({ children }) => {
  const { sendStopEngravingCommand } = useDeviceCommandSender()

  return (
    <button
      className={clsx(
        'border border-danger text-danger', buttonCommonClassNames,
      )}
      onClick={() => sendStopEngravingCommand()}
    >
      {children}
    </button>
  )
}

const PreparePanel: FC = () => {
  const { t } = useTranslation()
  const parameters = useEngravingParametersStore()
  const { deviceCurrentState } = useDeviceStatusStore()
  const { sendEngravingCommand } = useDeviceCommandSender()
  const labelByParameterFieldName = useLabelByParameterFieldName()
  const { editor, derivedState: { objectTypeContainingSituation: contained, layersBoundarySize } } = useFabric()

  const [commandSending, setCommandSending] = useState(false)

  const isPlaneMode = useMemo(() => parameters.workMode === WorkMode.PLANE || parameters.workMode === WorkMode.HANDHELD, [parameters.workMode])
  const isPrepared = useMemo(() => deviceCurrentState === DeviceWorkingStatus.PREPARED || commandSending, [deviceCurrentState, commandSending])

  const invalidField = useMemo(() => {
    if (!isPlaneMode) {
      if (!parameters.bitmapPower) return labelByParameterFieldName.bitmapPower
      if (!parameters.bitmapMarkTime) return labelByParameterFieldName.bitmapMarkTime
      if (!parameters.bitmapDPI) return labelByParameterFieldName.bitmapDPI
    } else {
      if (contained & ObjectTypeContainingSituation.BITMAP_ONLY) {
        if (!parameters.bitmapPower) return labelByParameterFieldName.bitmapPower
        if (!parameters.bitmapMarkTime) return labelByParameterFieldName.bitmapMarkTime
        if (!parameters.bitmapDPI) return labelByParameterFieldName.bitmapDPI
      }

      if (contained & ObjectTypeContainingSituation.VECTOR_ONLY) {
        if (!parameters.vectorPower) return labelByParameterFieldName.vectorPower
        if (!parameters.vectorSpeed) return labelByParameterFieldName.vectorSpeed
      }
    }
    return null
  }, [parameters, contained])

  const enabled = !invalidField

  const { templateId } = useURLSearchConfig()
  const { data: templateDetail } = useQueryTemplate(templateId ?? '')

  const startButton = useMemo(() => (
    <button
      disabled={!enabled}
      className={clsx(
        buttonCommonClassNames,
        !enabled && 'grayscale',
        isPrepared ? 'bg-primary/30 text-gray-100' : 'bg-primary text-white'
      )}
      onClick={() => {
        if (!enabled) return
        setCommandSending(true)
        sendEngravingCommand()
      }}
    >
      {isPrepared ? t('starting') : t('start-engraving')}
    </button>
  ), [enabled, isPrepared, sendEngravingCommand])

  useEffect(() => {
    if (templateId && templateDetail?.engraveParam?.engraveNumber) {
      parameters.setFormItem('numberOfEngraving', templateDetail.engraveParam?.engraveNumber)
    }
  }, [templateId, templateDetail])

  return (
    <div className="bg-white px-6 py-4 shadow-default rounded-t-xl">
      <div className="grid grid-cols-2 gap-x-8 max-xs:gap-x-4 gap-y-2 mb-6">
        <InfoItem label={t('common.material')}>{parameters.material?.name}</InfoItem>

        <InfoItem label={t('size-of-content')}>
          {`${editor?.pixelsToUnit(layersBoundarySize.width).toFixed(2)} × ${editor?.pixelsToUnit(layersBoundarySize.height).toFixed(2)}${parameters.unit}`}
        </InfoItem>

        <EngravingCoreParameterItems />
        <InfoItem label={t('engraving-mode')}>
          {t(parameters.workMode)}
        </InfoItem>
        {parameters.workMode === WorkMode.ROTATION && parameters.diameter && (
          <InfoItem label={t('common.diameter')}>
            {`${parameters.diameter} ${t(parameters.unit)}`}
          </InfoItem>
        )}
        {parameters.workMode === WorkMode.TABLET && parameters.length && (
          <InfoItem label={t('common.length')}>
            {`${parameters.length} ${t(parameters.unit)}`}
          </InfoItem>
        )}
      </div>

      <div className="mb-3">
        <InfoItem label={t('engraving-times')}>
          <NumberInput
            min={1}
            max={10}
            value={parameters.numberOfEngraving}
            onValueChange={v => parameters.setFormItem('numberOfEngraving', v)}
          />
        </InfoItem>
      </div>

      {startButton}

      {invalidField && (
        <div className="text-xs text-red-400 font-semibold text-center mt-2">
          {t('hints.invalid-form-field', { field: invalidField })}
        </div>
      )}
    </div>
  )
}

const WorkingPanel: FC = () => {
  const { sendPauseEngravingCommand, sendResumeEngravingCommand } = useDeviceCommandSender()

  const { t } = useTranslation()
  const {
    engravingProgress,
    deviceCurrentState,
  } = useDeviceStatusStore()

  const isPausing = deviceCurrentState === DeviceWorkingStatus.PAUSING

  const pauseOrResumeButton = useMemo(() => (
    <button
      className={clsx(
        'border border-primary', buttonCommonClassNames,
        isPausing ? 'bg-primary text-white' : 'bg-transparent text-primary'
      )}
      onClick={() => {
        if (isPausing) {
          sendResumeEngravingCommand()
        } else {
          sendPauseEngravingCommand()
        }
      }}
    >
      {isPausing ? t('continue-engraving') : t('pause-engraving')}
    </button>
  ), [isPausing, sendResumeEngravingCommand, sendPauseEngravingCommand])

  return (
    <div
      className="px-6 py-4 shadow-default rounded-t-xl"
      style={{ background: 'linear-gradient(116.81deg, #FEF4E6 0%, #FFFFFF 41.52%)' }}
    >
      <div className="flex gap-2">
        <div className="bg-white flex justify-center items-center size-8 rounded">
          <UploadIcon className="size-" />
        </div>
        <div className="w-full flex flex-col items-start">
          <div className="text-xs font-semibold mb-2">{t('engraving-progress')}</div>
          <div
            className="w-full grid grid-cols-2 gap-x-8 max-xs:gap-x-4 justify-between text-xs text-gray-400 gap-y-1.5 mb-3"
          >
            <EngravingCoreParameterItems />
            <EngravingTimeCostingInfoItems />
          </div>

          <div className="w-full rounded-full bg-[#F2F3F5] h-[18px] mb-3 relative">
            <div
              className={clsx(
                'absolute rounded-full h-full flex items-center justify-start pl-1',
                'transition-all duration-[0.5s]',
                'bg-primary'
              )}
              style={{ width: `${Math.min(100, Math.max(engravingProgress, 8))}%` }}
            >
              <div className="text-xs text-white">{engravingProgress}%</div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-3 w-full">
        <StopButton>
          {t('cancel-engraving')}
        </StopButton>
        {pauseOrResumeButton}
      </div>
    </div>
  )
}

const CompletedPanel: FC = () => {
  const { sendEngravingCommand } = useDeviceCommandSender()

  const { t } = useTranslation()
  const { goBackwardToDesign } = useCanvasMenu()
  const parameters = useEngravingParametersStore()
  const { engravingProgress } = useDeviceStatusStore()
  const originalNumberRef = useRef(parameters.numberOfEngraving)

  const backToHomeButton = useMemo(() => (
    <button
      className={clsx('bg-[#EEEEEE] text-[#999999]', buttonCommonClassNames)}
      onClick={() => sendMessageToNative(MessageAction.GO_BACK_HOME)}
    >
      {t('go-back-home')}
    </button>
  ), [])

  const reeditButton = useMemo(() => (
    <button
      className={clsx('border border-primary text-primary', buttonCommonClassNames)}
      onClick={() => goBackwardToDesign()}
    >
      {t('edit-again')}
    </button>
  ), [goBackwardToDesign])

  const handleEngraveAgain = useCallback(() => {
    if (parameters.numberOfEngraving !== originalNumberRef.current) {
      void sendEngravingCommand()
    } else {
      void sendEngravingCommand({ isRepeat: true })
    }
  }, [parameters, sendEngravingCommand])

  const engraveAgainButton = useMemo(() => (
    <button
      className={clsx('border border-primary text-primary', buttonCommonClassNames)}
      onClick={handleEngraveAgain}
    >
      {t('engrave-again')}
    </button>
  ), [handleEngraveAgain])

  return (
    <div
      className="px-6 py-4 shadow-default rounded-t-xl"
      style={{ background: 'linear-gradient(116.81deg, #e8f9f5 0%, #FFFFFF 41.52%)' }}
    >
      <div className="flex gap-2">
        <div className="bg-white flex justify-center items-center size-8 rounded">
          <CompleteIcon />
        </div>
        <div className="w-full flex flex-col items-start">
          <div className="text-xs font-semibold mb-2">{t('engraving-completed')}</div>
          <div className="w-full grid grid-cols-2 gap-x-8 max-xs:gap-x-4 justify-between gap-y-1.5 mb-3">
            <EngravingCoreParameterItems />
            <EngravingTimeCostingInfoItems />
          </div>

          <div className="w-full rounded-full bg-[#F2F3F5] h-[18px] mb-3 relative">
            <div
              className={clsx(
                'absolute rounded-full h-full flex items-center justify-start pl-1',
                'transition-all duration-[0.5s]',
                'bg-[#16C098]'
              )}
              style={{ width: `${Math.min(100, Math.max(engravingProgress, 8))}%` }}
            >
              <div className="text-xs text-white">{engravingProgress}%</div>
            </div>
          </div>

        </div>
      </div>

      <div className="flex justify-between items-center border-b py-4 mb-4 w-full">
        <RowFormControl>
          <div className="text-nowrap mr-16">{t('times-to-re-engrave')}</div>
          <NumberInput
            min={1}
            max={10}
            value={parameters.numberOfEngraving}
            onValueChange={v => parameters.setFormItem('numberOfEngraving', v)}
          />
        </RowFormControl>
      </div>

      <div className="grid grid-cols-[2fr_3fr_3fr] gap-3 w-full">
        {backToHomeButton}{reeditButton}{engraveAgainButton}
      </div>
    </div>
  )
}

const DataTransmittingPanel: FC = () => {
  const { t } = useTranslation()
  const { transmittingProgress = 0, dataSize } = useDeviceStatusStore()
  const { workMode } = useEngravingParametersStore()
  const isHandHeldMode = workMode === WorkMode.HANDHELD

  const transmissionFinished = useMemo(() => transmittingProgress >= 100, [transmittingProgress])

  useEffect(() => {
    if (transmittingProgress === 100 && isHandHeldMode) {
      sendMessageToNative(MessageAction.READY_ENGRAVING)
    }
  }, [transmittingProgress, isHandHeldMode])

  return (
    <div
      className="px-6 py-4 shadow-default rounded-t-xl"
      style={{ background: 'linear-gradient(116.81deg, #FEF4E6 0%, #FFFFFF 41.52%)' }}
    >
      <div className="flex gap-2">
        <div className="bg-white flex justify-center items-center size-8 rounded">
          <UploadIcon />
        </div>
        <div className="w-full flex flex-col items-start">
          <div className="text-xs font-semibold mb-2">{t('transmission-progress')}</div>
          <div
            className="w-full grid grid-cols-[repeat(4,72px)] justify-between text-xs text-gray-400 gap-y-1.5 mb-3"
          >
            <div>{t('data-size')}</div>
            <div>{typeof dataSize === 'number' ? formatBytes(dataSize) : '-'}</div>
          </div>

          <div className="w-full rounded-full bg-[#F2F3F5] h-[18px] mb-3 relative">
            <div
              className={clsx('absolute rounded-full h-full flex items-center justify-start pl-1',  'bg-primary')}
              style={{ width: `${Math.min(100, Math.max(transmittingProgress, 8))}%` }}
            >
              <div className="text-xs text-white">{transmittingProgress}%</div>
            </div>
          </div>

          <div className="w-full flex flex-col gap-2">
            {isHandHeldMode && transmissionFinished && (
              <button
                disabled={!transmissionFinished}
                className={clsx(
                  buttonCommonClassNames,
                  !transmissionFinished && 'grayscale',
                  'bg-primary text-white'
                )}
                onClick={() => sendMessageToNative(MessageAction.READY_ENGRAVING)}
              >
                {t('readyOk')}
              </button>
            )}

            <StopButton>
              {transmissionFinished ? t('cancel-engraving') : t('cancel-transmit')}
            </StopButton>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function Engraving() {
  const { t } = useTranslation()
  const { safe_area_top, safe_area_bottom } = useURLSearchConfig()
  const { deviceCurrentState, engravingProgress, engravingTime } = useDeviceStatusStore()
  const { workMode } = useEngravingParametersStore()
  const isHandHeldMode = workMode === WorkMode.HANDHELD

  const showToast = throttle(() => {
    return toast(t('engrave-page-disable-hint'), {
      position: 'top-center',
      duration: 750,
    })
  }, 1000, { trailing: false })

  const isTransmitting = deviceCurrentState === DeviceWorkingStatus.DATA_TRANSMITTING
  const isEngraving = deviceCurrentState === DeviceWorkingStatus.ENGRAVING
  const isPausing = deviceCurrentState === DeviceWorkingStatus.PAUSING
  const isCompleted = deviceCurrentState === DeviceWorkingStatus.FINISHED
    || (deviceCurrentState === DeviceWorkingStatus.CANCELED && (engravingProgress > 0 || engravingTime > 0))

  const transmitMode = useMemo(() => window.injected?.device?.configurations.transmitMode, [])

  return (
    <div
      className="flex flex-col"
      style={{ height: window.innerHeight - Number(safe_area_top) - PageHeaderHeight }}
    >
      <div className="flex-1 bg-black/30 transition-colors" onTouchStart={showToast} />

      {
        isTransmitting && (transmitMode !== TransmitMode.TRANS_UNITE_MARK || isHandHeldMode)
          ? <DataTransmittingPanel />
          : isCompleted
            ? <CompletedPanel />
            : isEngraving || isPausing
              ? <WorkingPanel />
              : <PreparePanel />
      }
    </div>
  )
}
