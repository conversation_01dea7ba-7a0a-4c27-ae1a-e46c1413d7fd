
import align_left from '@/assets/icons/align_left.svg?react'
import align_center from '@/assets/icons/align_center.svg?react'
import align_right from '@/assets/icons/align_right.svg?react'
import align_middle from '@/assets/icons/align_center_2.svg?react'
import align_top from '@/assets/icons/align_top.svg?react'
import align_bottom from '@/assets/icons/align_bottom.svg?react'
import { useFabric } from '@/contexts/fabric.context'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { AlignType } from '@/libs/fabricjs-react/plugins/vector.plugin'
import { useState } from 'react'

export default function Align() {
  const { t } = useTranslation()
  const { editor } = useFabric()
  const [currentAlignMode, setCurrentAlignMode] = useState<AlignType>()

  const options = [
    { label: t('canvas-align.left'), icon: align_left, value: 'left' },
    { label: t('canvas-align.center'), icon: align_center, value: 'center' },
    { label: t('canvas-align.right'), icon: align_right, value: 'right' },
    { label: t('canvas-align.top'), icon: align_top, value: 'top' },
    { label: t('canvas-align.middle'), icon: align_middle, value: 'middle' },
    { label: t('canvas-align.bottom'), icon: align_bottom, value: 'bottom', },
  ]

  return (
    <div className="flex gap-6 p-3 fadein-popup-handler-content">
      <div className="flex gap-4 overflow-x-auto w-full">
        {options
          .map(option => ({ ...option, active: option.value === currentAlignMode }))
          .map(({ icon: Icon, value, active, label }) => (
            <div
              key={value}
              className="flex flex-col gap-3 items-center flex-1 text-center text-nowrap"
              onClick={() => {
                editor.alignObjectsToCanvas(value)
                setCurrentAlignMode(value as AlignType)
              }}
            >
              <Icon className="size-6" fill={active ? '#F18D00' : '#333'} />
              <div className={clsx('text-xs', active ? 'text-primary' : 'text-[#333]')}>
                {label}
              </div>
            </div>
          ))}
      </div>

    </div>
  )
}
