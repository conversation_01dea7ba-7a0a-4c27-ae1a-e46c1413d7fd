import { useFabric } from '@/contexts/fabric.context'
import React, { useEffect, useMemo, useState  } from 'react'
import clsx from 'clsx'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'
import { useTranslation } from 'react-i18next'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { ButtonRadio, ButtonRadioItem } from '@/components/ui/ButtonRadio'
import { DeviceWorkingStatus, PreviewMode, WorkMode } from '@/libs/shared/types'
import { useDeviceStatusStore } from '@/stores/device-status'
import { ObjectTypeContainingSituation } from '@/libs/fabricjs-react/constants'
import { useMutation } from '@tanstack/react-query'
import { useDeviceCommandSender } from '@/hooks/useDeviceCommandSender'
import { LayerBasicPropsController } from '@/components/LayerBasicPropsController'

export default function PreviewCard() {
  const { t } = useTranslation()

  const { editor, derivedState: { objectTypeContainingSituation: situation } } = useFabric()
  const { inactivate } = useCanvasMenu()

  const { sendPreviewCommand, sendStopPreviewCommand } = useDeviceCommandSender()
  const { deviceCurrentState } = useDeviceStatusStore()
  const isPreviewing = useMemo(() => deviceCurrentState === DeviceWorkingStatus.PREVIEWING, [deviceCurrentState])
  const [hasMounted, setHasMounted] = useState(false)
  const { setFormItem: setParameter, previewMode, workMode } = useEngravingParametersStore()

  const options = useMemo<{ label: string; value: string }[]>(() => {
    const OPTION_SINGLE_RECT = { label: t('maximum-border'), value: PreviewMode.PREVIEW_SINGLE_RECT }
    const OPTION_PATH = { label: t('engraving-path'), value: PreviewMode.PREVIEW_PATH }

    if (situation === ObjectTypeContainingSituation.BOTH) {
      return [OPTION_SINGLE_RECT]
    }

    if ((situation === ObjectTypeContainingSituation.VECTOR_ONLY) && (workMode === WorkMode.PLANE || workMode === WorkMode.HANDHELD)) {
      return [OPTION_SINGLE_RECT, OPTION_PATH]
    }

    if ((situation === ObjectTypeContainingSituation.BITMAP_ONLY) || (workMode !== WorkMode.PLANE && workMode !== WorkMode.HANDHELD)) {
      return [OPTION_SINGLE_RECT]
    }

    return []
  }, [situation])

  const { mutateAsync } = useMutation({
    mutationFn: () => {
      if (isPreviewing) {
        inactivate()
        return sendStopPreviewCommand()
      } else {
        return sendPreviewCommand(previewMode)
      }
    },
  })

  useEffect(() => {
    setParameter('previewMode', options[0].value as PreviewMode)
    setHasMounted(true)
    sendPreviewCommand(options[0].value)
  }, [])

  useEffect(() => {
    if (!hasMounted) return

    if (!situation || !isPreviewing) {
      inactivate()
      sendStopPreviewCommand()
    }
  }, [situation, isPreviewing])

  useEffect(() => {
    const canvas = editor.canvas

    const handleObjectChange = async (e: any) => {
      const target = e.target
      if (target) {
        sendPreviewCommand(previewMode)
      }
    }

    canvas.on('object:modified', handleObjectChange)
    canvas.on('object:removed', handleObjectChange)
    return () => {
      canvas.off('object:modified', handleObjectChange)
      canvas.off('object:removed', handleObjectChange)
    }
  }, [editor.canvas, isPreviewing, workMode, previewMode ])

  useEffect(() => {
    if (isPreviewing) {
      sendPreviewCommand(previewMode)
    }
  }, [previewMode])

  return (
    <div className="flex flex-col fadein-popup-handler-content rounded-20 items-start py-4">

      <LayerBasicPropsController />
      {/*<div*/}
      {/*  className={clsx(*/}
      {/*    'w-full opacity-60 flex flex-row items-center justify-between gap-5'*/}
      {/*  )}*/}
      {/*>*/}
      {/*  <div className="flex gap-2 flex-1 items-center justify-center">*/}
      {/*    <InputItem label="X" value={toFixed(x)} onModify={val => editor.setBasicProp('x', val)} />*/}
      {/*    <InputItem label="Y" value={toFixed(y)} onModify={val => editor.setBasicProp('y', val)} />*/}
      {/*    <div>{unit}</div>*/}
      {/*  </div>*/}
      {/*  <div className="w-px h-[30px] border-l-[1px] border-dashed border-[#E0E0E0]" />*/}
      {/*  <div className="flex gap-2 flex-1 items-center justify-center">*/}
      {/*    <InputItem label="W" value={toFixed(w)} onModify={val => editor.setBasicProp('w', val)} />*/}
      {/*    <InputItem label="H" value={toFixed(h)} onModify={val => editor.setBasicProp('h', val)} />*/}
      {/*    <div>{unit}</div>*/}
      {/*  </div>*/}
      {/*</div>*/}
      <div className="w-full flex flex-col px-3 items-start">

        <div className="w-full border-[0.5px] border-solid border-[#E0E0E0] my-4" />

        <div className="text-xs font-bold mb-3">{t('light-output-mode')}</div>

        <ButtonRadio value={previewMode} onChange={v => setParameter('previewMode', v as PreviewMode)}>
          {options.map(option => (
            <ButtonRadioItem
              key={`${situation}-${option.value}`}
              value={option.value}
              className="h-6"
            >
              {option.label}
            </ButtonRadioItem>
          ))}
        </ButtonRadio>

        <button
          className={clsx('rounded-full text-white w-full text-sm font-semibold h-10 mt-6', 'bg-danger')}
          onClick={() => mutateAsync()}
        >
          {t('stop-preview')}
        </button>
      </div>
    </div>
  )
}
