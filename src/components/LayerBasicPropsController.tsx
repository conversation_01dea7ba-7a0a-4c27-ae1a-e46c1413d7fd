import clsx from 'clsx'
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import toast from 'react-hot-toast'
import { Input } from '@/components/ui/input'
import { useFabric } from '@/contexts/fabric.context'
import * as fabric from 'fabric'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'
import { PublicMenuKeys } from '@/components/CanvasMenu'
import { getRotatedBoundingSize } from '@/libs/fabricjs-react/utils/object'

type Dimensions = {
  x: number | undefined
  y: number | undefined
  w: number | undefined
  h: number | undefined
}

type InputItemProps = {
  label: string;
  value: string | undefined;
  onModify?: (val?: number) => void
}

const InputItem: React.FC<InputItemProps> = ({ label, value, onModify }) => {
  const { t } = useTranslation()
  const { selection } = useFabric()
  const { inactivate, activeMenuKey } = useCanvasMenu()

  const [inputValue, setInputValue] = useState(value || '')

  useEffect(() => {
    if (value === undefined) {
      setInputValue('')
    } else {
      setInputValue(value)
    }
  }, [value])

  const handleChange = useCallback(() => {
    const numericValue = Number(inputValue)

    if (Number.isNaN(numericValue) || numericValue < 0) {
      toast(t('hints.please-enter-positive-number'))
    } else {
      onModify?.(numericValue)
    }
  }, [inputValue, onModify])

  return (
    <div className="flex flex-row items-center gap-0.5 bg-[#F7F7F7] px-1 py-2">
      <div className="text-2xs mr-1">{label}</div>
      <Input
        disabled={!value || !onModify || selection?.isLocked}
        className="rounded h-3.5 !text-2xs text-center !font-bold border-none px-0"
        enterKeyHint="enter"
        value={inputValue}
        onChange={v => setInputValue(v.target.value)}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
            handleChange()
          }
        }}
        onFocus={e => {
          if (activeMenuKey !== PublicMenuKeys.previewCard) {
            inactivate()
          }
          e.target.select()
        }}
        onBlur={() => handleChange()}
      />
    </div>
  )
}

export const LayerBasicPropsController: FC = () => {
  const { editor, selection } = useFabric()
  const { unit } = useEngravingParametersStore()

  const getDimensions = useCallback((object: fabric.FabricObject): Dimensions => {
    const { width, height } = getRotatedBoundingSize(object)

    return {
      x: editor.pixelsToUnit(object.left),
      y: editor.pixelsToUnit(object.top),
      w: editor.pixelsToUnit(width),
      h: editor.pixelsToUnit(height)
    }
  }, [editor])

  const { x, y, w, h } = useMemo(() => {
    if (!selection) {
      return { x: undefined, y: undefined, w: undefined, h: undefined }
    }

    return getDimensions(selection.object)
  }, [selection])

  const toFixed = (value: number | null | undefined) => {
    if (value === null || value === undefined) return ''

    const fixedLength = value.toFixed(0).length
    if (fixedLength >= 4) return value.toFixed(0)
    if (fixedLength >= 3) return value.toFixed(1)
    return value.toFixed(2)
  }

  return (
    <div
      className={clsx(
        'w-full rounded px-3 py-3 opacity-60 flex flex-row items-center justify-between bg-white/50 h-12 gap-5 relative',
        !x && !y && !w && !h && 'opacity-30'
      )}
    >
      <div className="flex gap-2 flex-1 items-center justify-center">
        <InputItem label="X" value={toFixed(x)} onModify={val => editor.setBasicProp('x', val)} />
        <InputItem label="Y" value={toFixed(y)} onModify={val => editor.setBasicProp('y', val)} />
        <div className="text-xs">{unit}</div>
      </div>

      <div className="w-px h-[30px] border-l-[1px] border-dashed border-[#E0E0E0]" />

      <div className="flex gap-2 flex-1 items-center justify-center">
        <InputItem label="W" value={toFixed(w)} onModify={val => editor.setBasicProp('w', val)} />
        <InputItem label="H" value={toFixed(h)} onModify={val => editor.setBasicProp('h', val)} />
        <div className="text-xs">{unit}</div>
      </div>
    </div>
  )
}
