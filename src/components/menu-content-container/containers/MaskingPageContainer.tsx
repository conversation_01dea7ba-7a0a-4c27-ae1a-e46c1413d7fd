import React, { useMemo } from 'react'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { ZIndices } from '@/constants/z-index'
import { MenuContentContainerProps } from '../types'

export const MaskingPageContainer: React.FC<MenuContentContainerProps> = ({
  menuKey, children
}) => {
  const { activeMenuKey } = useCanvasMenu()
  const isActive = useMemo(() => activeMenuKey === menuKey, [menuKey, activeMenuKey])
  const { safe_area_top } = useURLSearchConfig()

  return (
    <div
      className="flex-1 overflow-y-hidden w-screen"
      style={{
        display: !isActive ? 'none' : undefined,
        left: 0,
        position: 'relative',
        paddingTop: `${safe_area_top}px`,
        zIndex: ZIndices.MaskingPage
      }}
    >
      {
        typeof children === 'function'
          ? children(isActive || false)
          : children
      }
    </div>
  )
}
