import { Drawer } from '@mui/material'
import {
  FC, useMemo
} from 'react'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { MenuContentContainerProps } from '../types'

export const DrawerContainer: FC<MenuContentContainerProps & { duration?: number }> = ({ menuKey, children, duration }) => {
  const { activeMenuKey, inactivate } = useCanvasMenu()
  const isActive = useMemo(() => activeMenuKey === menuKey, [activeMenuKey])

  return (
    <div className="w-full">
      <Drawer
        anchor="bottom"
        open={isActive}
        onClose={inactivate}
        transitionDuration={duration}
      >
        {
          isActive
            ? typeof children === 'function'
              ? children(isActive || false)
              : children
            : null
        }
      </Drawer>
    </div>
  )
}
