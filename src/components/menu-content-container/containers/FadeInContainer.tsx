import { useMemo, useRef } from 'react'
import { makeStyles } from '@mui/styles'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { MenuContentContainerProps } from '../types'
import { ZIndices } from '@/constants/z-index'

const useStyles = makeStyles(_theme => ({
  box: {
    opacity: 0,
    width: '100%',
    borderRadius: '8px 8px 0 0',
    boxShadow: '0px -4px 10px 0px #0000000D'
  },
  show: {
    animationName: '$fadein',
    animationDuration: '0.3s',
    animationFillMode: 'forwards'
  },
  hide: {
    display: 'none',
    animationName: '$fadeout',
    animationDuration: '0.3s',
    animationFillMode: 'forwards'
  },
  '@keyframes fadein': {
    '0%': {
      opacity: 0
    },
    '100%': {
      opacity: 1
    }
  },
  '@keyframes fadeout': {
    '0%': {
      opacity: 1
    },
    '100%': {
      opacity: 0
    }
  }
}))

export function FadeInContainer(props: MenuContentContainerProps) {
  const { children, menuKey } = props
  const { activeMenuKey } = useCanvasMenu()
  const isActive = useMemo(() => activeMenuKey === menuKey, [activeMenuKey])
  const handlerRef = useRef(null)
  const classes = useStyles()

  if (!isActive) return null

  return (
    <div className="FadeInPopupHandler" style={{ zIndex: ZIndices.FadeInContainer }}>
      <div className={`${classes.box} ${isActive ? classes.show : classes.hide}`} ref={handlerRef}>
        <div>
          {
            isActive
              ? typeof children === 'function'
                ? children(isActive || false)
                : children
              : null
          }
        </div>
      </div>
    </div>
  )
}

