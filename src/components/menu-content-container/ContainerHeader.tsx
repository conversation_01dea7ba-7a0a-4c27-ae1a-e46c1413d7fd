import React, { Fragment, PropsWithChildren, useCallback, useMemo } from 'react'
import CloseIcon from '@/assets/icons/close.svg?react'
import SubmitIcon from '@/assets/icons/submit.svg?react'
import clsx from 'clsx'
import { useCanvasMenu } from '@/contexts/canvas-menu'

type ContainerHeaderProps = PropsWithChildren<
  & {
    onClose?: VoidFunction;
    onSubmit?: VoidFunction;
    layoutMode?: 1 | 2 | 3;
    showClose?: boolean;
    showSubmit?: boolean;
    className?: string;

    SubmitElement?: React.ReactElement
  }
>

export const ContainerHeader: React.FC<ContainerHeaderProps> = ({
  onSubmit, onClose, className,
  showSubmit = true, showClose = true, layoutMode = 1,
  children, ...rest
}) => {
  children = typeof children === 'string'
    ? (
      <div className={clsx('font-bold', layoutMode === 1 ? 'text-base' : 'text-xs')}>
        {children}
      </div>
    )
    : children

  const { inactivate } = useCanvasMenu()

  const handleClose = useCallback(() => {
    onClose?.()
    inactivate?.()
  }, [onClose, inactivate])

  const SubmitElement = useMemo(() => (
    <div onClick={onSubmit}>
      {rest.SubmitElement || <SubmitIcon />}
    </div>
  ), [onSubmit, rest.SubmitElement])

  return (
    <div className={clsx('py-4 flex justify-between items-center', className, layoutMode === 1 ? 'px-4' : 'px-6')}>
      {
        layoutMode === 1 ? (
          <Fragment>
            <div className="min-w-8">
              {showClose && <CloseIcon onClick={handleClose} />}
            </div>
            {children}
            <div className="min-w-8">
              {onSubmit && showSubmit && SubmitElement}
            </div>
          </Fragment>
        ) : layoutMode === 2 ? (
          <Fragment>
            {children}
            <div>
              {onSubmit && showSubmit && SubmitElement}
            </div>
            <div>
              {showClose && <CloseIcon onClick={handleClose} />}
            </div>
          </Fragment>
        ) : (
          <Fragment>
            <div className="min-w-8" />
            {children}
            <div className="min-w-8">
              {showClose && <CloseIcon onClick={handleClose} />}
            </div>
          </Fragment>
        )
      }
    </div>
  )
}
