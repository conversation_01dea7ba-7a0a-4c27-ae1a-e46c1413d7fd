import React, { FC, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import * as fabric from 'fabric'

import layer_op_group from '@/assets/icons/layer_op_group.svg?react'
import layer_op_ungroup from '@/assets/icons/layer_op_ungroup.svg?react'
import layer_op_rasterization from '@/assets/icons/layer_op_rasterization.svg?react'
import layer_op_arrangement from '@/assets/icons/layer_op_arrangement.svg?react'
import { useFabric } from '@/contexts/fabric.context'
import clsx from 'clsx'
import { shouldShowObjectsGroupOperations } from '@/libs/fabricjs-react/utils'
import { ZIndices } from '@/constants/z-index'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { EngravingMenuKeys, PublicMenuKeys, VectorMenuKeys } from '@/components/CanvasMenu'
import { values } from 'lodash'
import align_right from '@/assets/icons/align_right.svg?react'
import align_middle from '@/assets/icons/align_center_2.svg?react'
import align_horizontal from '@/assets/icons/align_horizontal.svg?react'
import align_vertical from '@/assets/icons/align_vertical.svg?react'
import align_top from '@/assets/icons/align_top.svg?react'
import align_bottom from '@/assets/icons/align_bottom.svg?react'
import align_center from '@/assets/icons/align_center.svg?react'
import align_left from '@/assets/icons/align_left.svg?react'
import { AlignType } from '@/libs/fabricjs-react/plugins/vector.plugin'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'

export type ObjectsGroupOperationsProps = {
  //
}

export const OperationsContainerHeight = 56

const HandleItem: FC<{ icon: any, handler: () => void, label: string, disabled?: boolean, active?: boolean }> = ({
  icon: Icon, handler, label, disabled, active
}) => {
  return (
    <div
      key={label}
      className={clsx('flex flex-col items-center gap-1 justify-center size-14', disabled && 'select-none', active && 'text-primary')}
      onClick={() => {
        if (disabled) return
        handler()
      }}
    >
      <Icon className={clsx('size-6')} fill={disabled ? '#999' : active ? '#F18D00' : undefined} />
      <div className={clsx('text-xs text-nowrap', disabled && 'text-gray-300 ', active && 'text-primary')}>
        {label}
      </div>
    </div>
  )
}

export const ObjectsGroupOperations: FC<ObjectsGroupOperationsProps> = () => {
  const { t } = useTranslation()
  const { safe_area_bottom } = useURLSearchConfig()
  const { initialized, editor, selection } = useFabric()
  const { activeMenuKey, setActiveMenuKey, arrangementMode, setArrangementMode } = useCanvasMenu()

  const [currentAlignMode, setCurrentAlignMode] = useState<AlignType>()

  const objectLength = useMemo(() => {
    if (!selection) return 0
    if (!selection?.object || !(selection.object instanceof fabric.ActiveSelection)) return 0

    return selection.object.getObjects().length
  }, [selection])

  const operations = useMemo(() => {
    if (!initialized) return []

    return [
      {
        label: t('common.arrange'),
        icon: layer_op_arrangement,
        handler: () => {
          if (activeMenuKey === PublicMenuKeys.canvasAlign) {
            setActiveMenuKey(VectorMenuKeys.filling)
          }
          setArrangementMode(!arrangementMode)
        },
        active: arrangementMode,
      },
      // {
      //   label: t('common.delete'),
      //   icon: layer_op_delete,
      //   handler: () => editor.removeActiveObjects(),
      //   disabled: !editor.canRemove()
      // },
      // {
      //   label: t('common.copy'),
      //   icon: layer_op_copy,
      //   handler: () => editor.duplicateActiveObjects()
      // },
      {
        label: t('common.group'),
        icon: layer_op_group,
        handler: () => editor.groupActiveObjects(),
        disabled: !editor.canGroup()
      },
      {
        label: t('common.ungroup'),
        icon: layer_op_ungroup,
        handler: () => editor.ungroupActiveObjects(),
        disabled: !editor.canUngroup()
      },
      {
        label: t('rasterization'),
        icon: layer_op_rasterization,
        handler: () => editor.rasterization(),
        disabled: !editor.canRasterization()
      },
    ]
  }, [initialized, selection, arrangementMode, activeMenuKey])

  const alignOperations = useMemo(() => {
    if (!selection) return

    const operations: { label: string; icon: any; type: AlignType; disabled?: boolean, active?: boolean }[] = [
      { label: t('vector-align.left'), icon: align_left, type: 'left', active: currentAlignMode === 'left' },
      { label: t('vector-align.center'), icon: align_center, type: 'center', active: currentAlignMode === 'center' },
      { label: t('vector-align.right'), icon: align_right, type: 'right', active: currentAlignMode === 'right' },
      { label: t('vector-align.top'), icon: align_top, type: 'top', active: currentAlignMode === 'top' },
      { label: t('vector-align.middle'), icon: align_middle, type: 'middle', active: currentAlignMode === 'middle' },
      { label: t('vector-align.bottom'), icon: align_bottom, type: 'bottom', active: currentAlignMode === 'bottom' },
      {
        label: t('vector-align.horizontal'),
        icon: align_horizontal,
        type: 'horizontal',
        disabled: objectLength <= 2,
        active: currentAlignMode === 'horizontal'
      },
      {
        label: t('vector-align.vertical'),
        icon: align_vertical,
        type: 'vertical',
        disabled: objectLength <= 2,
        active: currentAlignMode === 'vertical'
      },
    ]

    return operations.map(({ label, icon, type, disabled, active }) => ({
      label,
      icon,
      disabled,
      active,
      handler: () => {
        editor.alignObjectsWithinGroup(type)
        setCurrentAlignMode(type)
      }
    }))
  }, [selection, objectLength, currentAlignMode])

  useEffect(() => {
    if (activeMenuKey === PublicMenuKeys.canvasAlign || selection?.isLocked) {
      setArrangementMode(false)
    }
  }, [activeMenuKey, selection])

  if (
    !(shouldShowObjectsGroupOperations(selection))
    || (activeMenuKey && [
      ...values(EngravingMenuKeys).map(String),
      PublicMenuKeys.previewCard,
    ].includes(activeMenuKey))
  ) {
    return null
  }

  return (
    <div
      className="flex flex-col gap-1 scrollbar-hidden fadein-popup-handler-content "
      style={{
        zIndex: ZIndices.CanvasMenu,
        paddingBottom: `${safe_area_bottom}px`
      }}
    >
      <div className="flex overflow-x-auto scrollbar-hidden ">
        {arrangementMode && (
          <div className="border-b border-gray-200 flex flex-nowrap" style={{ height: OperationsContainerHeight }}>
            {alignOperations?.map(({ icon, label, handler, disabled, active }) => (
              <HandleItem
                key={label}
                handler={handler}
                label={label}
                icon={icon}
                disabled={disabled}
                active={active}
              />
            ))}
          </div>
        )}
      </div>

      <div className="flex overflow-x-auto scrollbar-hidden" style={{ height: OperationsContainerHeight }}>
        {operations.map(({ icon, label, handler, disabled, active }) => (
          <HandleItem
            key={label}
            handler={handler}
            label={label}
            disabled={disabled || selection?.isLocked}
            icon={icon}
            active={active}
          />
        ))}
      </div>
    </div>
  )
}
