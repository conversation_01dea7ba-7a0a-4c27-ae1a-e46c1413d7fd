import { useFabric } from '@/contexts/fabric.context'
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import clsx from 'clsx'
import * as fabric from 'fabric'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { EngravingMenuKeys, VectorMenuKeys } from '@/components/CanvasMenu'
import { useTranslation } from 'react-i18next'
import toast from 'react-hot-toast'
import { ZIndices } from '@/constants/z-index'
import FlipVerticalIcon from '@/assets/icons/flip_vertical.svg?react'
import FlipHorizontalIcon from '@/assets/icons/flip_horizontal.svg?react'
import CounterClockwiseIcon from '@/assets/icons/counterclockwise.svg?react'
import { Input } from './ui/input'
import { LayerBasicPropsController } from '@/components/LayerBasicPropsController'


type CustomTickedSliderProps = {
  value: number,
  disabled: boolean,
  onChange: (v: number) => void,
  onChangeEnd?: () => void
}

const CustomTickedSlider: FC<CustomTickedSliderProps> = ({ value, disabled, onChange, onChangeEnd }) => {
  const min = -15
  const max = 15
  const step = 1
  const totalTicks = max - min
  const [isDragging, setIsDragging] = useState(false)
  const [initialValue, setInitialValue] = useState<number>()

  const positionPercent = ((value - min) / (max - min)) * 100

  const handleStart = () => {
    setIsDragging(true)
    setInitialValue(value)
  }

  const handleEnd = () => {
    setIsDragging(false)
    if (typeof initialValue === 'number' && initialValue !== value) {
      onChangeEnd?.()
    }
  }

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.preventDefault()  // 阻止焦点事件，防止弹出键盘
  }

  return (
    <div className="relative w-full h-10 flex items-center">
      {/* 刻度线容器 */}
      <div className="absolute top-1/2 left-0 -translate-y-1/2 w-full h-8 flex justify-between items-center pointer-events-none z-0">
        {Array.from({ length: totalTicks + 1 }).map((_, i) => {
          const isMajor = (i + min) % 5 === 0
          const tickValue = i + min
          const isZero = tickValue === 0
          return (
            <div key={i} className="relative flex items-center justify-center" style={{ width: '1px' }}>
              <div
                className={clsx(
                  'flex-none',
                  isMajor ? 'h-[14px] bg-[#999] mt-[-2px]' : 'h-[10px] bg-[#eee] mt-[-5px]'
                )}
                style={{ width: '1px' }}
              />
              {isZero && (
                <div className={clsx('absolute top-[18px] w-[3px] h-[3px] rounded-full', disabled ? 'bg-gray-400' : 'bg-black')} />
              )}
            </div>
          )
        })}
      </div>

      {/* 当前角度小提示框 */}
      {isDragging && (
        <div
          className="absolute z-20 flex flex-col items-center"
          style={{
            left: `${positionPercent}%`,
            top: '-34px', // 提示框离 thumb 的垂直距离
            transform: 'translateX(-50%)',
            transition: 'left 0.1s ease',
          }}
        >
          {/* 黑色提示框 */}
          <div className="w-[42px] h-[36px] bg-black/80 text-white text-xs rounded flex items-center justify-center">
            {value}
          </div>

          {/* 三角形装饰 */}
          <div
            className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-black"
          />
        </div>
      )}

      {/* 自定义虚拟 thumb（黑色指示线） */}
      <div
        className={clsx(
          'absolute top-1/2 -translate-y-1/2 h-[18px] w-[1.5px] z-10 pointer-events-none mt-[1px]',
          disabled ? 'bg-gray-400' : 'bg-black'
        )}
        style={{ left: `${positionPercent}%`, transform: 'translateX(-0.75px) translateY(-50%)', transition: 'left 0.1s ease' }}
      />

      {/* 透明 input 滑条 */}
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        disabled={disabled}
        onChange={e => onChange(Number(e.target.value))}
        onMouseDown={handleStart}
        onTouchStart={handleStart}
        onMouseUp={handleEnd}
        onTouchEnd={handleEnd}
        className={clsx(
          'ghost-range',
          'relative w-full z-20 appearance-none bg-transparent',
        )}
        style={{ height: '30px' }}
        onFocus={handleFocus}  // 添加防止输入框获得焦点
      />
    </div>
  )
}

const RotationInput: React.FC<{
  value: number
  onChange: (val: number, isInput: boolean) => void
  disabled?: boolean
}> = ({ value, onChange, disabled }) => {
  const MIN = 0, MAX = 360

  const { t } = useTranslation()

  const [inputValue, setInputValue] = useState(Math.round(value).toString())
  const inputRef = useRef<HTMLInputElement>(null)

  const commitChange = useCallback(() => {
    let newVal = Math.round(Number(inputValue))

    // 检查输入值是否是有效数字
    if (isNaN(newVal) || newVal < 0) {
      toast(t('hints.please-enter-positive-number'))
      setInputValue(value.toString())  // 如果输入无效，恢复为上一个有效值
      return
    }

    // 确保新值在允许的范围内
    if (newVal < MIN) {
      newVal = MIN
      toast(t('hints.value-too-low', { min: MIN }))  // 可选：显示提示消息，告诉用户最小值
    } else if (newVal > MAX) {
      newVal = MAX
      toast(t('hints.value-too-high', { max: MAX }))  // 可选：显示提示消息，告诉用户最大值
    }
    onChange(newVal, true)
    inputRef.current?.blur()
  }, [inputValue, onChange, t, value])

  useEffect(() => {
    setInputValue(Math.round(value).toString())
  }, [value])

  return (
    <Input
      ref={inputRef}
      disabled={disabled}
      value={inputValue}
      onChange={e => setInputValue(e.target.value)}
      onKeyDown={e => {
        if (e.key === 'Enter') {
          commitChange()
          inputRef.current?.blur()  // 提交时失去焦点
        }
      }}
      className="w-[60px] h-[24px] text-xs font-bold bg-[#F7F7F7] ml-2 text-center"
      type="number"
      enterKeyHint="enter"
    />
  )
}

export const LayerController = () => {
  const { editor, selection } = useFabric()
  const { activeMenuKey, arrangementMode } = useCanvasMenu()

  // 输入框显示的绝对角度
  const [rotation, setRotation] = useState(0)
  // 刻度条上显示的临时(相对)角度
  const [sliderRotation, setSliderRotation] = useState(0)
  // 选中对象时的原始角度
  const [initialAngle, setInitialAngle] = useState(0)

  const isUserSliderRef = useRef(false)

  const selectedObject = useMemo(() => {
    if (!selection) return undefined
    return selection.object
  }, [selection])

  const available = useMemo(() => selection && !selection?.isLocked, [selection])

  const handleFlipHorizontal = () => {
    const obj = selectedObject
    if (obj && available) {
      const newFlipX = !obj.flipX
      obj.set('flipX', newFlipX)
      editor.canvas.requestRenderAll()
      editor.canvas.fire('object:modified', { target: obj })
    }
  }

  const handleFlipVertical = () => {
    const obj = selectedObject
    if (obj && available) {
      const newFlipY = !obj.flipY
      obj.set('flipY', newFlipY)
      editor.canvas.requestRenderAll()
      editor.canvas.fire('object:modified', { target: obj })
    }
  }

  const handleRotationChange = (newRelativeRotation: number, isInputValue?: boolean) => {
    if (!selectedObject) return

    isUserSliderRef.current = true
    const newRotation = isInputValue ? newRelativeRotation : ((initialAngle + newRelativeRotation) % 360 + 360) % 360
    const center = selectedObject.getCenterPoint()
    selectedObject.set({
      angle: newRotation,
    })
    selectedObject.setPositionByOrigin(center, 'center', 'center')
    selectedObject.setCoords()
    editor.canvas.requestRenderAll()
    setRotation(newRotation)
    setSliderRotation(isInputValue ? 0 : newRelativeRotation)
    if (isInputValue) {
      setInitialAngle(newRotation)
      editor.canvas.fire('object:modified', { target: selectedObject })
    }
  }

  // 每次选中对象时，重置旋转角度为 0°
  useEffect(() => {
    if (selectedObject) {
      const angle = selectedObject.angle || 0
      setInitialAngle(angle)     // 记录原始角度
      setRotation(angle)
      setSliderRotation(0)// 重置 UI 上的角度为 0
    }
  }, [selectedObject])

  useEffect(() => {
    if (selectedObject) {
      const canvas = editor.canvas

      const handleObjectModified = (e: any) => {
        const target = e.target as fabric.Object
        if (target && selectedObject && target === selectedObject) {
          const angle = target.angle || 0

          setRotation(angle)
          // 如果是旋转控件造成的旋转，刻度条保持在零的位置
          if (!isUserSliderRef.current) {
            setSliderRotation(0)
            setInitialAngle(angle)
          }

          isUserSliderRef.current = false // 重置标志
        }
      }

      const handleObjectRotating = (e: any) => {
        const target = e.target as fabric.Object
        if (target && selectedObject && target === selectedObject) {

          const angle = target.angle || 0
          setRotation(angle)
        }
      }


      canvas.on('object:modified', handleObjectModified)
      canvas.on('object:rotating', handleObjectRotating)
      return () => {
        canvas.off('object:modified', handleObjectModified)
        canvas.off('object:rotating', handleObjectRotating)
      }
    }
  }, [editor.canvas, selectedObject])

  if (activeMenuKey === EngravingMenuKeys.engrave || activeMenuKey === EngravingMenuKeys.parameter) {
    return null
  }

  return (
    <div
      style={{ backdropFilter: 'blur(5px)', zIndex: ZIndices.Base }}
      className="flex flex-col w-full bg-white/50 relative"
    >
      <LayerBasicPropsController />

      {!(arrangementMode && activeMenuKey === VectorMenuKeys.filling) && (
        <div className="flex flex-row items-center justify-between w-full bg-white/50 px-3 h-12 gap-5">
          <CounterClockwiseIcon
            style={{ color: available ? '#333' : '#999' }}
            className="size-11"
            onClick={() => editor.rotateWithDegreeDelta(-90)}
          />

          <div className="flex items-center gap-2 w-full mx-4">
            <CustomTickedSlider
              value={sliderRotation}
              disabled={!available}
              onChange={handleRotationChange}
              onChangeEnd={() => {
                const obj = selectedObject
                if (obj) {
                  editor.canvas.fire('object:modified', { target: obj })
                }
              }}
            />
            <RotationInput disabled={!available} value={rotation} onChange={handleRotationChange} />
            <div className="text-sm">°</div>
          </div>

          <FlipVerticalIcon
            style={{ color: available ? '#333' : '#999' }}
            className="size-11"
            onClick={handleFlipVertical}
          />
          <FlipHorizontalIcon
            style={{ color: available ? '#333' : '#999' }}
            className="size-11"
            onClick={handleFlipHorizontal}
          />
        </div>
      )}
    </div>
  )
}
