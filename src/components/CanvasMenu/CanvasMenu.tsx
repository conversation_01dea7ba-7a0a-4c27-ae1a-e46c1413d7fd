import React, { FC, Fragment, useCallback, useMemo } from 'react'
import { values } from 'lodash'
import clsx from 'clsx'

import { useFabric } from '@/contexts/fabric.context'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { ObjectType } from '@/libs/fabricjs-react/constants'

import {
  BitmapMenuKeys,
  EngravingMenuKeys,
  GeneralMenuKeys,
  NameByMenuKey,
  NewBitmapMenuKeys,
  OtherMenuKeys,
  PublicMenuKeys,
  TextMenuKeys,
  VectorMenuKeys
} from './constants'

import { DrawerContainer, FadeInContainer, MaskingPageContainer } from '../menu-content-container'
import { TabMenu } from '@/components/ui/TabMenu'
import { CanvasToolbar } from '@/components/CanvasToolbar'
import { LayerController } from '@/components/LayerController'
import { useCanvasSelectionChangeHandler } from '@/hooks/useCanvasSelectionChangeHandler'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { MenuKeys } from '@/components/CanvasMenu'
import { MessageAction } from '@/libs/shared/types'
import { useTranslation } from 'react-i18next'
import { SocialEnums } from '@/datasource/api/social/types'
import { sendMessageToNative } from '@/utils/message'
import { useDeviceStatusStore } from '@/stores/device-status'
import { isDeviceWorking } from '@/utils/device'
import { safeParseJson, sleep } from '@/utils'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'

import {
  BitmapOperations,
  EngravingOperations,
  GeneralOperations,
  PublicOperations,
  TextOperations,
  VectorOperations
} from '@/components/operations'
import { ObjectsGroupOperations } from '@/components/ObjectsGroupOperations'
import { ZIndices } from '@/constants/z-index'

import tab_ai from '@/assets/icons/tab_ai.svg?react'
import tab_material from '@/assets/icons/tab_materials.svg?react'
import tab_object from '@/assets/icons/tab_object.svg?react'
import tab_text from '@/assets/icons/tab_text.svg?react'
import tab_paint from '@/assets/icons/tab_paint.svg?react'
import tab_picture from '@/assets/icons/tab_picture.svg?react'
import { shouldShowObjectsGroupOperations } from '@/libs/fabricjs-react/utils'
import { RECENT_ENGRAVING_PARAMS } from '@/constants/query-keys'

const buttonCommonClassNames = 'rounded-full w-full text-sm font-semibold h-10'

const ICON_BY_MENU_KEY: any = {
  [GeneralMenuKeys.ai]: tab_ai,
  [GeneralMenuKeys.image]: tab_picture,
  [GeneralMenuKeys.content]: tab_text,
  [GeneralMenuKeys.draw]: tab_paint,
  [GeneralMenuKeys.layers]: tab_object,
  [GeneralMenuKeys.material]: tab_material
}

// 最近参数存储工具
function saveRecentEngravingParam(materialId: number | string, parameterId: number | string) {
  try {
    let list: any[] = safeParseJson(localStorage.getItem(RECENT_ENGRAVING_PARAMS) || '[]') || []
    list = list.filter(item => !(item.materialId === materialId && item.parameterId === parameterId))
    list.unshift({ materialId, parameterId })
    if (list.length > 5) list = list.slice(0, 5)
    localStorage.setItem(RECENT_ENGRAVING_PARAMS, JSON.stringify(list))
  } catch (e) {
    // 静默失败
    console.warn('保存最近使用的参数失败: ', e)
  }
}

export const CanvasMenu: FC = () => {
  useCanvasSelectionChangeHandler()

  const { t } = useTranslation()
  const { editor, selection } = useFabric()
  const { safe_area_bottom } = useURLSearchConfig()
  const engravingStore = useEngravingParametersStore()
  const { deviceCurrentState, reset: resetDeviceStatus } = useDeviceStatusStore()

  const { activeMenuKey, isEngravingPrepared, setActiveMenuKey, inactivate, startToEngrave } = useCanvasMenu()

  const objectType = useMemo<ObjectType>(() => selection?.type || ObjectType.NONE, [selection])

  const handleWithComponent = useCallback(
    (key: string | null, toggleMenu = true) => {
      setActiveMenuKey(toggleMenu ? (key as MenuKeys) : null)
    },
    [activeMenuKey]
  )

  const handleChange = useCallback(
    (key: MenuKeys | undefined) => {
      if (selection?.isLocked) return

      handleWithComponent(key || null)

      const delayedInactive = () => sleep(1500).then(inactivate)

      switch (key) {
        case GeneralMenuKeys.ai: {
          sendMessageToNative(MessageAction.CALL_AI_GENERATING)
          return delayedInactive()
        }

        case GeneralMenuKeys.image: {
          if (window.ReactNativeWebView) {
            sendMessageToNative(MessageAction.REQUEST_DEVICE_IMAGE)
          } else {
            editor.__dev__importFile()
          }

          return delayedInactive()
        }

        case GeneralMenuKeys.material: {
          sendMessageToNative(MessageAction.REQUEST_DEVICE_MATERIAL)
          return
        }

        case BitmapMenuKeys.ai: {
          if (selection?.type === ObjectType.IMAGE) {
            sendMessageToNative(MessageAction.CALL_AI_OPTIMIZING, selection.object.toDataURL())
          }
          return delayedInactive()
        }

        case OtherMenuKeys.file: {
          if (window.ReactNativeWebView) {
            sendMessageToNative(MessageAction.OPEN_DEVICE_FILE_PICKER)
          } else {
            editor.__dev__importFile()
          }
          return delayedInactive()
        }

        case VectorMenuKeys.filling:
          return editor.fillSelectedVector()

        case VectorMenuKeys.stroke:
          return editor.clearFillForSelectedVector()

        case VectorMenuKeys.pathFill:
          return editor.pathFillSelectedVector()

        case BitmapMenuKeys.copy:
          return editor.duplicateActiveObjects()

        case EngravingMenuKeys.parameter:
          return resetDeviceStatus()

        case EngravingMenuKeys.preview:
        case EngravingMenuKeys.engrave:
        case PublicMenuKeys.previewCard:
          resetDeviceStatus()
          editor.zoomToMinimum()
          editor.canvas.discardActiveObject()
          return editor.canvas.requestRenderAll()
      }
    },
    [editor, objectType, handleWithComponent, selection, deviceCurrentState]
  )

  const menuKeysToShow = useMemo(() => {
    if (isEngravingPrepared) return EngravingMenuKeys
    if (activeMenuKey === PublicMenuKeys.previewCard || activeMenuKey === PublicMenuKeys.workMode) return GeneralMenuKeys

    return {
      [ObjectType.IMAGE]: NewBitmapMenuKeys,
      [ObjectType.TEXT]: TextMenuKeys,
      [ObjectType.OBJECTS]: GeneralMenuKeys,
      [ObjectType.GROUP]: GeneralMenuKeys,
      [ObjectType.VECTOR]: VectorMenuKeys,
      [ObjectType.VECTOR_GROUP]: VectorMenuKeys,
      [ObjectType.NONE]: GeneralMenuKeys
    }[objectType]
  }, [isEngravingPrepared, objectType, activeMenuKey])

  const isActive = useMemo(() => {
    const hiddenKeys: MenuKeys[] = [
      TextMenuKeys.text,
      TextMenuKeys.align,
      GeneralMenuKeys.content,
      OtherMenuKeys.vector,
      OtherMenuKeys.bitmap,
      PublicMenuKeys.workMode,
      PublicMenuKeys.previewCard,
      PublicMenuKeys.canvasAlign,
      NewBitmapMenuKeys.eraser,
      NewBitmapMenuKeys.filter,
      NewBitmapMenuKeys.adjust,
      VectorMenuKeys.pathFill,
    ]
    return activeMenuKey === null || !hiddenKeys.includes(activeMenuKey)
  }, [activeMenuKey, objectType, selection])

  const showObjectsGroupOperations = useMemo(() => {
    return !(shouldShowObjectsGroupOperations(selection)) ||
        (activeMenuKey && [
          ...values(EngravingMenuKeys).map(String),
          PublicMenuKeys.previewCard,
        ].includes(activeMenuKey))
  }, [selection, activeMenuKey])

  return (
    <div
      className={clsx(
        'w-screen flex flex-col justify-end',
        activeMenuKey === EngravingMenuKeys.parameter && 'h-screen'
      )}
    >
      {activeMenuKey !== PublicMenuKeys.previewCard && (
        <CanvasToolbar />
      )}

      {isActive && (
        <LayerController />
      )}

      <Fragment>
        <DrawerContainer title={t('common.draw')} menuKey={GeneralMenuKeys.draw}>
          <GeneralOperations.Painting />
        </DrawerContainer>

        <FadeInContainer menuKey={PublicMenuKeys.workMode}>
          <GeneralOperations.WorkMode />
        </FadeInContainer>

        <FadeInContainer menuKey={OtherMenuKeys.bitmap}>
          <GeneralOperations.Resources resourceType={SocialEnums.bitmap} />
        </FadeInContainer>

        <FadeInContainer menuKey={GeneralMenuKeys.content}>
          <GeneralOperations.Content />
        </FadeInContainer>

        <FadeInContainer menuKey={OtherMenuKeys.vector}>
          <GeneralOperations.Resources resourceType={SocialEnums.vector} />
        </FadeInContainer>

        <FadeInContainer menuKey={TextMenuKeys.text}>
          <TextOperations.Font />
        </FadeInContainer>

        <FadeInContainer menuKey={TextMenuKeys.align}>
          <TextOperations.Align />
        </FadeInContainer>

        <FadeInContainer menuKey={VectorMenuKeys.pathFill}>
          <VectorOperations.PathFill />
        </FadeInContainer>

        <DrawerContainer title={t('common.crop')} menuKey={BitmapMenuKeys.crop}>
          <BitmapOperations.Crop />
        </DrawerContainer>

        <FadeInContainer menuKey={BitmapMenuKeys.filter}>
          <BitmapOperations.Filter />
        </FadeInContainer>

        <FadeInContainer menuKey={BitmapMenuKeys.adjust}>
          <BitmapOperations.Adjust />
        </FadeInContainer>

        <FadeInContainer menuKey={BitmapMenuKeys.rotate}>
          <BitmapOperations.Rotate />
        </FadeInContainer>

        <FadeInContainer menuKey={BitmapMenuKeys.eraser}>
          <BitmapOperations.Eraser />
        </FadeInContainer>

        <MaskingPageContainer menuKey={EngravingMenuKeys.parameter}>
          <EngravingOperations.Parameter />
        </MaskingPageContainer>

        <FadeInContainer menuKey={EngravingMenuKeys.engrave}>
          <EngravingOperations.Engraving />
        </FadeInContainer>

        <FadeInContainer menuKey={PublicMenuKeys.previewCard}>
          <PublicOperations.PreviewCard />
        </FadeInContainer>

        <FadeInContainer menuKey={PublicMenuKeys.canvasAlign}>
          <PublicOperations.CanvasAlign />
        </FadeInContainer>
      </Fragment>

      <div id="CanvasMenu">
        <ObjectsGroupOperations />

        {(activeMenuKey !== EngravingMenuKeys.engrave && activeMenuKey !== EngravingMenuKeys.parameter) && (
          <>
            <TabMenu
              value={activeMenuKey}
              onChange={item => handleChange(item?.key as MenuKeys)}
              disabled={isDeviceWorking(deviceCurrentState) || selection?.isLocked}
              className={clsx(
                'px-3 w-full bg-[#F7F7F7]',
                !showObjectsGroupOperations ? 'hidden' : '',
                values(menuKeysToShow).length <= 3 && 'justify-center'
              )}
              itemClassName={clsx(
                values(menuKeysToShow).length <= 3 ? 'max-xs:mx-3 mx-2.5 text-nowrap' : 'flex-1 text-center text-nowrap',
                Number(safe_area_bottom) ? 'pt-3' : 'py-3'
              )}
              style={{
                paddingBottom: `${safe_area_bottom}px`,
                zIndex: ZIndices.CanvasMenu
              }}
              items={values(menuKeysToShow).map((key: MenuKeys) => {
                const Icon = ICON_BY_MENU_KEY[key]
                const shouldBeInvisible = (key !== GeneralMenuKeys.material && activeMenuKey !== key)
                  || (key === GeneralMenuKeys.material && activeMenuKey !== OtherMenuKeys.bitmap && activeMenuKey !== OtherMenuKeys.vector)

                return {
                  key,
                  name: t(NameByMenuKey[key]),
                  icon: Icon ? <Icon style={{ color: !shouldBeInvisible ? '#333' : '#999' }} /> : undefined,
                }
              })}
            />
          </>
        )}
      </div>

      {activeMenuKey === EngravingMenuKeys.parameter && (
        <div className="w-full text-center py-3 bg-white px-3">
          <button
            className={clsx(
              buttonCommonClassNames,
              'bg-primary text-white'
            )}
            onClick={() => {
              editor.canvas.discardActiveObject()
              if (engravingStore.material?.id && engravingStore.parameter?.id) {
                saveRecentEngravingParam(engravingStore.material.id, engravingStore.parameter.id)
              }
              startToEngrave()
            }}
          >
            {t('go-engraving')}
          </button>
        </div>
      )}
    </div>
  )
}
