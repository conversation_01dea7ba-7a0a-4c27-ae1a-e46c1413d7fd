export enum GeneralMenuKeys {
  ai = 'ai',
  image = 'image',
  content = 'content',
  material = 'material',
  draw = 'draw',
  layers = 'layers'
}

export enum OtherMenuKeys {
  vector = 'vector',
  bitmap = 'bitmap',
  file = 'file'
}

export enum PublicMenuKeys {
  workMode = 'workMode',
  previewCard = 'previewCard',
  canvasAlign = 'canvasAlign'
}

export enum BitmapMenuKeys {
  ai = 'bitmap-ai',
  filter = 'filter',
  adjust = 'adjust',
  rotate = 'rotate',
  crop = 'crop',
  copy = 'copy',
  eraser = 'eraser',
}

export enum NewBitmapMenuKeys {
  ai = 'bitmap-ai',
  filter = 'filter',
  adjust = 'adjust',
  // rotate = 'rotate',
  crop = 'crop',
  // copy = 'copy',
  eraser = 'eraser',
}

export enum TextMenuKeys {
  text = 'text',
  align = 'align',
}

export enum VectorMenuKeys {
  stroke = 'stroke',
  filling = 'filling',
  pathFill = 'pathFill'
}

export enum EngravingMenuKeys {
  parameter = 'parameter',
  preview = 'preview',
  engrave = 'engrave'
}

export enum EngravingParameterMenuKeys {
  moreMaterial = 'moreMaterial',
  moreParameters = 'moreParameters',
}

export const NameByMenuKey = {
  [GeneralMenuKeys.ai]: 'common.ai-generate',
  [GeneralMenuKeys.image]: 'common.image',
  [GeneralMenuKeys.content]: 'common.text',
  [OtherMenuKeys.bitmap]: 'common.bitmap',
  [OtherMenuKeys.vector]: 'common.vector',
  [GeneralMenuKeys.draw]: 'menus.general.draw',
  [OtherMenuKeys.file]: 'menus.general.file',
  [GeneralMenuKeys.layers]: 'menus.general.layers',
  [PublicMenuKeys.workMode]: 'menus.general.canvas',

  [BitmapMenuKeys.ai]: 'common.ai-retouch',
  [BitmapMenuKeys.filter]: 'menus.bitmap.filter',
  [BitmapMenuKeys.adjust]: 'menus.bitmap.adjust',
  [BitmapMenuKeys.rotate]: 'menus.bitmap.rotate',
  [BitmapMenuKeys.crop]: 'menus.bitmap.crop',
  [BitmapMenuKeys.copy]: 'menus.bitmap.copy',
  [BitmapMenuKeys.eraser]: 'menus.bitmap.eraser',

  [TextMenuKeys.text]: 'common.text',
  [TextMenuKeys.align]: 'menus.text.align',

  [VectorMenuKeys.filling]: 'menus.vector.filling',
  [VectorMenuKeys.stroke]: 'menus.vector.stroke',
  [VectorMenuKeys.pathFill]: 'menus.vector.path-fill',

  [EngravingMenuKeys.parameter]: 'menus.engraving.parameter',
  [EngravingMenuKeys.preview]: 'menus.engraving.preview',
  [EngravingMenuKeys.engrave]: 'menus.engraving.engrave',

  [GeneralMenuKeys.material]: 'common.materials',
}

