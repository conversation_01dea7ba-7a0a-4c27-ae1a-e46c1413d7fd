import * as React from 'react'
import { NumberInputProps, Unstable_NumberInput as BaseNumberInput } from '@mui/base/Unstable_NumberInput'
import { styled } from '@mui/material/styles'
import MinusIcon from '@/assets/icons/minus.svg?react'
import AddIcon from '@/assets/icons/add.svg?react'
import { alpha, InputBase } from '@mui/material'

const StyledInputRoot = styled('div')(() => ({
  display: 'flex',
  flexFlow: 'row nowrap',
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: 4
}))

const StyledInput = styled(InputBase)(({ theme }) => ({
  height: 30,
  flex: 1,
  '& .MuiInputBase-input': {
    height: '100%',
    borderRadius: 4,
    position: 'relative',
    textAlign: 'center',
    backgroundColor: theme.palette.grey['100'],
    border: 'none',
    fontSize: 16,
    padding: '0 12px',
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow',
    ]),
    '&:focus': {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}))


const StyledButton = styled('button')(({ theme, disabled }) => ({
  width: 24,
  height: 30,
  borderWidth: 0,
  backgroundColor: theme.palette.grey['100'],
  boxSizing: 'border-box',
  display: 'flex',
  flexFlow: 'flow no-wrap',
  justifyContent: 'center',
  alignItems: 'center',
  color: disabled ? theme.palette.grey['200'] : theme.palette.grey['900'],
  '&.increment': {
    order: 1
  },

  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: 'white'
  }
}))


export const CNumberInput2 = React.forwardRef(function CNumberInput2(props: NumberInputProps, ref) {
  return (
    // @ts-ignore
    <BaseNumberInput
      ref={ref}
      slots={{
        root: StyledInputRoot,
        input: StyledInput,
        incrementButton: StyledButton,
        decrementButton: StyledButton,
      }}
      slotProps={{
        input: {
          type: 'number',
        },
        incrementButton: {
          children: <AddIcon />,
          className: 'increment',
        },
        decrementButton: {
          children: <MinusIcon  />,
        },
      }}
      {...props}
    />
  )
})

