import React, { FC, PropsWithChildren, useContext, useEffect, useState } from 'react'
import clsx from 'clsx'
import { eq } from 'lodash'

const ButtonRadioContext = React.createContext<{
  value: string | number | null;
  onItemClicked: (val: string | number | null) => void,
  disabled?: boolean
}>(null as any)

type ButtonRadioProps = PropsWithChildren<{
  value?: string | number |  null;
  onChange?(val: string | number | null): void
  className?: string,
  disabled?: boolean
}>

type ButtonRadioItemProps = PropsWithChildren<{
  value: string | number | null;
  className?: string
}>

export const ButtonRadio: FC<ButtonRadioProps> = ({
  value = null, onChange, children, className, disabled = false
}) => {
  const [internalValue, setInternalValue] = useState<string | number | null>(null)

  const handleValueChange = (v: string | number | null) => {
    onChange?.(v)
    setInternalValue(v)
  }

  useEffect(() => {
    if (!eq(internalValue, value)) {
      handleValueChange(value ?? null)
    }
  }, [value, internalValue])

  return (
    <ButtonRadioContext.Provider value={{ value: internalValue, onItemClicked: handleValueChange, disabled }}>
      <div className={clsx('flex overflow-x-auto scrollbar-hidden', className)}>
        {children}
      </div>
    </ButtonRadioContext.Provider>
  )
}

export const ButtonRadioItem: FC<ButtonRadioItemProps> = ({
  children, value, className
}) => {
  const { value: activeValue, onItemClicked } = useContext(ButtonRadioContext)
  const isActive = activeValue === value

  return (
    <div
      className={clsx(
        isActive && 'bg-primary',
        'py-[3px] px-4 rounded-full flex items-center',
        'transition-all',
        className
      )}
      onClick={() => onItemClicked(value)}
    >
      <div
        className={clsx(
          isActive ? 'text-white' : 'text-gray-500',
          'text-xs transition-all text-nowrap whitespace-nowrap'
        )}
      >
        {children}
      </div>
    </div>
  )
}

export const ButtonRadioItem2: FC<ButtonRadioItemProps> = ({
  children, value, className
}) => {
  const { value: activeValue, onItemClicked } = useContext(ButtonRadioContext)
  const isActive = activeValue === value

  return (
    <div
      className={clsx(
        isActive ? 'bg-[#FEF4E6] border-primary' : 'bg-[#F7F7F7]',
        'rounded h-auto min-w-[80px] max-w-[160px] px-2 py-2 border border-transparent',
        'transition-all flex justify-center items-center text-center',
        className
      )}
      onClick={() => onItemClicked(value)}
    >
      <div
        className={clsx(
          isActive ? 'text-primary' : 'text-disabled',
          'text-xs transition-all break-words line-clamp-2'
        )}
      >
        {children}
      </div>
    </div>
  )
}

export const ButtonRadioItem3: FC<ButtonRadioItemProps> = ({
  children, value, className
}) => {
  const { value: activeValue, onItemClicked, disabled } = useContext(ButtonRadioContext)
  const isActive = activeValue === value

  return (
    <div
      className={clsx(
        disabled && 'bg-primary-200 select-none',
        (isActive && !disabled) ? 'bg-primary border-primary' : 'bg-[#F7F7F7]',
        (isActive && disabled) ? 'bg-light border-primary' : 'bg-[#F7F7F7]',
        'rounded h-8 w-full border border-transparent',
        'transition-all flex justify-center items-center',
        className
      )}
      onClick={() => {
        if (disabled) return
        return  onItemClicked(value)
      }}
    >
      <div
        className={clsx(
          isActive ? 'text-white' : 'text-disabled',
          'text-2xs transition-all text-nowrap'
        )}
      >
        {children}
      </div>
    </div>
  )
}

