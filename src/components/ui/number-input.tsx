import * as React from 'react'
import { Input } from '@/components/ui/input'
import { cn } from '@/libs/utils'

import { useState } from 'react'
import { MinusIcon, PlusIcon } from 'lucide-react'

interface NumberInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  min?: number
  max?: number
  step?: number
  onValueChange?: (value: number) => void
  emitChangeOnEnterKey?: boolean
}

export const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  (props, forwardedRef) => {
    const {
      min = -Infinity, max = Infinity, step = 1, value,
      className, onValueChange, emitChangeOnEnterKey = false,
      ...restProps
    } = props

    const internalRef = React.useRef<HTMLInputElement>(null)
    const [internalValue, setInternalValue] = useState<number>(Number(value))

    const emitChangedValue = (val: number) => {
      if (val < min) val = min
      if (val > max) val = max
      setInternalValue(val)
      onValueChange?.(val)
    }

    const handleChange = (delta: number) => {
      const input = internalRef.current
      if (!input) return

      const currentValue = parseFloat(input.value) || 0
      emitChangedValue(currentValue + delta)
    }

    const buttonClassname = 'flex justify-center items-center size-6 border-none bg-gray-100 rounded-xs'
    const iconClassname = 'text-gray-800 size-4'

    return (
      <div className={cn('flex items-center space-x-1', className)}>
        <button
          className={buttonClassname}
          onClick={internalValue > min ? () => handleChange(-step) : undefined}
        >
          <MinusIcon className={cn(iconClassname, internalValue <= min && 'text-gray-300')} />
        </button>

        <Input
          type="number"
          ref={node => {
            // @ts-ignore
            internalRef.current = node
            if (typeof forwardedRef === 'function') {
              forwardedRef(node)
            } else if (forwardedRef) {
              (forwardedRef as any).current = node
            }
          }}
          min={min}
          max={max}
          step={step}
          {...restProps}
          value={internalValue}
          onChange={e => {
            setInternalValue(e.target.valueAsNumber)
          }}
          className="text-center rounded-xs w-16 h-6 bg-gray-100 px-1 border-none shadow-none"
          onKeyDown={
            emitChangeOnEnterKey
              ? ((e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  console.log(internalRef?.current?.blur?.())
                  emitChangedValue?.(internalValue)
                }
              }) as any
              : restProps.onKeyDown
          }
        />

        <button
          className={buttonClassname}
          onClick={internalValue < max ? () => handleChange(step) : undefined}
        >
          <PlusIcon className={cn(iconClassname, internalValue >= max && 'text-gray-300')}  />
        </button>
      </div>
    )
  }
)

NumberInput.displayName = 'NumberInput'
