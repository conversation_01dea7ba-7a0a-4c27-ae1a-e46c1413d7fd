import { FC, HTMLAttributes, useCallback, useEffect, useState } from 'react'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { GeneralMenuKeys, OtherMenuKeys } from '@/components/CanvasMenu'
import { twMerge } from 'tailwind-merge'

export type TabMenuItemProps = {
  name: string
  key: string
  icon?: any
}

export type TabMenuProps = Pick<HTMLAttributes<HTMLDivElement>, 'className' | 'style' | 'id'> & {
  value: string | null
  items: TabMenuItemProps[]

  disabled?: boolean
  onChange?: (item: TabMenuItemProps) => void
  itemClassName?: HTMLAttributes<HTMLDivElement>['className']
}

export const TabMenu: FC<TabMenuProps> = ({
  value, items, onChange, className, style, itemClassName, id, disabled = false
}) => {
  const [active, setActive] = useState<string | null>(items[0]?.key)
  const { t } = useTranslation()

  const handleChangeTab = useCallback((item: TabMenuItemProps) => {
    if (disabled) return

    setActive(item.key)
    onChange?.(item)
  }, [disabled, onChange])

  const shouldShowItem = useCallback((item: TabMenuItemProps) => {
    return (item.key !== GeneralMenuKeys.material && active !== item.key)
      || (
        item.key === GeneralMenuKeys.material &&
        active !== OtherMenuKeys.bitmap &&
        active !== OtherMenuKeys.vector
      )
  }, [active])

  useEffect(() => {
    setActive(value)
  }, [value])

  return (
    <div id={id} className={twMerge('flex justify-start gap-3', className)} style={style}>
      {items
        .map(item => ({ ...item, visible: shouldShowItem(item) }))
        .map(item => (
          <div
            key={item.key}
            className={clsx('flex flex-col gap-1 text-nowrap whitespace-nowrap items-center', itemClassName)}
            onClick={() => handleChangeTab(item)}
          >
            {item.icon && (
              <div className="mb-1">
                {item.icon}
              </div>
            )}

            <span
              className={clsx(
                'text-sm',
                disabled
                  ? 'text-[#aaa]'
                  : item.visible
                    ? 'text-[#666666]'
                    : 'text-[#333333] font-bold'
              )}
            >
              {t(item.name)}
            </span>

            <div
              className={clsx(
                'w-full h-[4px] rounded-md from-[#F18D00] to-[#FFCA32] bg-gradient-to-r',
                item.visible && 'invisible'
              )}
            />
          </div>
        ))}
    </div>
  )
}
