import { FormControl, } from '@mui/material'
import styled from '@emotion/styled'

export const RowFormControl = styled(FormControl)({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: 16,
  width: '100%',
  '& > label.MuiFormLabel-colorPrimary': {
    fontSize: 12,
    color: '#999999'
  },
  '.MuiFormControlLabel-label': {
    fontSize: 14,
    color: '#333'
  }
})

export function CustomTabPanel(props: any) {
  const { children, value, index, ...rest } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      className="p-0"
      {...rest}
    >
      {value === index && <div className="">{children}</div>}
    </div>
  )
}
