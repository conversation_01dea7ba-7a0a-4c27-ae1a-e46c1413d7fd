import { useFabric } from '@/contexts/fabric.context'
import UndoIcon from '@/assets/icons/undo.svg?react'
import RedoIcon from '@/assets/icons/redo.svg?react'
import AimIcon from '@/assets/icons/aim.svg?react'
import DownloadIcon from '@/assets/icons/download.svg?react'
import SaveIcon from '@/assets/icons/saveImg.svg?react'
import CopyIcon from '@/assets/icons/layer_op_copy.svg?react'
import DeleteIcon from '@/assets/icons/layer_op_delete.svg?react'
import React, { useCallback, useMemo } from 'react'
import clsx from 'clsx'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { EngravingMenuKeys, PublicMenuKeys } from '@/components/CanvasMenu'
import { useTranslation } from 'react-i18next'
import toast from 'react-hot-toast'
import { ZIndices } from '@/constants/z-index'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'
import { CANVAS_CACHE_STORAGE_KEY } from '@/libs/fabricjs-react/plugins/export.plugin'
import { useGlobalLoading } from '@/contexts/loading'
import { ObjectType } from '@/libs/fabricjs-react/constants'
import { debounce } from 'lodash'

export const CanvasToolbar = () => {
  const { t } = useTranslation()
  const { withLoading } = useGlobalLoading()
  const { editor, selection, state } = useFabric()
  const { activeMenuKey, setActiveMenuKey } = useCanvasMenu()

  const handleExport = async () => {
    return withLoading(
      () => editor.exportCanvasAsJson(),
      t('hints.exporting-canvas-data')
    )
      .then(data => {
        if (window.ReactNativeWebView) {
          sendMessageToNative(MessageAction.SAVE, data)
        } else {
          localStorage?.setItem?.(CANVAS_CACHE_STORAGE_KEY, JSON.stringify(data))
          toast.success('保存成功')
        }
      })
  }

  const handleUndo = useCallback(debounce(() => {
    if (!state.undoCount) return

    return withLoading(() => editor.undo())
  }, 500), [state])

  const handleRedo = useCallback(debounce(() => {
    if (!state.redoCount) return

    return withLoading(() => editor.redo())
  }, 500), [state])

  const available = useMemo(() => !!selection && !selection.isLocked, [selection])

  if (activeMenuKey === EngravingMenuKeys.engrave || activeMenuKey === EngravingMenuKeys.parameter) {
    return null
  }

  return (
    <div
      style={{ backdropFilter: 'blur(5px)', zIndex: ZIndices.Base }}
      className="flex flex-col w-full bg-white/50 text-white text-2xs relative"
    >
      <div
        className="flex flex-row items-center justify-between w-full bg-white/50 px-3 h-12 gap-5"
      >
        {import.meta.env.DEV && !!state.zoom && (
          <div className="absolute left-4 top-[-16px] text-gray-600">{(state.zoom * 100).toFixed()}%</div>
        )}

        <div className="flex gap-[calc(min(4.26vw,16px))] opacity-60">
          <SaveIcon className="max-h-5 max-w-5 size-[5vw]" onClick={handleExport} />

          <div className="relative" onClick={handleUndo}>
            <UndoIcon
              style={{ color: (state.undoCount ?? 0) >= 1 ? '#333' : '#999' }}
              className="max-h-5 max-w-5 size-[5vw]"
            />
            {!!state.undoCount && <div className="absolute top-[-4px] right-[-3px] text-black text-[10px]">{state.undoCount}</div>}
          </div>

          <div className="relative" onClick={handleRedo}>
            <RedoIcon
              style={{ color: (state.redoCount ?? 0) >= 1 ? '#333' : '#999' }}
              className="max-h-5 max-w-5 size-[5vw]"
            />
            {!!state.redoCount && <div className="absolute top-[-4px] right-[-5px] text-black text-[10px]">{state.redoCount}</div>}
          </div>

          <AimIcon
            style={{ color: available ? '#333' : '#999' }}
            className="max-h-5 max-w-5 size-[5vw]"
            onClick={available ? () => editor.centerActiveObjects() : undefined}
          />

          <CopyIcon
            className="max-h-5 max-w-5 size-[5vw]"
            style={{ color: selection && !selection.isLocked ? '#333' : '#999' }}
            onClick={() => {
              if (!available) return
              withLoading(() => editor.duplicateActiveObjects())
            }}
          />

          <DeleteIcon
            style={{ color: selection && !selection.isLocked ? '#333' : '#999' }}
            className="max-h-5 max-w-5 size-[5vw]"
            onClick={available ? () => selection && editor.removeActiveObjects() : undefined}
          />

          {import.meta.env.DEV && selection?.type === ObjectType.VECTOR && (
            <DownloadIcon className="size-[5vw] max-h-5 max-w-5" onClick={() => editor.__dev__exportActiveObject()} />
          )}

          {import.meta.env.DEV && selection?.type === ObjectType.IMAGE && (
            <DownloadIcon className="size-[5vw] max-h-5 max-w-5" onClick={() => editor.__dev__exportBase64Image(selection?.object.getSrc())} />
          )}
        </div>
        <div className="flex flex-row text-disabled text-sm">
          <div
            className={clsx(
              'ml-2',
              available ? 'text-[#333]' : 'text-disabled',
              (activeMenuKey === PublicMenuKeys.canvasAlign) && '!text-primary'
            )}
            onClick={ () => {
              if (!available) return
              setActiveMenuKey(activeMenuKey === PublicMenuKeys.canvasAlign ? null : PublicMenuKeys.canvasAlign)
            }}
          >
            {t('menus.text.align')}
          </div>
          <div
            className={clsx(
              'ml-2 text-[#333]',
              (activeMenuKey === PublicMenuKeys.workMode) ? 'text-primary' : '')}
            onClick={() => setActiveMenuKey(PublicMenuKeys.workMode)}
          >
            {t('engraving-mode')}
          </div>
        </div>
      </div>
    </div>
  )
}
