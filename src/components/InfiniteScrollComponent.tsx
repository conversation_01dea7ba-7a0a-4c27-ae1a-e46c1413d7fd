import React, { FC, PropsWithChildren, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'

const InfiniteScrollWithReactQuery: FC<{ hasNextPage, fetchNextPage, isFetchingNextPage } & PropsWithChildren> = ({
  hasNextPage, fetchNextPage, isFetchingNextPage, children
}) => {
  const { t } = useTranslation()

  const observerRef = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage()
        }
      },
      { threshold: 0.5 }
    )

    if (observerRef.current) {
      observer.observe(observerRef.current)
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current)
      }
    }
  }, [fetchNextPage, hasNextPage])

  return (
    <div>
      {isFetchingNextPage === 'loading' && <p>{t('loading')}</p>}

      {children}

      <div ref={observerRef} className="text-sm text-gray-500 font-light pt-2">
        {
          isFetchingNextPage
            ? <p>{t('fetching-more-data-text')}...</p>
            : hasNextPage
              ? <p>{t('scroll-to-fetch-more')}</p>
              : null
        }
      </div>
    </div>
  )
}

export default InfiniteScrollWithReactQuery
