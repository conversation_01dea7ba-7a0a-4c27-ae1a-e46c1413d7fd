@import "./index.css";

#root {
    max-width: 1280px;
    margin: 0 auto;
    text-align: center;
    padding: 0;
    width: 100svw;
    height: 100svh;
}

.scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

@utility shadow-default {
    box-shadow: 0 -4px 10px 0 #0000000D;
}

.fadein-popup-handler-content {
    @apply w-full bg-white rounded-t-xl shadow-default
}

.MuiMenu-paper.MuiPopover-paper {
    max-height: 240px;
}

@font-face {
    font-family: 'Times New Roman';
    font-style: normal;
    font-weight: 400;
    src: url("./assets/fonts/times.ttf") format("truetype");
}

/* Prefetch default font for Text object */
@font-face {
    font-family: 'NotoSansSC-Regular';
    font-style: normal;
    font-weight: 400;
    src: url("./assets/fonts/NotoSansSC-Regular.ttf") format("truetype");
}

.ghost-range {
    -webkit-appearance: none;
    background: transparent;

    /*height: 4px;*/
    /*background: #e5e7eb;*/
    /*border-radius: 2px;*/
}

/* Chromium/WebKit thumb */
.ghost-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 40px;
    width: 40px;

    opacity: 0;
    /*background: #3b82f6; !* Tailwind blue-500 *!*/
    /*border-radius: 50%;*/
    /*cursor: pointer;*/
}
