import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { config } from '@/datasource/request/config'
import qs from 'qs'


const { base_url } = config


export const service: AxiosInstance = axios.create({
  baseURL: base_url,
  timeout: 30000,
  withCredentials: false
})

const handleAuthorized = () => {
  return Promise.reject()
}

function logResponseError(response?: AxiosResponse<any, any>) {
  console.error(
    `Request Url: ${decodeURIComponent(response?.config.url || response?.request.url || '')}
     Request Method: ${response?.config.method || response?.request.method}
     Request Body/Params: ${JSON.stringify(response?.config.data || response?.request.data || response?.request.params)}
     Response: ${JSON.stringify(response?.data)}
  `)
}

const interceptors = {
  request: {
    fulfilled: async (config: InternalAxiosRequestConfig) => {
      const { locale = 'en-US', access_token } = Object
        .fromEntries(
          new URLSearchParams(window.location.search).entries()
        )

      if (access_token) {
        config.headers.Authorization =  `Bearer ${access_token}`
      }


      config.headers['tenant-id'] = '1'
      // config.headers['Accept-Language'] = locale
      config.headers['Accept-Language'] = import.meta.env.MODE === 'intl' ? 'en-US' : 'zh-CN'

      const params = config.params || {}
      const data = config.data || false
      const method = config.method?.toUpperCase()

      if (
        config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
          && method === 'POST'
      ) {
        config.data = qs.stringify(data)
      }

      // get参数编码
      if (method === 'GET' && params) {
        config.params = {}
        const paramsStr = qs.stringify(params, { allowDots: true })
        if (paramsStr) {
          config.url = config.url + '?' + paramsStr
        }
      }


      return config
    },
    rejected: (error: AxiosError) => {

      return Promise.reject(error)
    }
  },
  response: {
    fulfilled: async (response: AxiosResponse<any>) => {
      let { data } = response

      if (!data) {
        throw new Error('Response with null body')
      }


      if (
        response.request.responseType === 'blob' ||
        response.request.responseType === 'arraybuffer'
      ) {
        if (response.data.type !== 'application/json') {
          return response.data
        }
        data = await new Response(response.data).json()

      }

      const code = data.code

      if (code !== 0) {

        logResponseError(response)

        if (code === 401) {
          return handleAuthorized()
        }


        return Promise.reject(new Error(data.msg || 'Unknown error'))
      }

      return data
    },
    rejected: (error: AxiosError) => {

      const response = error.response
      logResponseError(response)

      if ((response?.data as any)?.code === 401) {
        return handleAuthorized()
      }

      return Promise.reject(error)
    }
  }
}

service.interceptors.request.use(interceptors.request.fulfilled, interceptors.request.rejected)
service.interceptors.response.use(interceptors.response.fulfilled, interceptors.response.rejected)
