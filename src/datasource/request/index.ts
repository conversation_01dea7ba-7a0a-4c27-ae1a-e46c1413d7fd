import { service } from './service'

import { config } from './config'
import { AxiosRequestConfig } from 'axios'
import { PageObject, PageParam } from '@/datasource/request/types'

const { default_headers } = config

const buildRequest = (request_impl: (options: any) => Promise<any>) => ({
  get: async <T = any>(
    url: string,
    params?: AxiosRequestConfig['params'],
    options?: Omit<AxiosRequestConfig, 'data' | 'url' | 'method' | 'params'>
  ) => {
    options ||= {}

    const res = await request_impl({ ...options, url, method: 'GET', params  })
    return res.data as unknown as T
  },
  getPages: async <Item = any, Params extends PageParam = PageParam>(
    url: string,
    params?: Params,
    options?: Omit<AxiosRequestConfig, 'data' | 'url' | 'method' | 'params'>
  ): Promise<PageObject<Item>> => {
    options ||= {}
    const { pageSize = 10, pageNo = 1, ...rest } = params || {}

    const res = await request_impl({
      ...options,
      url,
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        ...rest
      }
    })

    return res.data as unknown as PageObject<Item> || null
  },
  post: async <T = any>(
    url: string,
    options?: Omit<AxiosRequestConfig, 'url' | 'method'>
  ) => {
    options ||= {}

    const res = await request_impl({ ...options, url, method: 'POST' })
    return res.data as unknown as T
  },
  postOriginal: async (option: any) => {
    const res = await request_impl({ method: 'POST', ...option })
    return res || null
  },
  delete: async <T = any>(option: any) => {
    const res = await request_impl({ method: 'DELETE', ...option })
    return res.data as unknown as T || null
  },
  put: async <T = any>(option: AxiosRequestConfig) => {
    const res = await request_impl({ method: 'PUT', ...option })
    return res.data as unknown as T || null
  },
  download: async <T = any>(option: any) => {
    const res = await request_impl({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request_impl({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  }
})

export const request = buildRequest((option: any) => {
  const { url, method, params, data, headersType, responseType, ...config } = option

  return service.request({
    url,
    method,
    params,
    data,
    ...config,
    responseType,
    headers: {
      'Content-Type': headersType || default_headers
    }
  })
})
