import { request } from '@/datasource/request'

export enum MaterialTypeEnum {
  default = 'default',
  customize =  'customize'
}

export const MaterialTypeCode = {
  [MaterialTypeEnum.customize]: 1,
  [MaterialTypeEnum.default]: 0
}

export type Material = {
  createTime: number;
  description: string;
  id: number;
  imageUrl: string;
  name: string;
  status: number;
  /**
   * 0：系统默认： 1是自定义
   */
  type: number;
  userId: number;
  [property: string]: any;
}


export type MaterialParameter = {
  bitmapEnabled: number;
  bitmapPower: number;
  bitmapResolution: number;
  bitmapSpeed: number;
  bitmapImageUrl: string
  createTime: number;
  description: string;
  id: number;
  imageUrl: string;
  materialId: number;
  name: string;
  status: number;
  /**
   * 0：系统 1 ： 自定义
   */
  type: number;
  userId: number | null;
  vectorEnabled: number;
  vectorPower: number | null;
  vectorSpeed: number | null;
  vectorImageUrl: string
  [property: string]: any;
}

export interface CreateParameterProps {
  bitmapEnabled: number | null;
  bitmapPower: number | null;
  bitmapResolution: number | null;
  bitmapSpeed: number | null;
  deviceModelId: string | null;
  name: string | null;
  vectorEnabled: number | null;
  vectorPower: number | null;
  vectorSpeed: number | null;

  materialId?: number
  description?: string
  imageUrl?: string
}

export const MaterialModule = {
  listMaterials() {
    return request.get<Material[]>('/app-api/device/v1/carving/material/list')
  },
  listParameters(materialId: number, deviceModelId: string) {
    return request.get<MaterialParameter[]>('/app-api/device/v1/carving/parameters/list', { materialId, deviceModelId })
  },
  createParameters(data: CreateParameterProps) {
    return request.post<number>('/app-api/device/v1/carving/parameters/create', { data })
  },
  createMaterial(data: { name: string, description?: string, imageUrl?: string }) {
    return request.post('/app-api/device/v1/carving/material/create', { data })
  },
  deleteMaterial(id) {
    return request.delete({ url: '/app-api/device/v1/carving/material/delete', params: { id } })
  },
  deleteParameter(id) {
    return request.delete({ url: '/app-api/device/v1/carving/parameters/delete', params: { id } })
  },
  updateParameter(data: CreateParameterProps & { id: number | null }) {
    return request.post('/app-api/device/v1/carving/parameters/update', { data })
  },
  templateDetail(id: string) {
    return request.post('/app-api/community/v1/template-view/feed', { data: { id } })
  },
}
