import { PageParam } from '@/datasource/request/types'

export enum PostAuditState {
  awaiting = 0,
  pending = 1,
  approve = 2,
  failed = 3
}

export const PostAuditStateByLabel = {
  [PostAuditState.awaiting]: 'awaiting',
  [PostAuditState.pending]: 'pending',
  [PostAuditState.approve]: 'approve',
  [PostAuditState.failed]: 'failed',
}

export enum ContentType {
  text = 'text',
  richText = 'richText',
}

export enum SocialEnums {
  creative = 'creative',
  vector = 'vector',
  bitmap = 'bitmap',
  experience = 'experience',
  event = 'event',
  template = 'template',
  content = 'content'

}

export enum MediaType {
  photo = 'photo',
  video = 'video',
  vector = 'vector',
}

export enum PostTopType {
  suggest = 1
}

export const SocialTypeMap: Record<SocialEnums, { typeId: number, categoryId?: number }> = {
  [SocialEnums.creative]: {
    typeId: 14,
    categoryId: 15
  },
  [SocialEnums.experience]: {
    typeId: 14,
    categoryId: 16
  },
  [SocialEnums.event]: {
    typeId: 10,
    categoryId: 10
  },
  [SocialEnums.vector]: {
    typeId: 12,
  },
  [SocialEnums.bitmap]: {
    typeId: 13,
  },
  [SocialEnums.template]: {
    typeId: 11,
  },
  [SocialEnums.content]: {
    typeId: 17
  }
}

export type Social = keyof typeof SocialEnums

export enum SortTypeFilterKey {
  new = 'new',
  hot = 'hot',
  follow = 'follow'
}

export interface PostDataParams extends PageParam {
  typeIds: number[],
  categoryIds?: (string | number)[] | null
  sortType: string,
  top?: PostTopType
}

export interface PostViewData {
  typeId: number
  categoryId: number
  content: string
  contentType: ContentType | MediaType | SocialEnums
  createTime: number
  hasFollow: boolean
  headImage?: MediaImagesData
  images?: MediaImagesData[]
  id: string
  interactInfo: InteractInfo
  tags?: string[]
  title: string
  user: User
  laser: LaserData
  voteTitle: string,
  voteType: number,
  auditState: PostAuditState,
  linkId: string,
  canvas?: string,
  subCategoryId?: number,
}

export interface CategoryParams {
  parentId?: number | string
}

export interface CategoryData {
  config: string;
  createTime: number;
  id: number;
  name: string;
  parentId: number;
  remark: string;
  sort: number;
  status: number;
  type: number;
}

export interface InteractInfo {
  collected: boolean
  collectedCount: number
  commentCount: number
  followed: boolean
  liked: boolean
  likedCount: number
  refCount: number
  shareCount: number
  views: number
}

export type MediaImagesData = {
  type: MediaType.photo | MediaType.video | MediaType.vector,
  url: string
}

export interface LaserData {
  modelId: string
  materialId: string
  engravingPower: string
  engravingSpeed: string
  resolutionId: string
}

export interface User {
  userId: number
  nickname: string
  avatar: string
  description: string
  sex: number,
  cover?: string
}

