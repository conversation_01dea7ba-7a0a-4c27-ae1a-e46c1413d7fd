
import { request } from '@/datasource/request'
import {
  PostViewData, PostDataParams, CategoryParams, CategoryData
} from '@/datasource/api/social/types'
import { PageObject } from '@/datasource/request/types'

const SocialModule = {
  postView: {
    list(data: PostDataParams) {
      return request.post<PageObject<PostViewData>>('/app-api/community/v1/post-view/home-feed', { data })
    },
    detail(id: string) {
      return request.post<PostViewData>('/app-api/community/v1/post-view/feed', { data: { id } })
    },
  },
  category: {
    list(data: CategoryParams) {
      return request.get<CategoryData[]>('/app-api/community/v1/category/list', data)
    },

  }
}

export default SocialModule
