export type WebviewInjectedWindow = {
  injected?: {
    device?: {
      model_id: number | undefined
      configurations: {
        transmitMode: TransmitMode
      }
    }
    fonts?: Record<string, string>
  }
}

export enum ExportedLayerType {
  bitmap = 'bitmap',
  vector = 'vector'
}

export type ExportedLayer =
  | {
    type: ExportedLayerType.vector
    data: string
    fill: boolean
    fillAngle?: string
    fillSpace?: string
  }
  | {
    type: ExportedLayerType.bitmap
    data: string
    x: string
    y: string
    width: string
    height: string
  }

export type ExportedCanvasData = {
  version: string
  objects: any[]

  previewImageSrc: string
  updated_at: number
}

export enum InitialAction {
  PAINTING = 'PAINTING',
  TEXT_CONTENT = 'TEXT_CONTENT'
}

export enum MessageAction {
  BACKWARD = 'BACKWARD',
  TRIGGER_BACKWARD = 'TRIGGER_BACKWARD',
  SAVE = 'SAVE',
  SAVE_DRAFT = 'SAVE_DRAFT',
  PREPARED = 'PREPARED',
  LONG_PRESS = 'LONG_PRESS',
  IMPORT_IMAGE = 'IMPORT_IMAGE',
  IMPORT_FILE = 'IMPORT_FILE',
  LOAD_ONLINE_FILE = 'LOAD_ONLINE_FILE',
  LOAD_FONTS = 'LOAD_FONTS',
  REMOVE_FONT = 'REMOVE_FONT',
  START_PREVIEW = 'START_PREVIEW',
  STOP_PREVIEW = 'STOP_PREVIEW',
  START_ENGRAVING = 'START_ENGRAVING',
  STOP_ENGRAVING = 'STOP_ENGRAVING',
  PAUSE_ENGRAVING = 'PAUSE_ENGRAVING',
  RESUME_ENGRAVING = 'RESUME_ENGRAVING',
  NOTIFY_DEVICE_STATUS = 'NOTIFY_DEVICE_STATUS',
  GO_BACK_HOME = 'GO_BACK_HOME',
  CALL_AI_GENERATING = 'CALL_AI_GENERATING',
  CALL_AI_OPTIMIZING = 'CALL_AI_OPTIMIZING',
  REQUEST_DEVICE_IMAGE = 'REQUEST_DEVICE_IMAGE',
  OPEN_DEVICE_FILE_PICKER = 'OPEN_DEVICE_FILE_PICKER',
  EXPORT_PAINTING_RESULT = 'EXPORT_PAINTING_RESULT',
  RESOURCE_LOAD_END = 'RESOURCE_LOAD_END',
  FONTS_LOAD_END = 'FONTS_LOAD_END',
  DEVICE_NOT_CONNECT_WARNING = 'DEVICE_NOT_CONNECT_WARNING',
  REPLACE_IMAGE_SRC = 'REPLACE_IMAGE_SRC',
  __DEV__EXPORT_CURRENT_LAYER_AS_SVG = '__DEV__EXPORT_CURRENT_LAYER_AS_SVG',
  CAN_TO_ENGRAVE = 'CAN_TO_ENGRAVE',
  READY_ENGRAVING = 'READY_ENGRAVING',
  HAND_MODE_STATUS = 'HAND_MODE_STATUS',
  UPDATE_DEVICE_NATIVE_CONFIG = 'UPDATE_DEVICE_NATIVE_CONFIG',
  GET_DEVICE_CONFIG = 'GET_DEVICE_CONFIG',
  UPDATE_DEVICE_CANVAS_CONFIG = 'UPDATE_DEVICE_CANVAS_CONFIG',
  REQUEST_DEVICE_MATERIAL = 'REQUEST_DEVICE_MATERIAL',
  IMPORT_MATERIAL_TYPE = 'IMPORT_MATERIAL_TYPE',
  OPEN_DEVICE_CONFIG = 'OPEN_DEVICE_CONFIG',
  DEVICE_IF_FAULT = 'DEVICE_IF_FAULT',
  UPDATE_DEVICE_FAULT = 'UPDATE_DEVICE_FAULT',
  DEVICE_IS_FAULT = 'DEVICE_IS_FAULT',
  IF_SAVE = 'IF_SAVE'
}

export type MessageData = {
  action: MessageAction
  payload: any
}

export enum PreviewMode {
  PREVIEW_SINGLE_RECT = '0',      // 预览所有对象的总外框矩形

  PREVIEW_PATH = '1',              // 预览每个对象的路径轮廓

  /**
   * @deprecated
   */
  PREVIEW_RECT = '2',             // 预览每个对象的外框矩形
}

export enum TransmitMode {
  /**
   * 传输和标刻同时进行
   */
  TRANS_UNITE_MARK = 0,

  /**
   * 先传输完最后再标刻
   */
  FIRST_TRANS_LAST_MARK = 1,

  /**
   * 只传输不标刻
   */
  ONLY_TRANS_NO_MARK = 2,

  /**
   * 只计算标刻时间(不传输也不标刻)
   */
  ONLY_CALC_MARKTIME = 3
}

export enum WorkMode {
  PLANE = 'plane',
  ROTATION = 'rotation',
  TABLET = 'tablet',
  HANDHELD = 'handheld'
}

// TODO
export const SUPPORTED_DPI_OPTIONS = [
  400, 300, 250, 200
]

export enum DeviceWorkingStatus {
  PREVIEWING = 999,
  IDLE = 0,
  PREPARED = 1,
  DATA_TRANSMITTING = 10,
  ENGRAVING = 20,
  PAUSING = 30,
  FINISHED = 40,
  CANCELED = 99
}

export enum DeviceStatusCode {
  IDLE = 0,
  ENGRAVING = 1,
  PREVIEWING = 2,
  PAUSING = 3
}

export enum LengthUnit {
  mm = 'mm',
  inch = 'inch'
}

export type DeviceCanvasConfig = {
  lengthUnit: LengthUnit
  diameter: number
  length: number,
  workMode: WorkMode
}

export type MessagePayloadMap = {
  [MessageAction.START_PREVIEW]: {
    layers: ExportedLayer[],
    workMode: WorkMode,
    previewMode: PreviewMode,
    length?: number,
    diameter?: number
  }

  [MessageAction.START_ENGRAVING]: {
    layers: ExportedLayer[]
    workMode: WorkMode
    diameter: number
    bitmapPower: number
    bitmapMarkTime: number
    bitmapDPI?: number
    vectorPower: number
    vectorSpeed: number
    numberOfEngraving: number
    canvasData: ExportedCanvasData
    materialName?: string
    containBitmap?: boolean
    containVector?: boolean
    length: number
    materialInfo: object | undefined
    parameterInfo: object | undefined
    unit: LengthUnit,
    isRepeat?: boolean
  }

  [MessageAction.NOTIFY_DEVICE_STATUS]:
    | { event: 'disconnect' }
    | {
      event: 'connect',
      deviceModelId: number
    }
    | {
      event: 'working-status-update'
      currentStatus: DeviceWorkingStatus
      engravingProgress?: number
      estimatedEngravingTime?: number
      dataTransmissionProgress?: number
      engravingTime?: number
      dataSize?: number
    }
    | {
      event: 'device-status',
      engravingStatus: number
      engravingProgress: number
      estimatedEngravingTime: number
      dataTransmissionProgress: number
    }

  [MessageAction.SAVE]: ExportedCanvasData

  [MessageAction.SAVE_DRAFT]: ExportedCanvasData

  [MessageAction.OPEN_DEVICE_FILE_PICKER]: {
    type?: 'font' | 'material'
  },

  [MessageAction.LOAD_FONTS]: string[]

  [MessageAction.FONTS_LOAD_END]: string[] | null

  [MessageAction.UPDATE_DEVICE_NATIVE_CONFIG]: {
    workMode?: WorkMode
    diameter?: number
    length?: number
    unit?: LengthUnit
  }

  [MessageAction.UPDATE_DEVICE_FAULT]: string[]

  [MessageAction.UPDATE_DEVICE_CANVAS_CONFIG]: DeviceCanvasConfig

  [MessageAction.IF_SAVE]: boolean
}

export type MessagePayload<T extends MessageAction> = T extends keyof MessagePayloadMap ? MessagePayloadMap[T] : string
