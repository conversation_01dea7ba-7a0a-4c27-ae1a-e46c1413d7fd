import * as fabric from 'fabric'
import { T2DPipelineState, TWebGLUniformLocationMap } from 'fabric'

/**
 * 素描滤镜
 */
export class PencilSketchFilter extends fabric.filters.BaseFilter<'PencilSketch', NonNullable<unknown>> {
  static type = 'PencilSketch'
  static uniformLocations = ['uLineRate', 'uTexSize']

  static defaults = {
    lineRate: 10.0
  }

  declare lineRate: number

  /**
   * Apply the Pixelate operation to a Uint8ClampedArray representing the pixels of an image.
   *
   * @param {Object} options
   * @param {ImageData} options.imageData The Uint8ClampedArray to be filtered.
   */
  applyTo2d({ imageData: { data, width, height } }: T2DPipelineState) {}

  /**
   * Indicate when the filter is not gonna apply changes to the image
   **/
  isNeutralState() {
    return this.lineRate === 1
  }

  protected getFragmentSource(): string {
    return `
    precision mediump float;
    varying vec2 vTexCoord;
    uniform sampler2D uTex;
    uniform vec2 uTexSize;
    uniform float uLineRate;

    void main() {
        vec2 size = vec2(10.0 / uTexSize.x, 10.0 / uTexSize.y);
        float big = 0.0, small = 0.0, c;

        // Calculate surrounding pixel values
        for(float x = -1.0; x <= 1.0; x += 1.0) {
            for(float y = -1.0; y <= 1.0; y += 1.0) {
                c = texture2D(uTex, vTexCoord + size * vec2(x, y)).g;
                big += c;

                // Get the current pixel value for the central line
                if(x == 0.0 || y == 0.0)
                    small += c;
            }
        }

        // Calculate the edge based on the difference between big and small values
        float edge = max(0.0, big / 9.0 - small / 5.0);


        // Calculate the final color, including the transparency adjustment
        vec4 old_color = texture2D(uTex, vTexCoord);
        float average = (old_color.r + old_color.b + old_color.g) / 3.0 ;
        float factor = average * 3.0;
        vec3 color = vec3(factor) - (edge * edge * uLineRate * 1000.0);
        
        // Set alpha to 0 for white or near-white colors

        float alpha = old_color.a * 1.0;
        // Apply the lineRate factor to control the level of sketch detail
        gl_FragColor = vec4(color, alpha);
    }
   `
  }

  /**
   * Send data from this filter to its shader program's uniforms.
   *
   * @param {WebGLRenderingContext} gl The GL canvas context used to compile this filter's shader.
   * @param {Object} uniformLocations A map of string uniform names to WebGLUniformLocation objects
   */
  sendUniformData(
    gl: WebGLRenderingContext,
    uniformLocations: TWebGLUniformLocationMap
  ) {
    const texSize = [gl.drawingBufferWidth, gl.drawingBufferHeight]
    gl.uniform1f(uniformLocations.uLineRate, this.lineRate)
    gl.uniform2fv(uniformLocations.uTexSize, texSize)
  }
}

fabric.classRegistry.setClass(PencilSketchFilter)
