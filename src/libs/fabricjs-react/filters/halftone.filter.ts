import * as fabric from 'fabric'
import { T2DPipelineState, TWebGLUniformLocationMap } from 'fabric'

type HalftoneProps = {
  blocksize: number
  threshold: number
  width: number
  height: number

  /**
   * 点密度
   */
  density: number

  /**
   * 点大小缩放因子
   */
  dotScaling: number
}

/**
 * 网格化滤镜
 */
export class HalftoneFilter extends fabric.filters.BaseFilter<'Halftone', HalftoneProps> {
  static type = 'Halftone'

  static defaults = {
    blocksize: 30,
    threshold: 0.1,
    density: 1.0,
    dotScaling: 1.0,
    width: 0,
    height: 0
  }

  declare blocksize: number
  declare threshold: number
  declare density: number
  declare dotScaling: number
  declare width: number
  declare height: number

  static uniformLocations = [
    'uBlocksize',
    'uThreshold',
    'uResolution',
    'uDotScaling',
    'uDensity',
  ]

  /**
   * Apply the Pixelate operation to a Uint8ClampedArray representing the pixels of an image.
   *
   * @param {Object} options
   * @param {ImageData} options.imageData The Uint8ClampedArray to be filtered.
   */
  applyTo2d({ imageData: { data, width, height } }: T2DPipelineState) {}

  /**
   * Indicate when the filter is not gonna apply changes to the image
   **/
  isNeutralState() {
    return this.blocksize === 1
  }

  protected getFragmentSource(): string {
    return `
    precision highp float;
    uniform float uBlocksize;
    uniform float uThreshold;
    uniform vec2 uResolution;
    uniform float uDotScaling;
    uniform float uDensity; 

    varying vec2 vTexCoord;
    uniform sampler2D uTexture;

    float Circle(vec2 pos, float radius) {
        return length(pos) - radius;
    }

    void main() {
      // 计算纵横比
      float w = 1.0 / min(uResolution.x, uResolution.y);
      vec2 uv = vTexCoord;
      vec2 pos = fract(uv * 100.0);
      pos -= 0.5;

      // 获取当前像素颜色，并使用权重将其转换为灰度
      vec4 pixel = texture2D(uTexture, vTexCoord);
      // Convert to greyscale
      const vec3 GREY_WEIGHTS = vec3(0.299, 0.587, 0.114);
      float greyResult = dot(pixel.rgb, GREY_WEIGHTS);
      
      // 获取到圆心的距离。使用灰度亮度的倒数来定义每个圆的半径。
      float radius = (1.0 - greyResult) * uDotScaling;
      float c = Circle(pos, radius);

      // 对圆边缘应用平滑
      float SMOOTHING = 4.0;
      float s = SMOOTHING * w;
      c = 1.0 - smoothstep(s, -s, c);

      // 设置输出片段像素 alpha
      gl_FragColor.a = pixel.a;
      
      //将当前像素颜色乘以圆距离值
      gl_FragColor.rgb = vec3(greyResult * c);
    }
   `
  }

  /**
   * Send data from this filter to its shader program's uniforms.
   *
   * @param {WebGLRenderingContext} gl The GL canvas context used to compile this filter's shader.
   * @param {Object} uniformLocations A map of string uniform names to WebGLUniformLocation objects
   */
  sendUniformData(
    gl: WebGLRenderingContext,
    uniformLocations: TWebGLUniformLocationMap
  ) {
    gl.uniform1f(uniformLocations.uBlocksize, this.blocksize)
    gl.uniform1f(uniformLocations.uThreshold, this.threshold)
    gl.uniform2f(uniformLocations.uResolution, this.width, this.height)
    gl.uniform1f(uniformLocations.uDotScaling, this.dotScaling) // Scaling factor for dot size
    gl.uniform1f(uniformLocations.uDensity, this.density) // Scaling factor for dot size
  }

}

fabric.classRegistry.setClass(HalftoneFilter)
