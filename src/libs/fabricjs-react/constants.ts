import Font_Times from '@/assets/fonts/times.ttf'
import { InteractiveFabricObject, Textbox } from 'fabric'

export const RULER_SIZE = 20
export const WORKSPACE_BORDER_WIDTH = 4
export const CANVAS_OBJECTS_SIZE_LIMIT = 1000

export const POSITION_MAX_X = 0
export const POSITION_MAX_Y = 0

export const VECTOR_FILL_MODES = {
  NO_FILL: '#01020300',
  FILL: '#010101dd',
  PATH_FILL: '#03020166'
}
export const VECTOR_MIN_FILL_SPACE = 0.1
export const VECTOR_MAX_FILL_SPACE = 10
export const VECTOR_NIN_FILL_ANGLE = 0
export const VECTOR_MAX_FILL_ANGLE = 360
export const VECTOR_DEFAULT_STROKE_WIDTH = 16
export const VECTOR_DEFAULT_STROKE_WIDTH_FACTOR = 8 / 2048

export const BITMAP_MAX_SIZE_BYTES = 4 << 20

export const WORKSPACE_DEFAULT_SIZE_MM = 100
export const WORKSPACE_SIZE_PIXELS = 2048

export const ZOOM_MAX_SCALE = 2.5
export const ZOOM_MIN_SCALE = Math.min(window.screen.availWidth, 500) / 2048 * 0.75

export const MM_PER_INCH = 25.4
export const POINT_PER_INCH = 72

export enum ObjectType {
  VECTOR = 'VECTOR',
  IMAGE = 'IMAGE',
  TEXT = 'TEXT',
  GROUP = 'GROUP',
  VECTOR_GROUP = 'VECTOR_GROUP',
  OBJECTS = 'OBJECTS',
  NONE = 'NONE'
}

export enum ObjectTypeContainingSituation {
  NOTHING = 0b00,
  VECTOR_ONLY = 0b10,
  BITMAP_ONLY = 0b01,
  BOTH = 0b11
}

export const OBJECT_LOCKING_PROPERTIES: (keyof InteractiveFabricObject | keyof Textbox)[] = [
  'lockRotation',
  'lockMovementX',
  'lockMovementY',
  'lockScalingY',
  'lockScalingX',
  'lockScalingFlip',
  'lockSkewingX',
  'lockSkewingY'
] as const

export const PROPERTIES_TO_INCLUDE_WHEN_EXPORT = [
  'selectable',
  'editable',
  'hasControl',
  'evented',
  'erasable',
  '__is_system_object__',
  '__filter_type__',
  '_fill_space_',
  '_fill_angle_',
  ...OBJECT_LOCKING_PROPERTIES
]

export const REMOTE_STATIC_FONT_BASE_URL = import.meta.env.VITE_REMOTE_STATIC_FONT_BASE_URL

export const LOCAL_STATIC_FONTS = [
  { label: 'Times New Roman', fontFamily: 'Times New Roman', localUri: Font_Times },
]

export const REMOTE_STATIC_DOMESTIC_FONTS = [
  { label: '阿里妈妈刀隶体', fontFamily: 'AlimamaDaoLiTi', type: 'ttf' },
  { label: '阿里妈妈东方大楷', fontFamily: 'AlimamaDongFangDaKai-Regular', type: 'otf' },
  { label: '鼎猎西大体', fontFamily: 'dingliexidafont-20250329V2)-2', type: 'ttf' },
  { label: 'DOUYU', fontFamily: 'douyuFont-2', type: 'otf' },
  { label: 'MaokenAssortedSans', fontFamily: 'MaoKenShiJinHei-2', type: 'ttf' },
  { label: 'Noto Sans CJK Black', fontFamily: 'NotoSansCJK-Black-7', type: 'otf' },
  { label: '平方赖江湖怀古体', fontFamily: 'PingFangLaiJiangHuHuaiGuTi-2', type: 'ttf' },
  { label: '千图马克手写体', fontFamily: 'QianTuMaKeShouXieTi-2', type: 'ttf' },
  { label: '云峰飞云体', fontFamily: 'YunFengFeiYunTi-2', type: 'ttf' },
  { label: '字库星球飞扬体', fontFamily: 'ZiKuXingQiuFeiYangTi-2', type: 'ttf' }
]

export const REMOTE_STATIC_INTL_FONTS = [
  { label: 'BrickRiotRegular', fontFamily: 'BrickRiotRegular-9Mqm0', type: 'ttf' },
  { label: 'OblataDisplayRegular', fontFamily: 'OblataDisplayRegular-Zp8o8', type: 'otf' },
  { label: 'PlayfairDisplaySemiboldItalic', fontFamily: 'PlayfairDisplaySemiboldItalic-YzEYO', type: 'ttf' },
  { label: 'Playgum', fontFamily: 'Playgum-6RGZv', type: 'otf' },
  { label: 'PqrmondayItalic', fontFamily: 'PqrmondayItalic-3lqoz', type: 'ttf' },
  { label: 'RalewayDots', fontFamily: 'RalewayDots-6pgv', type: 'ttf' },
  { label: 'RalewaySemibold', fontFamily: 'RalewaySemibold-Xzyo', type: 'ttf' },
  { label: 'SkateRegular', fontFamily: 'SkateRegular-lxeZZ', type: 'ttf' },
  { label: 'SuperPancake', fontFamily: 'SuperPancake-xRJLj', type: 'ttf' },
  { label: 'Waytoon', fontFamily: 'Waytoon-pge8K', type: 'otf' },
]

export const DEFAULT_FONT_FAMILY = LOCAL_STATIC_FONTS[0].fontFamily

export const TEXT_FILL_STYLES = {
  fill: VECTOR_FILL_MODES.FILL,
  strokeWidth: 0,
  stroke: undefined
}

export const TEXT_FONT_STYLES = {
  normal: 'normal',
  italic: 'italic'
}

export const TEXT_FONT_WEIGHTS = {
  normal: 'normal',
  bold: 'bold'
}

export const TEXT_STROKE_STYLES = {
  fill: VECTOR_FILL_MODES.NO_FILL,
  strokeWidth: 1,
  stroke: '#000'
}
