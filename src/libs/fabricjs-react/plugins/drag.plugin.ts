import { createPlugin, FabricCoreContext } from '../types/base'
import { getEventPositions, moveViewportTo } from '../utils'
import * as fabric from 'fabric'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'

const LONG_PRESS_DURATION_THRESHOLD = 350

export interface DragUtilities {
}

class DragGestureHandler implements DragUtilities {

  private _lastPosition: fabric.Point | null = null
  private _timer: any | null
  private _mousedownTime: number | null
  private _mouseMoved: boolean = false
  private _mousedownPosition: fabric.Point | null

  constructor (private context: FabricCoreContext) {

  }

  public init() {
    const { canvas, state } = this.context
    canvas.selection = true
    canvas.requestRenderAll()
    this.context.state.isDrawingMode = false

    canvas.on('mouse:down:before', opt => {
      if (canvas.isDrawingMode) return

      const target = opt.target
      const event = opt.e as MouseEvent | TouchEvent

      // console.log('mouse:down')
      // console.log((event as TouchEvent).touches)

      if (!state.activeObjectCache) {
        // canvas.discardActiveObject()
      }

      if (target) {
        // canvas.setActiveObject(target)
        // state.activeObjectCache = target
        // event.preventDefault()
        return
      }

      state.activeObjectCache = undefined

      const [p0, p1] = getEventPositions(event)

      this._mousedownTime = Date.now()

      if (p1) {
        this._mousedownTime = null
        if (this._timer) clearTimeout(this._timer)
        return
      }

      this._timer = setTimeout(() => {
        sendMessageToNative(MessageAction.LONG_PRESS)
      }, LONG_PRESS_DURATION_THRESHOLD)

      if (!p0) return
      this._lastPosition = p0
      this._mousedownPosition = p0
    })

    canvas.on('mouse:move:before', opt => {
      if (this._mouseMoved) return
      // console.log('mouse:move:before')
      const event = opt.e as MouseEvent | TouchEvent
      const [, p1] = getEventPositions(event)

      if (p1) {
        canvas.selection = false
        if (this._timer) clearTimeout(this._timer)
      } else {
        canvas.selection = !canvas.isDrawingMode
          && !!this._mousedownTime
          && (Date.now() - this._mousedownTime > LONG_PRESS_DURATION_THRESHOLD)
      }
    })

    canvas.on('mouse:move', opt => {
      // console.log('mouse:move', Date.now() % 10000)
      // console.log('mouse:move')
      if (canvas.isDrawingMode) return

      const [p0, p1] = getEventPositions(opt.e as TouchEvent | MouseEvent)

      // 坐标不同表明发生了实际移动
      if (this._mousedownPosition && p0 && (p0.x !== this._mousedownPosition.x || p0.y !== this._mousedownPosition.y)) {
        this._mouseMoved = true
        if (this._timer) clearTimeout(this._timer)
      }

      if (canvas.selection) {
        if (p0) this._lastPosition = p0
        canvas.requestRenderAll()
        return
      }

      state.isDragging = true

      if (!canvas.viewportTransform) return
      if (canvas.getActiveObjects().length) return

      if (!p0 || p1) return
      if (!this._lastPosition) {
        this._lastPosition = p0
        return
      }

      const { x: px, y: py } = p0

      const dx = (px - this._lastPosition.x) * 0.95
      const dy = (py - this._lastPosition.y) * 0.95

      moveViewportTo(
        canvas,
        x => x + dx,
        y => y + dy
      )

      this._lastPosition = p0
    })

    canvas.on('mouse:up', opt => {
      if (this._timer) clearTimeout(this._timer)

      const { target } = opt

      if (target && this._mousedownPosition && (this._lastPosition?.eq(this._mousedownPosition))) {
        canvas.setActiveObject(target)
        canvas.requestRenderAll()
        state.activeObjectCache = target
        return
      }

      if (canvas.selection && canvas.getActiveObjects().length) {
        state.activeObjectCache = canvas.getActiveObjects()[0]
      }

      state.isDragging = false
      canvas.selection = true
      this._mousedownTime = null
      this._mouseMoved = false
      this._mousedownPosition = null
    })
  }
}

export const DragPlugin = createPlugin<DragUtilities>(
  context => {
    const handler = new DragGestureHandler(context)

    return ({
      utilities: {
      },
      onInit() {
        handler.init()
      }
    })
  }
)
