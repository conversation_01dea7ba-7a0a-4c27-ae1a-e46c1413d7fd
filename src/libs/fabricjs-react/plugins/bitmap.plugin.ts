import { createPlugin } from '../types/base'
import * as fabric from 'fabric'
import { ImageFilters } from '@/libs/fabricjs-react/types/filters'
import { PaintingFilter } from '../filters/painting.filter'
import { PercentCrop } from 'react-image-crop'
import { HalftoneFilter } from '@/libs/fabricjs-react/filters/halftone.filter'
import { PencilSketchFilter } from '../filters/pencil-sketch.filter'
import { withAtomicOperation } from '@/libs/fabricjs-react/utils'
import { applyFilterWithWhiteBgIfNeeded } from '@/libs/fabricjs-react/utils/add-white-bg'

export interface BitmapUtilities {
  clearFilter(): void
  applyFilter(type: ImageFilters): void

  setCurrentImageSource(src: string): void
  setBrightness(val: number, commit?: true): void
  setContrast(val: number, commit?: true): void
  toggleInvert(): void
  cropImage(crop: PercentCrop, flipX: boolean, flipY: boolean, circularCrop?: boolean): void
}

const FILTER_BY_KEY = {
  [ImageFilters.PIXELATE]: (args: any) => new fabric.filters.Pixelate({ blocksize: 10, ...args }),
  [ImageFilters.BLACK_WHITE]: () => new fabric.filters.ColorMatrix({
    matrix: [
      1, 1, 1, 0, -1,
      1, 1, 1, 0, -1,
      1, 1, 1, 0, -1,
      0, 0, 0, 1, 0,
    ]
  }),
  [ImageFilters.GRAYSCALE]: (args: any) => new fabric.filters.Grayscale(args),
  [ImageFilters.INVERT]: (args: any) => new fabric.filters.Invert(args),
  [ImageFilters.PAINTING]: (args: any) => new PaintingFilter(args),
  [ImageFilters.HALFTONE]: (args: any) => new HalftoneFilter(args),
  [ImageFilters.PENCIL_SKETCH]: (args: any) => new PencilSketchFilter(args)
}

//需要白底的滤镜
const FILTERS_NEED_WHITE_BG: Set<ImageFilters> = new Set([
  ImageFilters.PAINTING,
  ImageFilters.PENCIL_SKETCH
])

function _limitValueRange(val: number, min = -1, max = 1) {
  return Math.max(min, Math.min(val, max))
}

function ensureImageErasable(image: fabric.FabricImage) {
  if (image && image.erasable !== true) {
    image.set({ erasable: true })
  }
  return image
}

const ALLOWED_FILTERS = [
  fabric.filters.Grayscale,
  fabric.filters.Brightness,
  fabric.filters.Contrast,
  fabric.filters.Invert,
  fabric.filters.Resize,
]

export const BitmapPlugin = createPlugin<BitmapUtilities>(
  ({ canvas }) => {
    const getActiveImage = () => {
      const objs = canvas.getActiveObjects()
      if (objs.length !== 1) return null

      const [o] = objs
      if (o instanceof fabric.FabricImage) {
        return o as fabric.FabricImage
      }

      return null
    }

    return {
      utilities: {
        clearFilter() {
          const image = getActiveImage()
          if (!image) return

          if (image.filters) {
            image.filters = image.filters.filter(filter => ALLOWED_FILTERS.some(o => o.type === filter.type))
          }

          //如果是需要加白底的滤镜，需要保存原始图像方便还原
          if (image.__originalElement && image._element !== image.__originalElement ) {
            image.setElement(image.__originalElement)
          } else {
            image.__originalElement = null
          }

          image.applyFilters()
          image.__filter_type__ = null

          ensureImageErasable(image)
          canvas.requestRenderAll()
          canvas.fire('selection:updated')
        },
        async applyFilter(type: ImageFilters) {
          const target = getActiveImage()
          if (!target) return

          if (type === ImageFilters.ORIGINAL) return this.clearFilter()

          const Filter = FILTER_BY_KEY[type]
          if (!Filter) return

          if (!target.__originalElement && FILTERS_NEED_WHITE_BG.has(type)) {
            target.__originalElement = target._element
          }

          const { __filter_type__ } = target

          if (__filter_type__ === type) {
            return this.clearFilter()
          }

          if (__filter_type__ !== null) {
            this.clearFilter()
          }

          const { width, height } = target._originalElement
          const filter = Filter({ width, height })

          //判断是否需要添加白底
          if (FILTERS_NEED_WHITE_BG.has(type)) {
            await applyFilterWithWhiteBgIfNeeded(target, filter)
          } else {
            target.filters.push(filter)
            target.applyFilters()
          }

          target.__filter_type__ = type

          ensureImageErasable(target)
          canvas.requestRenderAll()
          canvas.fire('selection:updated')
          canvas.fire('object:modified', { target })
        },
        async setCurrentImageSource(src) {
          const target = getActiveImage()
          if (!target) return

          await target.setSrc(src)

          ensureImageErasable(target)
          canvas.requestRenderAll()
          canvas.fire('object:modified', { target })
        },
        setBrightness(val: number, commit) {
          const target = getActiveImage()
          if (!target) return

          val = _limitValueRange(val)

          const filter = target
            .filters
            .find(o => o.type === fabric.filters.Brightness.type) as fabric.filters.Brightness | null

          if (filter) {
            filter.brightness = val
          } else {
            target.filters.push(new fabric.filters.Brightness({ brightness: val }))
          }

          target.applyFilters()
          ensureImageErasable(target)
          canvas.requestRenderAll()

          if (commit) {
            canvas.fire('selection:updated')
            canvas.fire('object:modified', { target })
          }
        },
        setContrast(val: number, commit) {
          const target = getActiveImage()
          if (!target) return

          val = _limitValueRange(val)

          const filter = target
            .filters
            .find(o => o.type === fabric.filters.Contrast.type) as fabric.filters.Contrast | null

          if (filter) {
            filter.contrast = val
          } else {
            target.filters.push(new fabric.filters.Contrast({
              contrast: val
            }))
          }

          target.applyFilters()
          ensureImageErasable(target)
          canvas.requestRenderAll()

          if (commit) {
            canvas.fire('selection:updated')
            canvas.fire('object:modified', { target })
          }
        },
        toggleInvert() {
          const image = getActiveImage()
          if (!image) return

          const invertFilterIndex = image
            .filters
            .findIndex(o => o.type === fabric.filters.Invert.type)

          if (invertFilterIndex !== -1) {
            image.filters.splice(invertFilterIndex, 1)
          } else {
            image.filters.push(new fabric.filters.Invert({ invert: true }))
          }

          image.applyFilters()
          ensureImageErasable(image)
          canvas.requestRenderAll()
          canvas.fire('object:modified', { target: image })
          canvas.fire('selection:updated')
        },
        async cropImage(crop, flipX, flipY, circularCrop) {
          const image = getActiveImage()
          if (!image) return

          let { x: xPercent, y: yPercent } = crop
          const { height: heightPercent, width: widthPercent } = crop

          if (flipX) xPercent = 100 - xPercent - widthPercent
          if (flipY) yPercent = 100 - yPercent - heightPercent

          await withAtomicOperation(image, async () => {
            const widthAfterCrop = image._originalElement.width * (widthPercent / 100)
            const heightAfterCrop = image._originalElement.height * (heightPercent / 100)
            const cropX = image._originalElement.width * xPercent / 100
            const cropY = image._originalElement.height * yPercent / 100

            // 创建临时图像进行裁剪
            const tempImage = await fabric.FabricImage.fromObject({
              src: image.getSrc(),
              filters: [],
              flipX,
              flipY,
              cropX,
              cropY,
              width: widthAfterCrop,
              height: heightAfterCrop,
              erasable: true
            })

            // 如果是圆形裁剪，应用圆形clipPath
            if (circularCrop) {
              // 确保裁剪为正方形，以便圆形完整显示
              const size = Math.min(widthAfterCrop, heightAfterCrop)

              // 创建一个临时的HTML Canvas元素来绘制圆形
              const htmlCanvas = document.createElement('canvas')
              htmlCanvas.width = size
              htmlCanvas.height = size
              const ctx = htmlCanvas.getContext('2d')
              if (!ctx) return

              // 清除画布
              ctx.clearRect(0, 0, size, size)

              // 绘制圆形裁剪路径
              ctx.beginPath()
              ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2, true)
              ctx.closePath()
              ctx.clip()

              // 创建临时图像对象用于绘制
              const imgElement = new Image()

              // 将裁剪和翻转等操作应用到临时画布
              await new Promise<void>(resolve => {
                imgElement.onload = () => {
                  // 计算源图像应该绘制的位置和大小，使其居中
                  const sourceWidth = tempImage.width
                  const sourceHeight = tempImage.height
                  const aspectRatio = sourceWidth / sourceHeight

                  let drawWidth, drawHeight, offsetX, offsetY

                  if (aspectRatio > 1) {
                    // 宽图像
                    drawHeight = size
                    drawWidth = drawHeight * aspectRatio
                    offsetX = (size - drawWidth) / 2
                    offsetY = 0
                  } else {
                    // 高图像
                    drawWidth = size
                    drawHeight = drawWidth / aspectRatio
                    offsetX = 0
                    offsetY = (size - drawHeight) / 2
                  }

                  // 如果需要翻转图像
                  if (flipX || flipY) {
                    ctx.save()
                    ctx.translate(size / 2, size / 2)
                    ctx.scale(flipX ? -1 : 1, flipY ? -1 : 1)
                    ctx.translate(-size / 2, -size / 2)
                  }

                  // 绘制图像到画布上
                  ctx.drawImage(
                    imgElement,
                    0, 0, imgElement.width, imgElement.height,
                    offsetX, offsetY, drawWidth, drawHeight
                  )

                  if (flipX || flipY) {
                    ctx.restore()
                  }

                  resolve()
                }

                // 设置图像源为tempImage的数据URL
                imgElement.src = tempImage.toDataURL({
                  format: 'png',
                  multiplier: 1
                })
              })

              // 获取圆形裁剪后的图像数据
              const url = htmlCanvas.toDataURL('image/png')

              // 从画布中移除原图像
              canvas.remove(image)

              // 设置原图像的新属性
              image.set({
                flipX: false,
                flipY: false,
                cropX: 0,
                cropY: 0,
                erasable: true
              })

              // 设置原图像的新源
              await image.setSrc(url)

              // 添加回画布
              canvas.add(image)
              ensureImageErasable(image)

              // 清理临时资源
              tempImage.dispose()
            } else {
              // 非圆形裁剪，使用原有逻辑
              const url = tempImage.toDataURL({
                width: widthAfterCrop,
                height: heightAfterCrop
              })

              canvas.remove(image)
              image.set({
                flipX: false,
                flipY: false,
                cropX: 0,
                cropY: 0,
                erasable: true
              })

              await image.setSrc(url)
              canvas.add(image)
              ensureImageErasable(image)

              tempImage.dispose()
            }
          })

          canvas.fire('object:modified', { target: image })
          canvas.setActiveObject(image)
        }
      }
    }
  }
)
