import { createPlugin, FabricCoreContext, FabricPlugin } from '../types/base'
import * as fabric from 'fabric'
import { moveViewportTo } from '../utils'
import {
  WORKSPACE_BORDER_WIDTH,
  WORKSPACE_SIZE_PIXELS,
  ZOOM_MIN_SCALE
} from '../constants'
import { WorkMode } from '@/libs/shared/types'
import { convertToSystemObject } from '@/libs/fabricjs-react/utils/object'

export interface WorkspaceUtilities {
  resetZoom(): void
  zoomToMinimum(): void
  getWorkspaceCenter(): fabric.Point
}

class Workspace implements FabricPlugin<WorkspaceUtilities> {
  private _group: fabric.Group

  constructor(private context: FabricCoreContext) {
    context.state.workspaceHeight = WORKSPACE_SIZE_PIXELS
    context.state.workspaceWidth = WORKSPACE_SIZE_PIXELS
  }

  public onInit() {
    const { canvas, getUtilities, width, state } = this.context

    this.#initBackground()
    this.#drawBorders()
    this.#zoomToMinimum()

    const handler = () => {
      this.#pinWorkspace()
      return true
    }

    canvas.on('object:added', handler)
    canvas.on('object:modified', handler)

    canvas.on('history:undo', ({ objects }) => {
      if (objects === 0) {
        this.#drawBorders()
      }
    })

    canvas.on('workspace:changed', ({ workMode, diameter }) => {
      state.workspaceWidth = workMode === WorkMode.ROTATION
        ? Math.PI * getUtilities().unitToPixels(diameter)
        : width
      this.#drawBorders()
    })

    canvas.on('workspace:length:changed', ({ workMode, length }) => {
      state.workspaceWidth = workMode === WorkMode.TABLET
        ? getUtilities().unitToPixels(length)
        : width

      this.#drawBorders()
    })
  }

  public get utilities() {
    return {
      resetZoom: this.#resetZoom.bind(this),
      zoomToMinimum: this.#zoomToMinimum.bind(this),
      getWorkspaceCenter: this.#getWorkspaceCenter.bind(this)
    }
  }

  #resetZoom() {
    return this.#setScale(1.0)
  }

  #getWorkspaceCenter() {
    const { workspaceHeight, workspaceWidth } = this.context.state
    return new fabric.Point(workspaceWidth! / 2, workspaceHeight! / 2)
  }

  #setScale(scale: number) {
    const { workspaceEl, canvas, state } = this.context

    const width = workspaceEl.offsetWidth
    const height = workspaceEl.offsetHeight
    canvas.setDimensions({ width, height })

    const center = canvas.getCenterPoint()
    canvas.setViewportTransform(fabric.iMatrix.concat() as any)
    canvas.zoomToPoint(center, scale)
    state.zoom = scale

    moveViewportTo(canvas, Infinity, Infinity)
  }

  #zoomToMinimum() {
    return this.#setScale(ZOOM_MIN_SCALE)
  }

  #initBackground() {
    const { canvas, workspaceEl } = this.context

    canvas.setDimensions({
      width: workspaceEl.offsetWidth,
      height: workspaceEl.offsetHeight
    })
  }

  #drawBorders() {
    const { canvas, state } = this.context

    if (this._group) {
      canvas.remove(this._group)
      this._group._objects.forEach(o => o.dispose())
      this._group.dispose()
      canvas.requestRenderAll()
    }

    const width = state.workspaceWidth || WORKSPACE_SIZE_PIXELS
    const height = state.workspaceHeight || WORKSPACE_SIZE_PIXELS

    const stroke = WORKSPACE_BORDER_WIDTH

    const l = -stroke * 3 / 2
    const r = width + stroke / 2
    const t = -stroke
    const b = height + stroke / 2

    this._group = convertToSystemObject(
      new fabric.Group([
        new fabric.Line([l, t, r, t]),  // Top Border
        new fabric.Line([l, t, l, b]),  // Left Border
        new fabric.Line([l, b, r, b]),  // Bottom Border
        new fabric.Line([r, t, r, b]),  // Right Border
      ].map(border => convertToSystemObject(
        border
          .set('stroke', '#ee0000')
          .set('strokeDashArray', [16, 16])
          .set('strokeWidth', stroke)
      )))
    )
    canvas.add(this._group)
  }

  #pinWorkspace() {
    this.context.canvas.bringObjectToFront(this._group)
    this.context.canvas.requestRenderAll()
  }
}

export const WorkspacePlugin = createPlugin<WorkspaceUtilities>(
  context => new Workspace(context)
)
