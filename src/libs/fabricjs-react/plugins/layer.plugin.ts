import { createPlugin } from '../types/base'
import { FabricObject } from 'fabric'
import { getTypeOfFabricObject, withAtomicOperation } from '../utils'
import { CANVAS_OBJECTS_SIZE_LIMIT, ObjectType, PROPERTIES_TO_INCLUDE_WHEN_EXPORT } from '../constants'
import { v4 } from 'uuid'
import * as fabric from 'fabric'
import toast from 'react-hot-toast'
import { extractObjects, isObjectLocked } from '@/libs/fabricjs-react/utils/object'

export type LayerData = {
  id: string
  type: ObjectType
  image: string

  locked?: boolean
  visible?: boolean
  active?: boolean

  _object: FabricObject
}

export interface LayerUtilities {
  canGroup(): boolean
  canUngroup(): boolean
  canRemove(): boolean
  canRasterization(): boolean

  groupActiveObjects(): void
  ungroupActiveObjects(): void
  removeActiveObjects(): void
  duplicateActiveObjects(): Promise<void>
  centerActiveObjects(): void

  getLayers(): LayerData[]
  moveLayer(from: number, to: number): boolean
}

export const LayerPlugin = createPlugin<LayerUtilities>(
  ({ t, canvas, getUtilities }) => {
    const _rawRemoveObjects = (...objs: fabric.FabricObject[]) => {
      objs.forEach(object => {
        const index = canvas._objects.indexOf(object)
        // only call onObjectRemoved if an object was actually removed
        if (index !== -1) {
          canvas._objects.splice(index, 1)
          object.dispose()
        }
      })
    }

    return ({
      utilities: {
        canGroup() {
          const objs = canvas.getActiveObjects()
          return objs.length >= 2 && objs.every(o => !isObjectLocked(o))
        },
        canUngroup() {
          const objs = canvas.getActiveObjects()

          return objs.length === 1
            && objs[0] instanceof fabric.Group
            && !(objs[0] instanceof fabric.ActiveSelection)
            && !isObjectLocked(objs[0])
        },
        canRemove() {
          return canvas.getActiveObjects().every(o => !isObjectLocked(o))
        },
        canRasterization() {
          const objs = canvas.getActiveObjects()
          return objs.every(o => !isObjectLocked(o))
        },

        groupActiveObjects() {
          if (!this.canGroup()) return
          const objs = canvas.getActiveObjects()
          const group = new fabric.Group(objs) as fabric.Group

          withAtomicOperation([group, ...objs], () => {
            _rawRemoveObjects(...objs)

            canvas.add(group)
          })

          canvas.setActiveObject(group)
          canvas.fire('object:modified')
        },
        ungroupActiveObjects() {
          if (!this.canUngroup()) return

          const objs = canvas.getActiveObjects()
          const group = objs[0] as fabric.Group

          const removed = group.removeAll()
          const prevRenderFlag = canvas.renderOnAddRemove

          canvas.renderOnAddRemove = false
          withAtomicOperation([group, ...removed], () => {
            canvas._objects.push(...removed)
            removed.forEach(o => {
              o._set('canvas', canvas)
              o.setCoords()
            })

            canvas.remove(group)
            canvas.discardActiveObject()
          })
          canvas.renderOnAddRemove = prevRenderFlag

          canvas.fire('object:modified')
        },
        removeActiveObjects() {
          if (!this.canRemove()) return

          const objs = canvas.getActiveObjects()

          canvas.discardActiveObject()
          _rawRemoveObjects(...objs)

          canvas.requestRenderAll()
          canvas.fire('object:removed')
        },
        async duplicateActiveObjects() {
          if (canvas._activeObject && isObjectLocked(canvas._activeObject)) return
          const objs = canvas.getActiveObjects()

          const totalObjects = extractObjects(...canvas._objects).length + extractObjects(...objs).length
          if (totalObjects > CANVAS_OBJECTS_SIZE_LIMIT) {
            toast.error(t('hints.reach-limit-of-layers'))
            return
          }

          await withAtomicOperation(objs, async () => {
            const clones = await Promise.all(
              objs.map(o => o.clone(PROPERTIES_TO_INCLUDE_WHEN_EXPORT))
            )

            for (let i = 0; i < clones.length; i++) {
              const item = clones[i]
              item.setXY(objs[i].getXY().add(new fabric.Point(32, 32)))
              canvas._objects.push(item)
              item.set('canvas', canvas)
              item.setCoords()
            }

            if (clones.length > 1) {
              canvas.setActiveObject(new fabric.ActiveSelection(clones))
            } else {
              canvas.setActiveObject(clones[0])
            }
          })

          const promise = new Promise<void>(resolve => {
            canvas.once('after:render', () => resolve())
          })
          canvas.requestRenderAll()
          canvas.fire('object:added')
          return promise
        },
        centerActiveObjects() {
          const target = canvas.getActiveObject()
          if (!target) return getUtilities().zoomToMinimum()

          const workspaceCenter = getUtilities().getWorkspaceCenter()

          if (target instanceof fabric.ActiveSelection) {
            const selection = canvas.getActiveObject()
            if (!selection) return

            const objects = selection instanceof fabric.ActiveSelection
              ? selection.getObjects()
              : [selection]

            canvas.discardActiveObject()
            if (selection instanceof fabric.ActiveSelection) {
              canvas.remove(selection)
            }

            // 将每个对象居中（自己的中心点对齐画布中心）
            objects.forEach(obj => {
              const centerPoint = obj.getCenterPoint()
              obj.left += workspaceCenter.x - centerPoint.x
              obj.top += workspaceCenter.y - centerPoint.y
              obj.setCoords()
            })

            // 重新选中这些对象
            const newSelection = new fabric.ActiveSelection(objects, { canvas })
            canvas.setActiveObject(newSelection)
            newSelection.setCoords()

            canvas.requestRenderAll()
            canvas.fire('object:modified', { target: newSelection })
          }

          else {
            canvas.discardActiveObject()
            target?.setXY(workspaceCenter, 'center', 'center')
            canvas.setActiveObject(target)
          }

          canvas.requestRenderAll()
          canvas.fire('object:modified', { target })
        },

        getLayers() {
          const objects = canvas
            .getObjects()
            .filter(o => !o.__is_system_object__)

          const activeObjects = canvas.getActiveObjects()

          return objects
            .map(object => {
              const visible = object.visible
              const type = getTypeOfFabricObject(object)
              if (!visible) {
                object.visible = true
                canvas._renderObjects(canvas.getContext(), [object])
              }

              const image = object.toDataURL()
              if (!visible) {
                object.visible = false
                canvas.requestRenderAll()
              }

              return {
                id: v4(),
                type,
                image,
                _object: object as any,
                locked: isObjectLocked(object),
                visible: object.visible,
                active: activeObjects.includes(object)
              } as LayerData
            })
            .filter(o => o.image.startsWith('data:image')) // 过滤不可见图层
        },
        moveLayer(from: number, to: number) {
          const layers = this.getLayers()

          const total = layers.length

          if (from > total || to > total || from < 0 || to < 0 || from === to) {
            return false
          }

          canvas.moveObjectTo(layers[from]._object, to)
          canvas.fire('object:modified')
          return true
        },
      },
    })
  }
)
