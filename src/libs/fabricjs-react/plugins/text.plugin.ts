import { createPlugin } from '../types/base'
import * as fabric from 'fabric'
import {
  MM_PER_INCH,
  POINT_PER_INCH,
  TEXT_FILL_STYLES,
  TEXT_STROKE_STYLES,
  WORKSPACE_DEFAULT_SIZE_MM,
  WORKSPACE_SIZE_PIXELS,
  LOCAL_STATIC_FONTS, TEXT_FONT_STYLES, TEXT_FONT_WEIGHTS
} from '@/libs/fabricjs-react/constants'
import opentype from 'opentype.js'
import { withAtomicOperation } from '@/libs/fabricjs-react/utils'
import { loadFontFormBase64 } from '@/utils/font'
import localforage from 'localforage'
import { VectorText } from '@/libs/fabricjs-react/custom_objects'
import Font_NotoSansSCRegular from '@/assets/fonts/NotoSansSC-Regular.ttf'

export type FontStyleProperties = 'italic' | 'bold' | 'underline' | 'linethrough' | 'fill' | 'stroke'

export interface TextUtilities {
  setTextAlign(align: string): void
  setTextContent(val: string, commit?: boolean): void
  setFontSize(size: number, commit?: boolean): void
  setFontFamily(value: string, commit?: boolean): void
  setFontStyles(props: FontStyleProperties[]): void

  pixelToPoint(pixel: number): number
  pointToPixel(point: number): number

  __dev__replaceTextToVector(): void
}

let fallbackFont: opentype.Font | null = null

async function getFallbackFont(): Promise<opentype.Font> {
  if (!fallbackFont) {
    fallbackFont = await fetch(Font_NotoSansSCRegular)
      .then(res => res.arrayBuffer())
      .then(buf => opentype.parse(buf))
  }
  return fallbackFont as opentype.Font
}

async function _loadFontByFamily(fontFamily: string): Promise<opentype.Font> {
  try {
    const isLocalFont = LOCAL_STATIC_FONTS.some(font => font.fontFamily === fontFamily)

    if (isLocalFont) {
      const localFont = LOCAL_STATIC_FONTS.find(font => font.fontFamily === fontFamily)
      if (localFont?.localUri) {
        const buffer = await fetch(localFont.localUri).then(r => r.arrayBuffer())
        return opentype.parse(buffer)
      }
    }

    const cachedFontData = await localforage.getItem<ArrayBuffer>(fontFamily)
    if (cachedFontData) {
      return opentype.parse(cachedFontData)
    }

    const targetFont = window.injected?.fonts?.[fontFamily]
    if (targetFont) {
      return loadFontFormBase64(targetFont)
    }

    return await getFallbackFont()
  } catch (e) {
    return await getFallbackFont()
  }
}

export const TextPlugin = createPlugin<TextUtilities>(
  ({ canvas }) => {
    function getActiveTarget(): VectorText | null {
      const objs = canvas.getActiveObjects()
      if (objs.length !== 1) return null

      const [obj] = objs

      if (obj instanceof VectorText) {
        return obj as VectorText
      }

      return null
    }

    return {
      utilities: {
        setTextAlign(align: 'center' | 'start' | 'end') {
          const target = getActiveTarget()
          if (!target) return

          target.set({ textAlign: align })
          canvas.fire('object:modified', { target })
          canvas.requestRenderAll()
        },
        setTextContent(val: string, commit = false) {
          const target = getActiveTarget()
          if (!target) return

          target.set({ text: val.split('\n').filter(Boolean).map(o => o.replace(/^ */, '')).join('\n') })

          if (commit) {
            canvas.fire('object:modified', { target })
          }
        },
        setFontSize(size: number, commit = false) {
          const target = getActiveTarget() as fabric.FabricText
          if (!target) return

          const fontSize = this.pointToPixel(size)
          target.set({
            fontSize,
            preview: !commit
          })
          canvas.requestRenderAll()
          canvas.fire('selection:updated')

          if (commit) {
            canvas.fire('object:modified', { target })
          }
        },
        setFontFamily(fontFamily, commit = false) {
          const target = getActiveTarget()
          if (!target) return

          target.set({ fontFamily })
          canvas.requestRenderAll()
          canvas.fire('selection:updated')

          if (commit) {
            canvas.fire('object:modified', { target })
          }
        },
        setFontStyles(props) {
          const target = getActiveTarget()
          if (!target) return

          const fontStyle = props.includes('italic')
            ? TEXT_FONT_STYLES.italic
            : TEXT_FONT_STYLES.normal

          const fontWeight = props.includes('bold')
            ? TEXT_FONT_WEIGHTS.bold
            : TEXT_FONT_WEIGHTS.normal

          const fillProps = props.includes('fill')
            ? TEXT_FILL_STYLES
            : {
              ...TEXT_STROKE_STYLES,
              strokeWidth: this.pixelToPoint(target.fontSize) / 16
            }

          target.set({
            underline: props.some(o => o === 'underline'),
            linethrough: props.some(o => o === 'linethrough'),
            fontStyle,
            fontWeight,
            ...fillProps
          })

          canvas.requestRenderAll()
          canvas.fire('selection:updated')
          canvas.fire('object:modified', { target })
        },
        pixelToPoint(pixel: number): number {
          return Math.round(pixel / WORKSPACE_SIZE_PIXELS * WORKSPACE_DEFAULT_SIZE_MM / MM_PER_INCH * POINT_PER_INCH)
        },
        pointToPixel(point: number): number {
          return point / POINT_PER_INCH * MM_PER_INCH / WORKSPACE_DEFAULT_SIZE_MM * WORKSPACE_SIZE_PIXELS
        },

        async __dev__replaceTextToVector() {
          const target = getActiveTarget()
          if (!target) return

          const vector = target._cachedVector
          if (vector) {
            withAtomicOperation([target, vector], () => {
              canvas.remove(target)
              canvas.add(vector)
            })
          }
          // const path = await this.convertTextToPath(target)
          // withAtomicOperation([target, path], () => {
          //   canvas.remove(target)
          //   canvas.add(path)
          // })
        }
      }
    }
  }
)
