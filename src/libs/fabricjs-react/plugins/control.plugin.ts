import { createPlugin } from '../types/base'
import * as fabric from 'fabric'
import DeleteIcon from '@/assets/icons/delete.svg'
import RotateIcon from '@/assets/icons/rotate.svg'
import LockIcon from '@/assets/icons/lock.svg'
import UnlockIcon from '@/assets/icons/unlock.svg'
import ZoomIcon from '@/assets/icons/zoom.svg'
import { VectorText } from '../custom_objects'
import { isObjectLocked } from '@/libs/fabricjs-react/utils/object'

function renderIcon(
  ctx: CanvasRenderingContext2D,
  left: number,
  top: number,
  imgEl: HTMLImageElement,
  angle: number = 0,
  wSize = 28,
  hSize = 28,
) {
  ctx.save()
  ctx.translate(left, top)
  ctx.rotate(fabric.util.degreesToRadians(angle))
  ctx.drawImage(imgEl, -wSize / 2, -hSize / 2, wSize, hSize)
  ctx.restore()
}

function createImgElementWithSrc(src: any) {
  const el = document.createElement('img')
  el.src = src
  return el
}

export const ControlPlugin = createPlugin(
  ({ getUtilities }) => {
    return {
      utilities: {},
      onInit() {
        // eslint-disable-next-line unused-imports/no-unused-vars
        const { mtr, mr, mt, ...defaultControls } = fabric.controlsUtils.createObjectDefaultControls()

        const deleteIconEl = createImgElementWithSrc(DeleteIcon)
        const rotateIconEl = createImgElementWithSrc(RotateIcon)
        const lockIconEl = createImgElementWithSrc(LockIcon)
        const unlockIconEl = createImgElementWithSrc(UnlockIcon)
        const zoomIconEl = createImgElementWithSrc(ZoomIcon)

        /**
         * 删除
         */
        const bl = new fabric.Control({
          x: -0.5,
          offsetX: -12,
          y: 0.5,
          offsetY: 12,
          render(ctx, left, top, styleOverride, object) {
            renderIcon(ctx, left, top, deleteIconEl, object.angle)
          },
          mouseDownHandler(eventData, { target }) {
            return getUtilities().removeActiveObjects()
          },
        })

        /**
         * 锁定/解锁
         */
        const tl = new fabric.Control({
          x: -0.5,
          offsetX: -12,
          y: -0.5,
          offsetY: -12,
          render(ctx, left, top, styleOverride, object) {
            renderIcon(ctx, left, top, isObjectLocked(object as any) ? lockIconEl : unlockIconEl, object.angle)
          },
          mouseDownHandler(e, { target }) {
            return getUtilities().toggleLocking(target)
          }
        })

        /**
         * 旋转
         */
        const tr = new fabric.Control({
          x: 0.5,
          offsetX: 12,
          y: -0.5,
          offsetY: -12,
          render(ctx, left, top, styleOverride, object) {
            renderIcon(ctx, left, top, rotateIconEl, object.angle)
          },
          actionHandler: (e, t, x, y) => {
            return fabric.controlsUtils.rotationWithSnapping(e, { ...t, originX: 'center', originY: 'center' }, x, y)
          },
        })

        /**
         * 缩放
         */
        const br = new fabric.Control({
          x: 0.5,
          y: 0.5,
          offsetX: 12,
          offsetY: 12,
          render(ctx, left, top, styleOverride, object) {
            renderIcon(ctx, left, top, zoomIconEl, object.angle)
          },
          actionHandler: fabric.controlsUtils.scalingEqually,
        })

        const createObjectControls = () => ({
          controls: {
            br, bl, tl, tr,
          }
        })

        const createTextControls = () => ({
          controls: {
            br,
            bl, tl, tr,
            // br: new fabric.Control({
            //   x: 0.5,
            //   y: 0.5,
            //   offsetX: 12,
            //   offsetY: 12,
            //   render(ctx, left, top, styleOverride, object) {
            //     renderIcon(ctx, left, top, zoomIconEl, object.angle)
            //   },
            //   actionHandler: fabric.controlsUtils.wrapWithFixedAnchor((eventData, transform, x, y,) => {
            //     const target = transform.target
            //     // scaleProportionally = scaleIsProportional(eventData, target),
            //     let newPoint, scaleX, scaleY, dim, signX, signY
            //
            //     newPoint = fabric.controlsUtils.getLocalPoint(
            //       transform,
            //       'right',
            //       'bottom',
            //       x,
            //       y,
            //     )
            //     dim = target._getTransformedDimensions()
            //
            //     const distance = Math.abs(newPoint.x) + Math.abs(newPoint.y),
            //       { original } = transform,
            //       originalDistance =
            //         Math.abs((dim.x * original.scaleX) / target.scaleX) +
            //         Math.abs((dim.y * original.scaleY) / target.scaleY),
            //       scale = distance / originalDistance
            //
            //
            //     console.log({ distance })
            //     return false
            //   })
            // })
          }
        })

        fabric.InteractiveFabricObject.createControls = createObjectControls
        VectorText.createControls = createTextControls
      }
    }
  }
)
