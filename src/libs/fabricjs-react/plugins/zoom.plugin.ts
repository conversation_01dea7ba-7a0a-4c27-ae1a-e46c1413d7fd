import { createPlugin, FabricCoreContext } from '../types/base'
import * as fabric from 'fabric'
import { throttle } from 'lodash'
import { ZOOM_MAX_SCALE, ZOOM_MIN_SCALE } from '@/libs/fabricjs-react/constants'
import { moveViewportTo } from '@/libs/fabricjs-react/utils'

const limitBetween = (min: number, val: number, max: number) => Math.max(min, Math.min(val, max))

class PinchGestureHandler {

  private _startTouches: [Touch, Touch] | null = null
  private _originalZoom: number

  constructor (private context: FabricCoreContext) {
    const { workspaceEl, canvas, state } = this.context

    workspaceEl.addEventListener(
      'touchstart',
      ({ touches }: TouchEvent) => {
        if (touches.length !== 2) {
          this._startTouches = null
          return
        }

        canvas.selection = false
        this._startTouches = [touches.item(0)!, touches.item(1)!]
        this._originalZoom = canvas.getZoom()
      }
    )

    workspaceEl.addEventListener(
      'touchmove',
      throttle(({ touches }: TouchEvent) => {
        if (!this._startTouches) return

        if (touches.length !== 2) {
          return
        }

        canvas.discardActiveObject()

        const currentTouches = [touches.item(0)!, touches.item(1)!]
        const currentCenter = this._getCenterPoint(currentTouches[0], currentTouches[1])
        const currentDistance = this._getDistance(currentTouches[0], currentTouches[1])
        const initialDistance = this._getDistance(...this._startTouches)
        const diff = currentDistance - initialDistance

        let targetZoom = limitBetween(ZOOM_MIN_SCALE, this._originalZoom + 3e-3 * diff, ZOOM_MAX_SCALE)
        targetZoom = Number(targetZoom.toFixed(2))

        canvas.zoomToPoint(new fabric.Point(currentCenter.x, currentCenter.y), targetZoom)
        // 防止缩小时超出工作区
        moveViewportTo(canvas, x => x, y => y)
        canvas.requestRenderAll()

        state.zoom = targetZoom
      }, 5, { trailing: false })
    )

    workspaceEl.addEventListener(
      'touchend',
      () => {
        // canvas.selection = true
      }
    )
  }

  _getDistance(p1: Touch, p2: Touch) {
    const { clientX: x1, clientY: y1 } = p1
    const { clientX: x2, clientY: y2 } = p2

    return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2))
  }

  _getCenterPoint(p1: Touch, p2: Touch) {
    const { clientX: x1, clientY: y1 } = p1
    const { clientX: x2, clientY: y2 } = p2

    return {
      x: (x1 + x2) / 2,
      y: (y1 + y2) / 2,
    }
  }
}

class WheelEventHandler {
  static bind({ canvas, state }: FabricCoreContext) {
    canvas.on('mouse:wheel', function (this: fabric.Canvas, opt) {
      const delta = opt.e.deltaY

      const zoom = limitBetween(ZOOM_MIN_SCALE, this.getZoom() * (0.999 ** delta), ZOOM_MAX_SCALE)

      if (zoom > this.getZoom()) {
        this.zoomToPoint(this.getCenterPoint(), zoom)
      } else {
        this.setZoom(zoom)
      }
      canvas.requestRenderAll()
      state.zoom = zoom

      opt.e.preventDefault()
      opt.e.stopPropagation()
    })
  }
}

export const ZoomPlugin = createPlugin(
  context => ({
    utilities: {},
    onInit() {
      WheelEventHandler.bind(context)
      new PinchGestureHandler(context)
    }
  })
)
