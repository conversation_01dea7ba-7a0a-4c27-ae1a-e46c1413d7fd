import { createPlugin } from '../types/base'
import * as fabric from 'fabric'
import {
  ObjectType,
  VECTOR_FILL_MODES,
  WORKSPACE_DEFAULT_SIZE_MM,
  WORKSPACE_SIZE_PIXELS
} from '../constants'
import { ExportedCanvasData, ExportedLayer, ExportedLayerType } from '@/libs/shared/types'
import { getTypeOfFabricObject, getValidObjects, shouldExportAsBitmap, wrapWithSvgRoot } from '../utils'
import { VectorText } from '@/libs/fabricjs-react/custom_objects'
import { safeParseJson } from '@/utils'

export interface ExportUtilities {
  exportLayers(forceBitmap?: boolean, isPreview?: boolean): Promise<ExportedLayer[]>

  exportCanvasAsJson(silent?: boolean): Promise<ExportedCanvasData>

  prepareCanvasCache(data: any): void

  restoreCanvasFromCache(): void

  __dev__exportSingleSvg(svg: string): void
  __dev__exportActiveObject(): void
  __dev__previewAsSvg(): Promise<void>
  __dev__exportBase64Image(src: string): Promise<void>
}

async function temporarilyIgnoreCanvasEvents(canvas: fabric.Canvas, fn: () => void | Promise<void>) {
  const originalFire = canvas.fire
  canvas.fire = () => {} // 禁用所有事件触发

  try {
    // 执行传入的函数，确保是一个 Promise 对象
    const result = fn()

    // 如果传入的函数返回的是一个 Promise，等待其完成
    if (result instanceof Promise) {
      await result
    }

    // 强制更新画布，确保状态一致
    canvas.requestRenderAll()  // 更新所有对象的渲染状态

  } finally {
    // 最后恢复事件触发
    canvas.fire = originalFire
  }
}

export const CANVAS_CACHE_STORAGE_KEY = 'REACT_FABRIC_EDITOR_CANVAS_DATA'

async function exportCanvasToImage(canvas: fabric.Canvas, x: number, y: number, width: number, height: number) {
  const tempCanvasEl = document.createElement('canvas')
  tempCanvasEl.width = width
  tempCanvasEl.height = height

  canvas.discardActiveObject()
  canvas.renderAll()

  const staticCanvas = new fabric.StaticCanvas(tempCanvasEl, {
    width,
    height,
    backgroundColor: canvas.backgroundColor
  })

  const objectsToRender = canvas
    .getObjects()
    .filter(obj => {
      const objBound = obj.getBoundingRect()
      return (
        objBound.left + objBound.width >= x
          && objBound.top + objBound.height >= y
          && objBound.left <= x + width
          && objBound.top <= y + height
      )
    })

  for (const obj of objectsToRender) {
    if (obj instanceof VectorText) {
      await obj.toSVGAsync()
        .then(svg => fabric.loadSVGFromString(wrapWithSvgRoot(svg, width, height)))
        .then(({ objects }) => {
          for (const loaded of objects) {
            if (loaded) {
              staticCanvas._objects.push(loaded)
              loaded.set('canvas', staticCanvas)
            }
          }
        })
    } else {
      const cloned = await obj.clone()
      staticCanvas._objects.push(cloned)
      cloned.set('canvas', staticCanvas)
    }
  }

  const promise = new Promise<void>(resolve => {
    staticCanvas.once('after:render', () => resolve())
  })
  staticCanvas.renderAll()
  await promise

  const result = staticCanvas.toDataURL({
    format: 'png',
    quality: 1,
    multiplier: 1,
    width: width,
    height: height,
  })

  await staticCanvas.dispose()
  tempCanvasEl.remove()

  return result
}

class LayerExportHandler {

  constructor(
    private readonly forceBitmap: boolean,
    private readonly canvasWidth: number,
    private readonly canvasHeight: number
  ) {
  }

  public async export(object: fabric.FabricObject): Promise<ExportedLayer[]> {
    if (shouldExportAsBitmap(object) || this.forceBitmap) {
      return [this.exportBitmap(object)]
    }

    const objectType = getTypeOfFabricObject(object)

    switch (objectType) {
      case ObjectType.TEXT:
        return [await this.exportVectorText(object as VectorText)]

      case ObjectType.VECTOR_GROUP: {
        const group = await object.clone() as fabric.Group
        const removed = group.removeAll()
        const exportPromises = removed.map(async (o, index) => {
          const type = getTypeOfFabricObject(o)
          if (o.isVectorTextGroup) {
            //矢量里有位置信息的文字
            return await this.exportVectorText(o as VectorText, true)
          } else if (type === ObjectType.VECTOR_GROUP) {
            //组里的组
            return await this.export(o)
          } else if (type === ObjectType.TEXT) {
            //画布添加的文字
            return await this.exportVectorText(o as VectorText)
          } else {
            return this.exportVector(o)
          }
        })
        const results = await Promise.all(exportPromises);

        [group, ...removed].forEach(o => o.dispose())
        return results.flat()
      }

      case ObjectType.VECTOR:
        return [this.exportVector(object)]

      default:
        return [this.exportBitmap(object)]
    }
  }

  exportBitmap(object: fabric.FabricObject) {
    const { left, top, width, height } = object.getBoundingRect()

    return {
      type: ExportedLayerType.bitmap,
      data: object.toDataURL({
        format: 'png',
        quality: 0.1,
      }),
      x: left.toFixed(),
      y: top.toFixed(),
      width: width.toFixed(),
      height: height.toFixed()
    } satisfies ExportedLayer
  }

  exportVector(object: fabric.FabricObject) {
    const { _fill_angle_, _fill_space_ } = object

    return {
      type: ExportedLayerType.vector,
      data: wrapWithSvgRoot(object.toSVG(), this.canvasWidth, this.canvasHeight),
      fill: object.fill === VECTOR_FILL_MODES.FILL || object.fill === VECTOR_FILL_MODES.PATH_FILL,
      fillSpace: _fill_space_
        ? (_fill_space_ * WORKSPACE_SIZE_PIXELS / WORKSPACE_DEFAULT_SIZE_MM).toString()
        : undefined,
      fillAngle: _fill_angle_?.toString()
    } satisfies ExportedLayer
  }

  async textGroupToSVGAsync(group: fabric.Group, reviver?: (markup: string[]) => void): Promise<string> {
    const clone = await group.clone()
    clone.remove(...clone._objects.filter(o => o.isType(fabric.Rect.type)))
    const decorationSVG = clone.toSVG()

    const markup = [
      '<g>',
      decorationSVG,
      '</g>',
    ]

    if (reviver) reviver(markup)
    return markup.join('\n')
  }

  async exportVectorText(object: VectorText|fabric.Group, isTextGroup = false) {
    const { _fill_angle_, _fill_space_ } = object

    const svgString = isTextGroup
      ? await this.textGroupToSVGAsync(object as fabric.Group)
      : await (object as VectorText).toSVGAsync()

    return {
      type: ExportedLayerType.vector,
      data: wrapWithSvgRoot(svgString, this.canvasWidth, this.canvasHeight),
      fill: object.fill === VECTOR_FILL_MODES.FILL || object.fill === VECTOR_FILL_MODES.PATH_FILL,
      fillSpace: _fill_space_
        ? (_fill_space_ * WORKSPACE_SIZE_PIXELS / WORKSPACE_DEFAULT_SIZE_MM).toString()
        : undefined,
      fillAngle: _fill_angle_?.toString()
    } satisfies ExportedLayer
  }
}

export const ExportPlugin = createPlugin<ExportUtilities>(
  ({ canvas, state, getUtilities, width, height }) => {
    const downloadLink = document.createElement('a')

    return {
      onInit() {
        document.body.appendChild(downloadLink)
        if (window.ReactNativeWebView) return

        const cached = localStorage.getItem(CANVAS_CACHE_STORAGE_KEY)
        if (cached) {
          state.canvasCache = cached
        }
      },
      utilities: {
        async exportLayers(forceBitmap = false, isPreview = false): Promise<ExportedLayer[]> {
          let previouslySelectedObject: fabric.FabricObject | undefined = undefined
          if (isPreview) {
            previouslySelectedObject = canvas.getActiveObject()
            await temporarilyIgnoreCanvasEvents(canvas, () => {
              canvas.discardActiveObject()
              canvas.requestRenderAll()
            })
          } else {
            canvas.discardActiveObject()
            canvas.requestRenderAll()
          }

          const handler = new LayerExportHandler(forceBitmap, canvas.width, canvas.height)

          const objects = getValidObjects(canvas)
          const result: ExportedLayer[] = []

          for (const object of objects) {
            result.push(...await handler.export(object))
          }

          if (isPreview && previouslySelectedObject) {
            await temporarilyIgnoreCanvasEvents(canvas, () => {
              canvas.setActiveObject(previouslySelectedObject as fabric.FabricObject)
              canvas.requestRenderAll()
            })
          }

          return result
        },
        async exportCanvasAsJson(exportPreviewImage = true) {
          const { getCanvasData } = getUtilities()
          const { objects, version, __updated_at__ } = getCanvasData()

          let previewImageSrc = ''
          if (exportPreviewImage) {
            previewImageSrc = await exportCanvasToImage(canvas, 0, 0, width, height)
          }

          const data = {
            updated_at: __updated_at__,
            version,
            objects: objects.filter(o => !o.__is_system_object__),
            previewImageSrc
          } satisfies ExportedCanvasData

          return data as ExportedCanvasData
        },
        prepareCanvasCache(data: any) {
          try {
            if (typeof data === 'string') {
              data = JSON.parse(data)
            }

            const { version, objects } = data || {}

            if (version !== __FABRICJS_VERSION__) return
            if (!Array.isArray(objects) && (objects as Array<any>).length === 0) return

            state.canvasCache = data
          } catch (e) {
            //
          }
        },
        restoreCanvasFromCache() {
          const json = safeParseJson(state.canvasCache)

          if (json && 'objects' in json && Array.isArray(json.objects) && json.objects.length > 0) {
            canvas.loadFromJSON(json)
              .then(() => {
                state.canvasCache = null
                getUtilities().clearHistory()
                getUtilities().zoomToMinimum()
              })
          }
        },
        __dev__exportSingleSvg(svgString) {
          if (!import.meta.env.DEV) return

          downloadLink.href = 'data:image/svg+xml;charset=utf-8,'
            + encodeURIComponent(svgString)
          downloadLink.download = Date.now().toString()
          downloadLink.click()
        },
        __dev__exportActiveObject() {
          const [active] = canvas.getActiveObjects()
          if (!active) return

          this.__dev__exportSingleSvg(
            wrapWithSvgRoot(active.toSVG() || '', width, height)
          )
        },
        async __dev__previewAsSvg() {
          if (!import.meta.env.DEV) return

          const objects = getValidObjects(canvas)

          for (let i = 0; i < objects.length; i++) {
            const object = objects[i]

            if (object instanceof fabric.FabricImage || object instanceof fabric.FabricText) {
              downloadLink.href = object.toDataURL({
                format: 'png',
                quality: 1,
                left: -object.getX(),
                top: -object.getY(),
                width,
                height
              })
            } else {
              const svgString = object.toSVG()

              downloadLink.href = 'data:image/svg+xml;charset=utf-8,'
                + encodeURIComponent(
                  wrapWithSvgRoot(svgString, width, height)
                )
            }

            downloadLink.download = `layer-${i + 1}`
            downloadLink.click()
          }
        },
        async __dev__exportBase64Image(src) {
          if (!import.meta.env.DEV) return

          downloadLink.href = src
          downloadLink.download = Date.now().toString()
          downloadLink.click()
        },
      }
    }
  }
)
