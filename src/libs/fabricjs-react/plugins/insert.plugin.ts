// noinspection ExceptionCaughtLocallyJS

import { createPlugin } from '../types/base'
import { <PERSON><PERSON><PERSON> } from 'buffer'
import * as fabric from 'fabric'
import {
  BITMAP_MAX_SIZE_BYTES,
  CANVAS_OBJECTS_SIZE_LIMIT,
  DEFAULT_FONT_FAMILY,
  TEXT_STROKE_STYLES,
  VECTOR_FILL_MODES,
  WORKSPACE_SIZE_PIXELS
} from '@/libs/fabricjs-react/constants'
import { getImageSize, withAtomicOperation } from '@/libs/fabricjs-react/utils'
import toast from 'react-hot-toast'
import { formatBytes } from '@/utils/string'
import { VectorText } from '@/libs/fabricjs-react/custom_objects'
import { isString } from 'lodash'

import { FontResolver } from '@/libs/fabricjs-react/utils/font-resolver'
import { safeParseJson, safeParseSvgDocument } from '@/utils'
import { relocateObject } from '@/libs/fabricjs-react/utils/object'

export interface InsertUtilities {
  addText(text: string): void
  addVectorFromString(svgContent: string | fabric.FabricObject[], reformat?: boolean): Promise<void>
  addBitmapFromUrl(dataURL: string): Promise<fabric.FabricImage | null>
  addObjectsFromJSON(json: string | object): Promise<void>

  __dev__importFile(): void
  __dev__importFontFile(): void
}

function isTransparentColor(target: fabric.FabricObject) {
  return isString(target.fill) && /^#.{6}00$/.test(target.fill)
}

function removeNestedDuplicateClipPaths(svgString: string): string {
  function processNode(node: Element, parentClipPath: string | null) {
    const currentClipPath = node.getAttribute('clip-path')
    if (currentClipPath && currentClipPath === parentClipPath) {
      // 如果当前节点和父节点的 clip-path 相同，移除当前节点的
      node.removeAttribute('clip-path')
    }
    // 遍历子节点
    for (const child of Array.from(node.children)) {
      processNode(child as Element, currentClipPath || parentClipPath)
    }
  }

  const svgDoc = safeParseSvgDocument(svgString)
  if (!svgDoc) return ''

  const svgRoot = svgDoc.documentElement
  processNode(svgRoot, null)

  const serializer = new XMLSerializer()
  return serializer.serializeToString(svgDoc)
}

function convertToVectorText(oldText: fabric.Text): VectorText {
  const text = new VectorText(oldText.text ?? '')
  text.set({
    left: oldText.left,
    top: oldText.top,
    originX: oldText.originX,
    originY: oldText.originY,
    angle: oldText.angle,
    scaleX: oldText.scaleX,
    scaleY: oldText.scaleY,
    fontSize: oldText.fontSize,
  })
  return text
}

export const InsertPlugin = createPlugin<InsertUtilities>(
  ({ t, canvas, state, getUtilities }) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.style.display = 'none'

    function _reformatVector(target: fabric.FabricObject): typeof VECTOR_FILL_MODES[keyof typeof VECTOR_FILL_MODES] {
      target.strokeUniform = false

      if (!target.fill || isTransparentColor(target)) {
        getUtilities().clearFillForVector(target)
        return VECTOR_FILL_MODES.NO_FILL
      } else {
        getUtilities().fillVector(target)
        return VECTOR_FILL_MODES.FILL
      }
    }

    async function _addVectorObjects(objects: fabric.FabricObject[], opts?: { reformat: boolean, relocate: boolean }) {
      const { getWorkspaceCenter } = getUtilities()
      const { reformat = true, relocate = false } = opts || {}

      objects = objects.filter((o): o is fabric.FabricObject => !!o)

      if (objects.length === 1) {
        const target = objects[0]

        if (reformat) _reformatVector(target)
        if (relocate) relocateObject(target, getWorkspaceCenter())

        canvas.add(target)
        canvas.setActiveObject(target)
      } else {
        const resolvedObjects = await Promise.all(objects.map(async obj => {
          if (obj.isType(fabric.FabricText.type)) {
            obj = convertToVectorText(obj as fabric.Text)
            if (!FontResolver.fallbackFont) {
              toast(t('hints.generating'))
            }
            return await (obj as VectorText).toVectorGroup(true)
          }
          return obj
        }))
        const group = new fabric.Group(resolvedObjects) as fabric.Group

        if (relocate) relocateObject(group, getWorkspaceCenter())

        const reformatResult = objects.map(_reformatVector)
        if (reformatResult.every(item => item === VECTOR_FILL_MODES.FILL)) {
          group.fill = VECTOR_FILL_MODES.FILL
        } else if (reformatResult.every(item => item === VECTOR_FILL_MODES.NO_FILL)) {
          group.fill = VECTOR_FILL_MODES.NO_FILL
        }

        withAtomicOperation(group, () => {
          canvas.add(group)
          canvas.setActiveObject(group)
        })

        canvas.fire('object:added', { target: group })
      }
    }

    async function _addImageObject(image: fabric.FabricImage, isRaw = true, relocate = false): Promise<fabric.FabricImage | null> {
      try {
        const imageSize = getImageSize(image)
        if (imageSize > BITMAP_MAX_SIZE_BYTES) {
          toast(t('hints.bitmap-size-limit', { limit: formatBytes(BITMAP_MAX_SIZE_BYTES) }))
          return null
        }

        const { width, height } = image.getOriginalSize()
        const { scaleX: srcScaleX, scaleY: srcScaleY } = image
        // console.log(`width=${width}, height=${height}, top=${top}, left=${left}, srcScaleX=${srcScaleX}, srcScaleY=${srcScaleY}`)

        const scaleX = WORKSPACE_SIZE_PIXELS * 0.9375 / width
        const scaleY = WORKSPACE_SIZE_PIXELS * 0.9375 / height
        const targetScale = Math.min(scaleX, scaleY)

        if (targetScale < 1) {
          // console.log(`image has size of ${width}*${height}, apply scale of ${targetScale.toFixed(2)}`)
          const scaledImage = await fabric.FabricImage.fromURL(
            image.getSrc(),
            undefined,
            { scaleX: targetScale, scaleY: targetScale }
          )
          const scaledWidth = width * targetScale
          const scaledHeight = height * targetScale

          const scaledImageSrc = scaledImage.toDataURL({
            width: scaledWidth,
            height: scaledHeight,
          })
          // getUtilities().__dev__exportBase64Image(scaledImageSrc)

          scaledImage.dispose()

          if (isRaw) {
            await image.setSrc(scaledImageSrc)
            image.set({
              width: scaledWidth,
              height: scaledHeight
            })
          } else {
            image = await fabric.FabricImage.fromObject({
              ...image,
              src: scaledImageSrc,
              width: scaledWidth,
              height: scaledHeight,
              scaleX: srcScaleX / targetScale,
              scaleY: srcScaleY / targetScale
            })
          }
        }

        // 添加 erasable: true 属性，确保位图可被擦除
        image.set({ erasable: true })

        image.filters.push(new fabric.filters.Grayscale())
        image.applyFilters()
        if (relocate) {
          image.setXY(getUtilities().getWorkspaceCenter(), 'center', 'center')
        }

        canvas.add(image)
        canvas.setActiveObject(image)

        return image
      } catch (e) {
        console.error(e)
        return null
      }
    }

    async function checkObjectsAmountConstraint(incomingObjects = 1) {
      if (canvas._objects.length + incomingObjects > CANVAS_OBJECTS_SIZE_LIMIT) {
        toast.error(t('hints.reach-limit-of-layers'))
        return Promise.reject(null)
      }
    }

    return {
      onInit() {
        document.body.append(input)
      },
      utilities: {
        async addText(content: string) {
          await checkObjectsAmountConstraint()

          if (!FontResolver.fallbackFont) {
            toast(t('hints.font-still-loading'))
            void FontResolver.loadFallbackFont()
            return Promise.reject()
          }

          const { getWorkspaceCenter, zoomToMinimum } = getUtilities()
          const text = new VectorText(content)

          text.set({
            fontFamily: DEFAULT_FONT_FAMILY,
            fontSize: getUtilities().pointToPixel(16),
            fontWeight: 'normal',
            fontStyle: 'normal',
            underline: false,
            linethrough: false,
            lineHeight: 1,
            pathAlign: 'ascender',

            ...TEXT_STROKE_STYLES
          })
          text.setXY(getWorkspaceCenter(), 'center', 'center')

          canvas.add(text)
          canvas.setActiveObject(text)
          zoomToMinimum()
        },
        async addVectorFromString(src: string, reformat = true) {

          return fabric
            .loadSVGFromString(removeNestedDuplicateClipPaths(src))
            .then(async ({ objects }) => {
              await checkObjectsAmountConstraint(objects.length)
              return _addVectorObjects(
                objects.filter(Boolean) as fabric.FabricObject[],
                { reformat, relocate: true }
              )
            })
            .then(() => getUtilities().zoomToMinimum())
            .catch(e => {
              console.warn(e)
            })
        },

        async addBitmapFromUrl(dataURL: string) {
          await checkObjectsAmountConstraint()

          try {
            const img = await fabric.FabricImage.fromURL(dataURL)
            const result = await _addImageObject(img, true, true)
            getUtilities().zoomToMinimum()
            return result
          } catch (error) {
            console.error('导入图像失败：', error)
            throw error
          }
        },
        async addObjectsFromJSON(json: string | object) {
          type JSON = { version?: string; objects: any[] }
          const safeJson = typeof json === 'string' ? safeParseJson<JSON>(json) : json as JSON

          if (!safeJson) return

          const { objects } = safeJson

          // if (version !== __FABRICJS_VERSION__) throw new Error(`invalid \`version\`, got ${version} but require ${__FABRICJS_VERSION__}`)

          if (!Array.isArray(objects) || objects.length === 0) throw new Error('invalid `objects`, only non-empty array is acceptable')

          return new Promise<void>(resolve => {
            fabric.util
              .enlivenObjects<fabric.FabricObject>(objects)
              .then(enlivens => checkObjectsAmountConstraint(enlivens.length).then(() => enlivens))
              .then(async enlivens => {
                if (enlivens.some(o => o.isType(VectorText.type))) {
                  await FontResolver.loadFallbackFont()
                }

                for (const object of enlivens) {
                  await withAtomicOperation(object, async () => {
                    if (object.isType(VectorText.type)) {
                      canvas.add(object)
                    }
                    else if (object.isType(fabric.FabricImage.type)) {
                      const image = object as fabric.FabricImage
                      if (getImageSize(image) < BITMAP_MAX_SIZE_BYTES) {
                        await _addImageObject(image, false)
                      }
                    }
                    else {
                      // The object is neither a Text nor an Image, so can be considered as a vector
                      await _addVectorObjects([object], { reformat: false, relocate: false })
                    }
                  })
                }

                canvas.fire('object:added')
                resolve()
              })
              .then(() => {
                setTimeout(() => canvas.requestRenderAll(), 500)
                getUtilities().zoomToMinimum()
              })
          })
        },

        __dev__importFile() {
          if (!import.meta.env.DEV) return
          input.accept = '.sl,.svg,image/jpeg,image/png,image/webp,image/jpg,image/bmp'
          input.onchange = async e => {
            const [file] = (e.target as HTMLInputElement).files || [] as File[]
            const buffer = Buffer.from(await file.arrayBuffer())

            const rawContent = buffer.toString('utf-8')

            if (file.name.endsWith('.svg')) {
              this.addVectorFromString(rawContent)
            } else if (file.name.endsWith('.sl')) {
              this.addObjectsFromJSON(JSON.parse(rawContent))
            } else {
              const base64 = buffer.toString('base64')
              this.addBitmapFromUrl(`data:${file.type};base64,${base64}`)
            }
          }

          input.click()
        },
        __dev__importFontFile() {
          if (!import.meta.env.DEV) return
          input.accept = '.ttf,.otf,.woff,.woff2'

          input.onchange = async e => {
            const [file] = (e.target as HTMLInputElement).files || [] as File[]

            const fontFamily = file.name.slice(0, file.name.indexOf('.'))
            // const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1)
            const buffer = await (file as File).arrayBuffer()
            const base64 = Buffer.from(buffer).toString('base64')

            const styleEl = document.createElement('style')
            styleEl.innerHTML = `
              @font-face {
                font-family: '${fontFamily}';
                src: url(data:font/truetype;base64,${base64}) format('truetype');
              }
            `

            document.body.append(styleEl)
            if (!state.fontFamilies) {
              state.fontFamilies = []
            }
            state.fontFamilies.push(fontFamily)
          }
          input.click()
        },
      }
    }
  }
)
