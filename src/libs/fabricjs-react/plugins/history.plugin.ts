import { createPlugin } from '../types/base'

import * as fabric from 'fabric'
import { Canvas, CanvasEvents } from 'fabric'
import { PROPERTIES_TO_INCLUDE_WHEN_EXPORT } from '@/libs/fabricjs-react/constants'

type CanvasState = {
  objects: any[]
  version: string
  __updated_at__: number
  [p: string]: any
}

export interface HistoryUtilities {
  undo(): Promise<void>
  redo(): Promise<void>
  clearHistory(): void
  getCanvasData(): CanvasState
  getCanvasDataVersion(): number | null
}

type StackChangeCallback = (stack: any[]) => void

export class FabricHistory implements HistoryUtilities {
  private _undoStack: any[] = []
  private _redoStack: any[] = []

  private _currentState: CanvasState
  private _historyProcessing: boolean = false

  constructor(
    private canvas: Canvas,
    private onUndoStackChange?: StackChangeCallback,
    private onRedoStackChange?: StackChangeCallback,
  ) {
  }

  public init() {
    this._currentState = this._buildCurrentState()

    const events = this._initEvents()
    this.canvas.on(events)
  }

  public undo() {
    return new Promise<void>(resolve => {
      this._historyProcessing = true

      const history = this._undoStack.pop()
      this.onUndoStackChange?.(this._undoStack)

      if (history) {
        this._redoStack.push(this._buildCurrentState())
        this.onRedoStackChange?.(this._redoStack)
        this._currentState = history
        resolve(this._loadHistory(history, 'history:undo'))
      } else {
        this._historyProcessing = false
        resolve()
      }
    })
  }

  public redo() {
    return new Promise<void>(resolve => {
      this._historyProcessing = true

      const history = this._redoStack.pop()
      this.onRedoStackChange?.(this._redoStack)
      if (history) {
      // Every redo action is actually a new action to the undo history
        this._undoStack.push(this._buildCurrentState())
        this.onUndoStackChange?.(this._undoStack)
        this._currentState = history
        resolve(this._loadHistory(history, 'history:redo'))
      } else {
        this._historyProcessing = false
        resolve()
      }
    })
  }

  public clearHistory() {
    this._undoStack = this._redoStack = []
    this.onUndoStackChange?.(this._undoStack)
    this.onRedoStackChange?.(this._redoStack)
  }

  public getCanvasData() {
    return this._currentState
  }

  public getCanvasDataVersion(): number | null {
    const current = this.getCanvasData()
    if (current && Array.isArray(current.objects) && current.objects.length > 1) {
      return current.__updated_at__
    }

    return null
  }

  private _buildCurrentState() {
    const json = this.canvas.toDatalessJSON(PROPERTIES_TO_INCLUDE_WHEN_EXPORT)
    if (Array.isArray(json.objects) && json.objects.length) {
      json['__updated_at__'] = Date.now()
    }

    // 保存当前视图缩放和变换
    json.__viewportTransform__ = this.canvas.viewportTransform?.slice()
    json.__zoom__ = this.canvas.getZoom()

    return json
  }

  private _initEvents() {
    const events: (keyof CanvasEvents)[] = [
      'object:added',
      'object:removed',
      'object:modified',
    ]

    return Object.fromEntries(
      events.map(event => [
        event,
        e => this._handleCanvasEvent({ eventType: event, target: e.target }),
      ])
    ) as Record<keyof CanvasEvents, (...args: any[]) => any>
  }

  private async _loadHistory(history: any, event: keyof Pick<CanvasEvents, 'history:undo' | 'history:redo'>) {
    const objects = history.objects.filter(o => !o.__is_system_object__)

    return this.canvas.loadFromJSON({ ...history, objects })
      .then(() => {
        // 恢复视图状态
        if (history.__viewportTransform__) {
          this.canvas.setViewportTransform(history.__viewportTransform__)
        }
        if (history.__zoom__) {
          this.canvas.setZoom(history.__zoom__)
        }

        this.canvas.renderAll()

        // @ts-ignore
        this.canvas.fire(event, { objects: objects.length })
        this._historyProcessing = false
      })
  }

  private _handleCanvasEvent(e: { eventType: keyof CanvasEvents; target: fabric.FabricObject }) {
    if (this._historyProcessing) return

    if (!e || !e.target || (!e.target.__is_system_object__ && !e.target.__should_ignore_by_history__)) {
      this._undoStack.push(this._currentState)
      this.onUndoStackChange?.(this._undoStack)

      this._redoStack = []
      this.onRedoStackChange?.(this._redoStack)

      this._currentState = this._buildCurrentState()
    }
  }
}

export const HistoryPlugin = createPlugin<HistoryUtilities>(
  ({ canvas, state }) => {
    const historyImpl = new FabricHistory(
      canvas,
      undo => state.undoCount = undo.length,
      redo => state.redoCount = redo.length
    )

    return {
      onInit() {
        return historyImpl.init()
      },
      utilities: {
        undo() {
          return historyImpl.undo()
        },
        redo() {
          return historyImpl.redo()
        },
        clearHistory() {
          return historyImpl.clearHistory()
        },
        getCanvasData() {
          return historyImpl.getCanvasData()
        },
        getCanvasDataVersion() {
          return historyImpl.getCanvasDataVersion()
        }
      }
    }
  }
)
