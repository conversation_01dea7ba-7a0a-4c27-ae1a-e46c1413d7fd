import { createPlugin } from '../types/base'
import { WORKSPACE_SIZE_PIXELS } from '@/libs/fabricjs-react/constants'
import * as fabric from 'fabric'
import { AlignType } from '@/libs/fabricjs-react/plugins/vector.plugin'

export interface AlignUtilities {
  alignObjectsToCanvas(align: string): void
  alignObjectsWithinGroup(alignment: AlignType): void
}

/**
 * 组内对齐
 */
class WithinGroupAlignResolver {
  constructor(private readonly canvas: fabric.Canvas) {
  }

  public alignWithinGroup(alignment: AlignType) {
    const canvas = this.canvas
    if (!canvas) return

    const objects = canvas.getActiveObjects().filter(obj => obj.selectable && obj.visible)
    const objectCount = objects.length

    if (objectCount < 2) return

    // 先解除ActiveSelection，避免fabric自动重定位导致乱飞
    canvas.discardActiveObject()

    const bounds = objects.map(obj => obj.getBoundingRect())
    const lefts = bounds.map(b => b.left)
    const rights = bounds.map(b => b.left + b.width)
    const tops = bounds.map(b => b.top)
    const bottoms = bounds.map(b => b.top + b.height)

    // 选区最大范围
    const minX = Math.min(...lefts)
    const maxX = Math.max(...rights)
    const minY = Math.min(...tops)
    const maxY = Math.max(...bottoms)

    // 找到基准对象
    let refIdx = 0
    switch (alignment) {
      case 'left':
        refIdx = lefts.indexOf(minX)
        this.#alignToObject(objects, bounds, 'left', refIdx)
        break
      case 'right':
        refIdx = rights.indexOf(maxX)
        this.#alignToObject(objects, bounds, 'right', refIdx)
        break
      case 'top':
        refIdx = tops.indexOf(minY)
        this.#alignToObject(objects, bounds, 'top', refIdx)
        break
      case 'bottom':
        refIdx = bottoms.indexOf(maxY)
        this.#alignToObject(objects, bounds, 'bottom', refIdx)
        break
      case 'center': {
        const centerX = (minX + maxX) / 2
        this.#alignToCenter(objects, 'left', centerX)
        break
      }
      case 'middle': {
        const centerY = (minY + maxY) / 2
        this.#alignToCenter(objects, 'top', centerY)
        break
      }
      case 'horizontal':
        if (objectCount >= 3) this.#distributeObjects(objects, 'horizontal', minX, maxX)
        break
      case 'vertical':
        if (objectCount >= 3) this.#distributeObjects(objects, 'vertical', minY, maxY)
        break
    }

    // 对齐后重新设置ActiveSelection，保证操作体验
    const selection = new fabric.ActiveSelection(objects, { canvas })
    canvas.setActiveObject(selection)
    canvas.requestRenderAll()

    canvas.fire('object:modified', { target: selection })
  }

  /**
   * 等距分布对象
   */
  #distributeObjects(objects: fabric.Object[], direction: 'horizontal' | 'vertical', minBoundary: number, maxBoundary: number) {
    const isHorizontal = direction === 'horizontal'
    const property = isHorizontal ? 'left' : 'top'
    const sizeProperty = isHorizontal ? 'width' : 'height'

    const sortedObjects = [...objects]
      .sort((a, b) => {
        return a.getBoundingRect()[property] - b.getBoundingRect()[property]
      })

    const totalSpace = maxBoundary - minBoundary
    const totalSize = sortedObjects
      .reduce(
        (sum, obj) => sum + obj.getBoundingRect()[sizeProperty]
        , 0
      )

    const spacing = (totalSpace - totalSize) / (sortedObjects.length - 1)

    let currentPos = minBoundary

    for (const obj of sortedObjects) {
      const objBounds = obj.getBoundingRect()
      obj[property] += currentPos - objBounds[property]
      currentPos += objBounds[sizeProperty] + spacing
    }
  }

  /**
   * 中心对齐
   */
  #alignToCenter(objects: fabric.Object[], property: 'left' | 'top', centerPosition: number) {
    for (const obj of objects) {
      const objBounds = obj.getBoundingRect()
      const objSize = property === 'left' ? objBounds.width : objBounds.height
      const newPosition = centerPosition - objSize / 2

      obj[property] += newPosition - objBounds[property]
      obj.setCoords()
    }
  }

  /**
   * 以基准对象为参考进行对齐
   */
  #alignToObject(
    objects: fabric.Object[],
    bounds: fabric.TBBox[],
    alignType: 'left' | 'right' | 'top' | 'bottom',
    refIdx: number
  ) {
    const refBound = bounds[refIdx]

    for (const obj of objects) {
      const i = objects.indexOf(obj)
      if (i === refIdx) continue // 基准对象不动

      const bounding = obj.getBoundingRect()
      let deltaX = 0, deltaY = 0

      switch (alignType) {
        case 'left':
          deltaX = refBound.left - bounding.left
          break
        case 'right':
          deltaX = refBound.width + refBound.left - (bounding.width + bounding.left)
          break
        case 'top':
          deltaY = refBound.top - bounding.top
          break
        case 'bottom':
          deltaY = refBound.height + refBound.top - (bounding.height + bounding.top)
          break
      }

      obj.set({
        left: obj.left + deltaX,
        top: obj.top + deltaY
      })
      obj.setCoords()
    }
  }
}

export const AlignPlugin = createPlugin<AlignUtilities>(
  ({ canvas, state }) => {

    const alignResolver = new WithinGroupAlignResolver(canvas)

    return {
      utilities: {
        async alignObjectsToCanvas(
          align: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'
        ) {
          if (!canvas) return

          const objects = canvas.getActiveObjects().filter(obj => obj.selectable && obj.visible)
          if (!objects.length) return

          const workspaceWidth = state.workspaceWidth || WORKSPACE_SIZE_PIXELS
          const workspaceHeight = state.workspaceHeight || WORKSPACE_SIZE_PIXELS

          // 先解除 ActiveSelection，避免坐标系统混乱
          canvas.discardActiveObject()

          // 获取每个对象的绝对边界框（相对于 Canvas）
          const bounds = objects.map(obj => obj.getBoundingRect())

          // 计算所有对象的整体边界框
          const groupBounds = {
            left: Math.min(...bounds.map(b => b.left)),
            top: Math.min(...bounds.map(b => b.top)),
            right: Math.max(...bounds.map(b => b.left + b.width)),
            bottom: Math.max(...bounds.map(b => b.top + b.height))
          }

          groupBounds.width = groupBounds.right - groupBounds.left
          groupBounds.height = groupBounds.bottom - groupBounds.top

          let offsetX = 0
          let offsetY = 0

          // 根据对齐方式计算偏移量
          switch (align) {
            case 'left':
              offsetX = -groupBounds.left
              break
            case 'center':
              offsetX = workspaceWidth / 2 - (groupBounds.left + groupBounds.width / 2)
              break
            case 'right':
              offsetX = workspaceWidth - (groupBounds.left + groupBounds.width)
              break
            case 'top':
              offsetY = -groupBounds.top
              break
            case 'middle':
              offsetY = workspaceHeight / 2 - (groupBounds.top + groupBounds.height / 2)
              break
            case 'bottom':
              offsetY = workspaceHeight - (groupBounds.top + groupBounds.height)
              break
          }

          // 移动每个对象
          for (let i = 0; i < objects.length; i++) {
            const obj = objects[i]
            const bound = bounds[i]

            // 计算对象新的位置
            const newLeft = bound.left + offsetX
            const newTop = bound.top + offsetY

            // 设置对象的新位置
            obj.set({
              left: newLeft,
              top: newTop
            })
            obj.setCoords()
          }

          // 重新创建 ActiveSelection
          if (objects.length > 1) {
            const selection = new fabric.ActiveSelection(objects, { canvas })
            canvas.setActiveObject(selection)
            canvas.fire('object:modified', { target: selection })
          } else {
            const [object] = objects
            canvas.setActiveObject(object)
            canvas.fire('object:modified', { target: object })
          }

          canvas.requestRenderAll()
        },

        alignObjectsWithinGroup(alignment: AlignType) {
          return alignResolver.alignWithinGroup(alignment)
        }
      }
    }
  }
)
