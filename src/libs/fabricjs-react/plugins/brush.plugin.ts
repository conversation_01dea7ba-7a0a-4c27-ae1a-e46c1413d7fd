import { createPlugin } from '@/libs/fabricjs-react/types/base'
import { EraserBrush } from '@erase2d/fabric'
import * as fabric from 'fabric'

export interface BrushUtilities {
  setBrushSize(size: number): void
  toggleEraser(enable: boolean): void;
}

export const BrushPlugin = createPlugin<BrushUtilities>(
  ({ canvas, state }) => {
    const eraser = new EraserBrush(canvas)
    eraser.width = 30
    eraser.on('end', e => {
      const { targets: erasedTargets, path } = e.detail
      e.preventDefault() // prevent erasing being committed to the tree
      
      // 确保所有目标对象都是可擦除的
      const validTargets = erasedTargets.filter(target => {
        if (target && typeof target === 'object') {
          // 确保位图对象的 erasable 属性为 true
          if (target instanceof fabric.FabricImage && target.erasable !== true) {
            target.set({ erasable: true })
          }
          return true
        }
        return false
      })
      
      if (validTargets.length > 0) {
        eraser.commit({ targets: validTargets, path })
          .then(() => {
            // 对每个擦除目标触发对象修改事件
            validTargets.forEach(target => {
              canvas.fire('object:modified', { target })
            })
            
            if (state.activeObjectCache) {
              canvas.setActiveObject(state.activeObjectCache)
              canvas.requestRenderAll()
            }
          })
          .catch(err => {
            console.error('Error committing eraser changes:', err)
            canvas.requestRenderAll()
          })
      } else {
        canvas.requestRenderAll()
      }
    })

    return {
      utilities: {
        setBrushSize(size: number) {
          const { freeDrawingBrush } = canvas
          if (freeDrawingBrush) {
            freeDrawingBrush.width = size
          }
        },
        toggleEraser(enable: boolean) {
          if (enable) {
            state.activeObjectCache = canvas.getActiveObjects()?.[0]
            canvas.isDrawingMode = true
            canvas.freeDrawingBrush = eraser
          } else {
            canvas.freeDrawingBrush = undefined
            canvas.isDrawingMode = false

            return
          }
        }
      }
    }
  }
)
