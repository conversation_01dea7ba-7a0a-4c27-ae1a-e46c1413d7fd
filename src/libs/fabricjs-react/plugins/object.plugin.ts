import { createPlugin } from '../types/base'
import * as fabric from 'fabric'
import { withAtomicOperation } from '@/libs/fabricjs-react/utils'
import _, { isNil } from 'lodash'
import {
  getRotatedBoundingSize,
  isObjectLocked,
  setObjectLocked,
  toggleObjectLocking
} from '@/libs/fabricjs-react/utils/object'

export interface ObjectUtilities {
  rotateToDegree(degree: number, commit?: boolean): number

  rotateWithDegreeDelta(degreeDelta: number): number

  setBasicProp(prop: 'x' | 'y' | 'w' | 'h', val: number | undefined): void

  toggleLocking(target: fabric.FabricObject): void

  toggleVisible(target: fabric.FabricObject): void

  toggleSelection(target: fabric.FabricObject): boolean

  rasterization(): void
}

export const ObjectPlugin = createPlugin<ObjectUtilities>(
  ({ canvas, getUtilities }) => {

    function _setActiveObjects(objects: fabric.FabricObject[]) {
      if (!objects.length) {
        canvas.discardActiveObject()
      } else if (objects.length === 1) {
        canvas.setActiveObject(objects[0])
      } else {
        canvas.setActiveObject(new fabric.ActiveSelection([...objects]))
      }
    }

    return {
      utilities: {
        rotateToDegree(degree, commit = false) {
          const target = canvas.getActiveObject()
          if (!target || isObjectLocked(target)) return -1

          withAtomicOperation(target, () => {
            target.centeredRotation = true
            target.rotate((degree % 360 + 360) % 360)
          })

          if (commit) {
            canvas.fire('object:modified', { target })
          }
          canvas.discardActiveObject()
          canvas.setActiveObject(target)
          canvas.fire('selection:updated', { selected: [target], deselected: [] })
          canvas.requestRenderAll()

          return target.angle as number
        },

        rotateWithDegreeDelta(degreeDelta) {
          const target = canvas.getActiveObject()
          if (!target || isObjectLocked(target)) return -1

          return this.rotateToDegree(target.angle + degreeDelta, true)
        },

        setBasicProp(prop, val) {
          if (isNil(val)) return

          const target = canvas.getActiveObject()
          if (!target || isObjectLocked(target)) return

          const pixels = getUtilities().unitToPixels(val)

          const isActiveSelection = target instanceof fabric.ActiveSelection

          const { width: boundingWidth, height: boundingHeight } = getRotatedBoundingSize(target)

          const handler = (o: fabric.FabricObject): fabric.FabricObject => {
            const deltaX = o.getX() - target.getX()
            const deltaY = o.getY() - target.getY()
            const parentX = target.left ?? target.getX()
            const parentY = target.top ?? target.getY()

            const insideDelta = prop === 'x'
              ? pixels - parentX
              : pixels - parentY

            if (prop === 'x') {
              if (isActiveSelection) {
                o.left = o.left + insideDelta
              } else {
                o.left = pixels + deltaX
              }
            }

            if (prop === 'y') {
              if (isActiveSelection) {
                o.top = o.top + insideDelta
              } else {
                o.top = pixels + deltaY
              }
            }

            if (prop === 'w' || prop === 'h') {
              const scaleMultiplier = _.floor(
                prop === 'w'
                  ? pixels / boundingWidth
                  : pixels / boundingHeight
                , 8
              )

              if (isActiveSelection) {
                o.setX(target.getX() + (deltaX) * scaleMultiplier)
                o.setY(target.getY() + (deltaY) * scaleMultiplier)
              }

              o.scale(o.scaleX * scaleMultiplier)
            }

            return o
          }

          if (isActiveSelection) {
            const objects = canvas
              .getActiveObjects()
              .map(o => handler(o))

            canvas.discardActiveObject()
            const active = new fabric.ActiveSelection(objects)
            canvas.setActiveObject(active)
            return canvas.fire('object:modified', { target: active })
          } else {
            canvas.discardActiveObject()
            handler(target)
            canvas.setActiveObject(target)
            return canvas.fire('object:modified', { target })
          }
        },

        toggleLocking(target) {
          if (target instanceof fabric.ActiveSelection) {
            const locked = isObjectLocked(target)
            target._objects.forEach(o => setObjectLocked(o, !locked))
            canvas.setActiveObject(new fabric.ActiveSelection(target._objects))
          } else {
            toggleObjectLocking(target)
          }

          canvas.fire('selection:updated')
          return canvas.requestRenderAll()
        },

        toggleVisible(target) {
          if (target.visible && canvas.getActiveObjects().includes(target)) {
            this.toggleSelection(target)
          }

          target.visible = !target.visible
          canvas.fire('object:modified', { target })

          return canvas.requestRenderAll()
        },

        toggleSelection(target) {
          if (!target.visible) return false

          const activeObjects = canvas.getActiveObjects()
          const index = activeObjects.indexOf(target)

          // 不在当前选中组中，则追加
          if (index === -1) {
            _setActiveObjects([...activeObjects, target])
          } else {
            _setActiveObjects(activeObjects.toSpliced(index, 1))
          }

          canvas.requestRenderAll()

          return true
        },

        async rasterization() {
          if (!this.canRasterization()) return

          const actives = canvas.getActiveObjects()
          const group = new fabric.Group(actives)
          const dataURL = group.toDataURL({
            format: 'png',
            quality: 1
          })
          const image = await fabric.FabricImage.fromURL(dataURL)
          withAtomicOperation([image, ...actives], () => {
            image.erasable = true
            image.setXY(group.getXY())
            canvas.remove(...actives)
            canvas.add(image)
          })
          canvas.discardActiveObject()
          canvas.setActiveObject(image)
          canvas.requestRenderAll()

          canvas.fire('selection:updated')
          canvas.fire('object:modified')
        },
      }
    }
  }
)
