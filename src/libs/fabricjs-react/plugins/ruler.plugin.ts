import { createPlugin, FabricPlugin } from '../types/base'
import { Canvas, Color, Point } from 'fabric'
import { MM_PER_INCH, WORKSPACE_DEFAULT_SIZE_MM, WORKSPACE_SIZE_PIXELS } from '@/libs/fabricjs-react/constants'
import { range } from 'lodash'
import { LengthUnit } from '@/libs/shared/types'

const MAJOR_TICK_MARK_LINE_LENGTH = 15
const MINOR_TICK_MARK_LINE_LENGTH = 4

interface RulerOptions {
  canvas: Canvas;

  /**
   * 标尺宽高
   * @default 20
   */
  ruleSize?: number;

  /**
   * 字体大小
   * @default 10
   */
  fontSize?: number;

  /**
   * 背景颜色
   */
  backgroundColor?: string;

  /**
   * 文字颜色
   */
  textColor?: string;

  /**
   * 边框颜色
   */
  borderColor?: string;
}

export class Ruler implements FabricPlugin<RulerUtilities> {

  private _options: Required<RulerOptions>

  protected _ctx: CanvasRenderingContext2D

  private startCalibration: undefined | Point

  private unit: LengthUnit = LengthUnit.mm

  constructor(private canvas: Canvas) {
    this._options = {
      canvas,
      ruleSize: 20,
      fontSize: 10,
      backgroundColor: '#EEEEEE',
      borderColor: '#ddd',
      textColor: '#B3B3B3',
    }
    this._ctx = canvas.getContext()
  }

  public onInit() {
    this.#render()
    this.canvas.on('after:render', () => this.#render())
  }

  public get utilities() {
    return {
      setRulerUnit: this.setRulerUnit.bind(this),
      getPixelsPerUnit: this.getPixelsPerUnit.bind(this),
      pixelsToUnit: this.pixelsToUnit.bind(this),
      unitToPixels: this.unitToPixels.bind(this)
    }
  }

  public getPixelsPerUnit() {
    return this.unit === LengthUnit.mm ? this.#pixelsPerMM : this.#pixelsPerInch
  }

  public pixelsToUnit(pixels: number): number {
    return pixels / this.getPixelsPerUnit()
  }

  public unitToPixels(unit: number): number {
    return unit * this.getPixelsPerUnit()
  }

  public setRulerUnit(unit: LengthUnit) {
    this.unit = unit
    this.#render()
  }

  get #pixelsPerMM() {
    return WORKSPACE_SIZE_PIXELS / WORKSPACE_DEFAULT_SIZE_MM
  }

  get #pixelsPerInch() {
    return this.#pixelsPerMM * MM_PER_INCH
  }

  #render() {
    const vpt = this.canvas.viewportTransform
    if (!vpt) return

    const { width, height } = this.#getSize()

    // 绘制尺子
    this.#drawRuler({
      isHorizontal: true,
      rulerLength: width,
      startCalibration: this.startCalibration?.x || -(vpt[4] / vpt[0]),
    })

    this.#drawRuler({
      isHorizontal: false,
      rulerLength: height,
      startCalibration: this.startCalibration?.y || -(vpt[5] / vpt[3]),
    })
  }

  #drawRuler(opt: { isHorizontal: boolean; rulerLength: number; startCalibration: number }) {
    const { isHorizontal, rulerLength, startCalibration } = opt
    const zoom = this.canvas.getZoom()

    const [gap, nextGap] = this.#getGaps(zoom)
    const unitLength = rulerLength / zoom
    const startValue = Math[startCalibration > 0 ? 'floor' : 'ceil'](startCalibration / gap) * gap
    const startOffset = startValue - startCalibration

    // 标尺背景
    const canvasSize = this.#getSize()
    this.#drawRect({
      left: 0,
      top: 0,
      width: isHorizontal ? canvasSize.width : this._options.ruleSize,
      height: isHorizontal ? this._options.ruleSize : canvasSize.height,
      fill: this._options.backgroundColor,
      stroke: this._options.borderColor,
    })

    // 颜色
    const textColor = new Color(this._options.textColor)

    const rangeEnd = Math.ceil(unitLength) - startOffset + 1
    const majorTicks = range(0, rangeEnd, gap)
    const minorTicks = nextGap ? range(0, rangeEnd, nextGap) : []

    const getPositionByTick = (tick: number) => {
      return Math.round((startOffset + tick) * zoom)
    }

    majorTicks
      .forEach(tick => {
        const position = getPositionByTick(tick)

        this.#drawTickMarkLine(position, isHorizontal, MAJOR_TICK_MARK_LINE_LENGTH)
        this.#drawGridLine(position, isHorizontal)

        const value = (startValue + tick) / this.getPixelsPerUnit()
        if (value < 0) return

        const decimals = this.unit === LengthUnit.mm || !value ? 0 : 1
        const textValue = `${value.toFixed(decimals)}${this.unit}`

        // 渲染标尺坐标刻度值
        this.#drawText({
          text: textValue,
          left: isHorizontal
            ? position + 4
            : this._options.ruleSize / 2 - this._options.fontSize / 2 + 4,
          top: isHorizontal
            ? this._options.ruleSize / 2 - 4
            : position + 4,
          fill: textColor.toRgb(),
          angle: isHorizontal ? 0 : -90,
          align: isHorizontal ? 'left' : 'right'
        })
      })

    minorTicks
      .forEach(tick => {
        const position = getPositionByTick(tick)
        this.#drawTickMarkLine(position, isHorizontal, MINOR_TICK_MARK_LINE_LENGTH)
      })
  }

  #getSize() {
    return {
      width: this.canvas.width ?? 0,
      height: this.canvas.height ?? 0,
    }
  }

  #drawTickMarkLine(position: number, isHorizontal: boolean, tickLength: number) {
    const left = isHorizontal ? position : this._options.ruleSize - tickLength
    const top = isHorizontal ? this._options.ruleSize - tickLength : position

    this.#drawLine({
      left,
      top,
      width: isHorizontal ? 0 : tickLength,
      height: isHorizontal ? tickLength : 0,
      stroke: '#CCC'
    })
  }

  #drawGridLine(position: number, isHorizontal: boolean) {
    const left = isHorizontal ? position : this._options.ruleSize
    const top = isHorizontal ? this._options.ruleSize : position

    this.#drawLine({
      left,
      top,
      width: isHorizontal ? 0 : 99999,
      height: isHorizontal ? 99999 : 0,
      stroke: '#F7F7F7',
      globalCompositeOperation: 'destination-over'
    })
  }

  #drawRect(
    options: {
      left: number;
      top: number;
      width: number;
      height: number;
      fill?: string | CanvasGradient | CanvasPattern;
      stroke?: string;
      strokeWidth?: number;
    }
  ) {
    const ctx = this._ctx
    ctx.save()
    const { left, top, width, height, fill, stroke, strokeWidth } = options
    ctx.beginPath()
    fill && (ctx.fillStyle = fill)
    ctx.rect(left, top, width, height)
    ctx.fill()

    if (stroke) {
      ctx.strokeStyle = stroke
      ctx.lineWidth = strokeWidth ?? 1
      ctx.stroke()
    }
    ctx.restore()
  };

  #drawText(
    options: {
      left: number;
      top: number;
      text: string;
      fill?: string | CanvasGradient | CanvasPattern;
      align?: CanvasTextAlign;
      angle?: number;
      fontSize?: number;
    }
  ) {
    const ctx = this._ctx
    ctx.save()
    const { left, top, text, fill, align, angle, fontSize } = options
    fill && (ctx.fillStyle = fill)
    ctx.textAlign = align ?? 'left'
    ctx.textBaseline = 'top'
    ctx.font = `${fontSize ?? 10}px sans-serif`
    if (angle) {
      ctx.translate(left, top)
      ctx.rotate((Math.PI / 180) * angle)
      ctx.translate(-left, -top)
    }
    ctx.fillText(text, left, top)
    ctx.restore()
  }

  #drawLine = (
    options: {
      left: number;
      top: number;
      width: number;
      height: number;
      stroke?: string | CanvasGradient | CanvasPattern;
      lineWidth?: number;
      globalCompositeOperation?: GlobalCompositeOperation
    }
  ) => {
    const ctx = this._ctx
    if (options.globalCompositeOperation) {
      ctx.globalCompositeOperation = options.globalCompositeOperation
    }

    ctx.save()
    const { left, top, width, height, stroke, lineWidth } = options
    ctx.beginPath()
    stroke && (ctx.strokeStyle = stroke)
    ctx.lineWidth = lineWidth ?? 1
    ctx.moveTo(left, top)
    ctx.lineTo(left + width, top + height)
    ctx.stroke()
    ctx.restore()

    ctx.globalCompositeOperation = 'source-over'
  }

  #getGaps(zoom: number) {
    let zooms, gaps
    if (this.unit === LengthUnit.mm) {
      zooms = [0.05, 0.2, 0.4, 1, 2]
      gaps = [20, 10, 5, 2, 1].map(o => o * this.#pixelsPerMM)
    } else {
      zooms = [0.05, 0.1, 0.2, 0.5, 1.25]
      gaps = [2, 1, 0.5, 0.2, 0.1].map(o => o * this.#pixelsPerInch)
    }

    let i = 0
    while (i < zooms.length && zooms[i] < zoom) {
      i++
    }

    // i = Math.max(i, 1)

    return [gaps[i - 1], gaps[i]]
  }
}

export type RulerUtilities = {
  setRulerUnit(unit: LengthUnit): void
  getPixelsPerUnit(): number
  pixelsToUnit(pixels: number): number
  unitToPixels(unit: number): number
}

export const RulerPlugin = createPlugin<RulerUtilities>(
  ({ canvas }) => new Ruler(canvas)
)
