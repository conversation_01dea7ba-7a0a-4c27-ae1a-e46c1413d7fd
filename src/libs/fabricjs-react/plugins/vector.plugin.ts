import { createPlugin, FabricCoreContext } from '../types/base'
import * as fabric from 'fabric'
import {
  ObjectType, VECTOR_DEFAULT_STROKE_WIDTH_FACTOR,
  VECTOR_FILL_MODES,
  VECTOR_MIN_FILL_SPACE,
  VECTOR_NIN_FILL_ANGLE
} from '../constants'
import { getTypeOfFabricObject, withAtomicOperation } from '@/libs/fabricjs-react/utils'

// 定义Canvas扩展方法的接口
declare module 'fabric' {
  interface Canvas {
    _applyPathTransform(obj: fabric.Object): void;
  }
}

export type AlignType =
  | 'left'
  | 'center'
  | 'right'
  | 'top'
  | 'middle'
  | 'bottom'
  | 'horizontal'
  | 'vertical'

export interface VectorUtilities {
  fillVector(obj: fabric.FabricObject): void
  pathFillVector(obj: fabric.FabricObject): void
  clearFillForVector(obj: fabric.FabricObject): void

  fillSelectedVector(): void
  pathFillSelectedVector(): void
  clearFillForSelectedVector(): void

  setFillSpace(val: number, commit?: boolean): void
  setFillAngle(val: number, commit?: boolean): void
}

/**
 * 扩展 FabricObject 原型，添加对路径填充的渲染支持
 */
class PathFillExtension {

  static init({ canvas, getUtilities }: FabricCoreContext) {
    // 在扩展前保存原生_renderFill，防止递归死机
    const fabricOriginalRenderFill = fabric.FabricObject.prototype._renderFill

    // 初始化时扩展 FabricObject
    fabric.FabricObject.prototype._renderFill = function(ctx) {
      // 检查是否是路径填充模式且有必要的参数
      const isPathFill = this.fill === VECTOR_FILL_MODES.PATH_FILL

      // 非路径填充模式直接使用原生方法
      if (!isPathFill) {
        fabricOriginalRenderFill.call(this, ctx)
        return
      }

      // 安全检查canvas和getActiveObject方法是否存在
      let isSelected = false
      try {
        if (this.canvas && typeof this.canvas.getActiveObject === 'function') {
          const activeObject = this.canvas.getActiveObject()
          isSelected = (activeObject === this ||
            (activeObject instanceof fabric.ActiveSelection &&
              (activeObject as fabric.ActiveSelection)._objects.indexOf(this) > -1))
        }
      } catch (e) {
        // 如果获取activeObject出错，认为未选中
        isSelected = false
      }

      // 确保空间和角度有默认值
      const spaceMM = this._fill_space_ === null ? VECTOR_MIN_FILL_SPACE : this._fill_space_
      const angle = this._fill_angle_ === null ? VECTOR_NIN_FILL_ANGLE : this._fill_angle_

      // 计算元素的有效缩放系数
      const objectScaleX = this.scaleX || 1
      const objectScaleY = this.scaleY || 1
      const effectiveScale = Math.sqrt(objectScaleX * objectScaleY)

      // 毫米单位转像素单位
      const baseSpacePixels = getUtilities().unitToPixels(spaceMM)

      // 更新对象属性，确保有正确的值
      if (this._fill_space_ === null) {
        this._fill_space_ = spaceMM
      }
      if (this._fill_angle_ === null) {
        this._fill_angle_ = angle
      }

      try {
        // 保存上下文状态
        ctx.save()

        // 根据对象类型选择不同的路径绘制方式
        // 手绘SVG、矩形、椭圆等基本图形用专门的路径生成
        // 复杂SVG和常规SVG仍使用_renderPathCommands
        let canClip = true
        // 添加形状检测类型列表
        const isBuiltInShape = this.type === 'rect' || this.type === 'MyRect' ||
          this.type === 'ellipse' || this.type === 'circle' ||
          this.type === 'polygon' || this.type === 'Star' ||
          this.type === 'MyTriangle'

        // 调试日志 - 检查对象是否有_renderPathCommands方法
        // console.log('对象类型和渲染方法:', {
        //   type: this.type,
        //   hasRenderPathCommands: typeof this._renderPathCommands === 'function'
        // })

        if (this.type === 'Star' || this.type === 'polygon' || this.type === 'MyTriangle') {
          // 特殊处理星形和多边形：优先使用_renderPathCommands
          ctx.beginPath()
          if (typeof this._renderPathCommands === 'function') {
            this._renderPathCommands(ctx)
          } else {
            // 如果没有_renderPathCommands方法，尝试使用points属性
            if (this.points && Array.isArray(this.points) && this.points.length > 0) {
              const points = this.points
              ctx.moveTo(points[0].x, points[0].y)
              for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y)
              }
              ctx.closePath()
            } else {
              canClip = false
            }
          }
        } else if (isBuiltInShape && typeof this._renderPathCommands === 'function') {
          // 对于我们添加了_renderPathCommands方法的形状，优先使用它
          ctx.beginPath()
          this._renderPathCommands(ctx)
        } else if (this.type === 'rect' || this.type === 'MyRect') {
          // 矩形路径
          PathFillExtension.drawRectPath(ctx, this.width, this.height)
        } else if (this.type === 'ellipse' || this.type === 'circle') {
          // 椭圆/圆形路径
          PathFillExtension.drawEllipsePath(ctx, this.rx || this.width / 2, this.ry || this.height / 2)
        } else if (typeof this._renderPathCommands === 'function') {
          // 复杂SVG和常规SVG优先使用_renderPathCommands，这是原始逻辑
          this._renderPathCommands(ctx)
        } else if (this.type === 'path' && typeof this.path === 'object' && Array.isArray(this.path)) {
          // 手绘Path对象特殊处理 - 仅当没有_renderPathCommands时使用此方法
          ctx.beginPath()
          const p = this.path
          if (p.length > 0) {
            ctx.moveTo(p[0][1], p[0][2])
            for (let i = 1; i < p.length; i++) {
              const cmd = p[i][0]
              if (cmd === 'L') {
                ctx.lineTo(p[i][1], p[i][2])
              } else if (cmd === 'Q') {
                ctx.quadraticCurveTo(p[i][1], p[i][2], p[i][3], p[i][4])
              } else if (cmd === 'C') {
                ctx.bezierCurveTo(p[i][1], p[i][2], p[i][3], p[i][4], p[i][5], p[i][6])
              }
            }
            ctx.closePath()
          } else {
            canClip = false
          }
        } else {
          // 无法安全绘制路径时使用原始填充
          canClip = false
        }

        if (!canClip) {
          ctx.restore()
          fabricOriginalRenderFill.call(this, ctx)
          return
        }

        // 绘制半透明背景
        ctx.fillStyle = typeof this.fill === 'string' ? this.fill : '#000000'
        ctx.globalAlpha = isSelected ? 0.1 : 0.05
        ctx.fill()
        ctx.globalAlpha = 1.0

        // 使用形状作为剪裁区域
        ctx.clip()

        // 计算线条绘制的参数
        const width = this.width || 10
        const height = this.height || 10

        // 计算真实尺寸
        const scaledWidth = Math.abs(width * objectScaleX)
        const scaledHeight = Math.abs(height * objectScaleY)

        // 计算复杂度系数
        const objectArea = scaledWidth * scaledHeight
        const normalizedArea = Math.sqrt(objectArea) / 100
        const complexityFactor = Math.max(0.5, Math.min(1.0, normalizedArea))

        // 调整间距
        const adjustedSpacePixels = baseSpacePixels * complexityFactor / effectiveScale

        // 角度转弧度
        const angleRad = (angle * Math.PI) / 180
        const cos = Math.cos(angleRad)
        const sin = Math.sin(angleRad)

        // 计算对角线长度
        const diagonalLength = Math.sqrt(width * width + height * height) * 3.0

        // 设置线条属性
        ctx.strokeStyle = ctx.fillStyle
        ctx.lineWidth = isSelected ? 0.5 : 0.3

        // 计算线条数量
        const maxDistanceNeeded = diagonalLength * 1.5
        const linesPerSide = Math.ceil(maxDistanceNeeded / adjustedSpacePixels)
        const maxLinesPerSide = 600
        const actualLinesPerSide = Math.min(linesPerSide, maxLinesPerSide)

        // 调整间距
        const actualSpacingPixels = linesPerSide > maxLinesPerSide
          ? adjustedSpacePixels * (linesPerSide / maxLinesPerSide)
          : adjustedSpacePixels

        // 绘制线条
        for (let i = -actualLinesPerSide; i <= actualLinesPerSide; i++) {
          const offset = i * actualSpacingPixels
          const x1 = -diagonalLength * cos + offset * sin
          const y1 = -diagonalLength * sin - offset * cos
          const x2 = diagonalLength * cos + offset * sin
          const y2 = diagonalLength * sin - offset * cos

          ctx.beginPath()
          ctx.moveTo(x1, y1)
          ctx.lineTo(x2, y2)
          ctx.stroke()
        }

        // 恢复上下文
        ctx.restore()

        // 描边绘制（独立的上下文）
        if (this.stroke && this.strokeWidth && this.strokeWidth > 0) {
          ctx.save()

          // 根据对象类型安全地生成描边路径
          const isBuiltInShape = this.type === 'rect' || this.type === 'MyRect' ||
            this.type === 'ellipse' || this.type === 'circle' ||
            this.type === 'polygon' || this.type === 'Star' ||
            this.type === 'MyTriangle'

          if (this.type === 'Star' || this.type === 'polygon' || this.type === 'MyTriangle') {
            // 特殊处理星形和多边形：优先使用_renderPathCommands
            ctx.beginPath()
            if (typeof this._renderPathCommands === 'function') {
              this._renderPathCommands(ctx)
            } else {
              // 如果没有_renderPathCommands方法，尝试使用points属性
              if (this.points && Array.isArray(this.points) && this.points.length > 0) {
                const points = this.points
                ctx.moveTo(points[0].x, points[0].y)
                for (let i = 1; i < points.length; i++) {
                  ctx.lineTo(points[i].x, points[i].y)
                }
                ctx.closePath()
              } else {
                ctx.restore()
                return
              }
            }
          } else if (isBuiltInShape && typeof this._renderPathCommands === 'function') {
            // 对于我们添加了_renderPathCommands方法的形状，优先使用它
            ctx.beginPath()
            this._renderPathCommands(ctx)
          } else if (this.type === 'rect' || this.type === 'MyRect') {
            PathFillExtension.drawRectPath(ctx, this.width, this.height)
          } else if (this.type === 'ellipse' || this.type === 'circle') {
            PathFillExtension.drawEllipsePath(ctx, this.rx || this.width / 2, this.ry || this.height / 2)
          } else if (typeof this._renderPathCommands === 'function') {
            // 复杂SVG和常规SVG优先使用_renderPathCommands
            this._renderPathCommands(ctx)
          } else if (this.type === 'path' && typeof this.path === 'object' && Array.isArray(this.path)) {
            ctx.beginPath()
            const p = this.path
            if (p.length > 0) {
              ctx.moveTo(p[0][1], p[0][2])
              for (let i = 1; i < p.length; i++) {
                const cmd = p[i][0]
                if (cmd === 'L') {
                  ctx.lineTo(p[i][1], p[i][2])
                } else if (cmd === 'Q') {
                  ctx.quadraticCurveTo(p[i][1], p[i][2], p[i][3], p[i][4])
                } else if (cmd === 'C') {
                  ctx.bezierCurveTo(p[i][1], p[i][2], p[i][3], p[i][4], p[i][5], p[i][6])
                }
              }
              ctx.closePath()
            } else {
              ctx.restore()
              return
            }
          } else {
            ctx.restore()
            return
          }

          // 应用描边样式
          ctx.strokeStyle = this.stroke
          ctx.lineWidth = this.strokeWidth
          ctx.stroke()
          ctx.restore()
        }
      } catch (error) {
        console.error('路径填充渲染错误:', error)
        // 出错时恢复上下文并回退到原始填充
        ctx.restore()
        fabricOriginalRenderFill.call(this, ctx)
      }
    }

    // 监听对象添加事件，确保刚导入矢量图时路径填充也能实时更新
    canvas.on('object:added', e => {
      const obj = e.target
      if (!obj) return

      // 检查是否是矢量图且填充模式为路径填充
      if (obj.fill === VECTOR_FILL_MODES.PATH_FILL) {
        // 确保对象已准备好进行渲染
        if (obj._cacheCanvas) {
          obj.dirty = true;
          (obj as any).clearCache && (obj as any).clearCache()
        }

        // 如果是组，处理每个子对象
        if (obj instanceof fabric.Group) {
          obj._objects.forEach((o: fabric.FabricObject) => {
            if (o.fill === VECTOR_FILL_MODES.PATH_FILL && o._cacheCanvas) {
              o.dirty = true;
              (o as any).clearCache && (o as any).clearCache()
            }
          })
        }

        // 延迟一帧确保DOM更新后再渲染
        requestAnimationFrame(() => {
          canvas.requestRenderAll()
        })
      }
    })
  }

  // 直接绘制矩形路径
  static drawRectPath(ctx: CanvasRenderingContext2D, width: number, height: number) {
    ctx.beginPath()
    ctx.moveTo(-width / 2, -height / 2)
    ctx.lineTo(width / 2, -height / 2)
    ctx.lineTo(width / 2, height / 2)
    ctx.lineTo(-width / 2, height / 2)
    ctx.closePath()
  }

  // 直接绘制椭圆路径
  static drawEllipsePath(ctx: CanvasRenderingContext2D, rx: number, ry: number) {
    ctx.beginPath()
    ctx.ellipse(0, 0, rx, ry, 0, 0, 2 * Math.PI)
    ctx.closePath()
  }
}

export const VectorPlugin = createPlugin<VectorUtilities>(
  context => {
    const { canvas } = context
    function getSingleActiveObject(): fabric.FabricObject | null {
      const objects = canvas.getActiveObjects()

      if (objects.length !== 1) return null

      const type = getTypeOfFabricObject(objects[0])
      if (type === ObjectType.VECTOR || type === ObjectType.VECTOR_GROUP) {
        return objects[0]
      }

      return null
    }

    return {
      onInit() {
        PathFillExtension.init(context)
      },
      utilities: {
        fillVector(target) {
          target.set({
            strokeWidth: Math.max(target.width, target.height) * VECTOR_DEFAULT_STROKE_WIDTH_FACTOR,
            fill: VECTOR_FILL_MODES.FILL,
            stroke: '#000',
            _fill_space_: null,
            _fill_angle_: null,
          })
          if (target instanceof fabric.Group) {
            target._objects.forEach(o => {
              this.fillVector(o)
            })
          }

          canvas.requestRenderAll()
        },
        clearFillForVector(target) {
          target.set({
            strokeWidth: Math.max(target.width, target.height) * VECTOR_DEFAULT_STROKE_WIDTH_FACTOR,
            fill: VECTOR_FILL_MODES.NO_FILL,
            stroke: '#000',
            _fill_space_: null,
            _fill_angle_: null,
          })

          if (target instanceof fabric.Group) {
            target._objects.forEach(o => {
              this.clearFillForVector(o)
            })
          }
          canvas.requestRenderAll()
        },
        pathFillVector(target) {
          const initialSpace = 1.0  // 默认间距为1毫米

          // 保留原有的描边设置
          const originalStroke = target.stroke || '#000'

          target.set({
            fill: VECTOR_FILL_MODES.PATH_FILL,
            _fill_space_: initialSpace,
            _fill_angle_: VECTOR_NIN_FILL_ANGLE,
            stroke: originalStroke,
          })

          if (target instanceof fabric.Group) {
            target._objects.forEach(o => {
              // 为组内每个对象保留原有描边设置
              const objStroke = o.stroke || '#000'

              o.set({
                fill: VECTOR_FILL_MODES.PATH_FILL,
                _fill_space_: initialSpace,
                _fill_angle_: VECTOR_NIN_FILL_ANGLE,
                stroke: objStroke,
              })
            })
          }

          // 手动清除缓存，确保路径填充设置即时生效
          if (target._cacheCanvas) {
            target.dirty = true;
            (target as any).clearCache && (target as any).clearCache()
          }

          // 如果是组，也清除所有子对象的缓存
          if (target instanceof fabric.Group) {
            target._objects.forEach((o: fabric.FabricObject) => {
              if (o._cacheCanvas) {
                o.dirty = true;
                (o as any).clearCache && (o as any).clearCache()
              }
            })
          }

          // 请求渲染
          canvas.requestRenderAll()
        },
        fillSelectedVector() {
          const target = getSingleActiveObject()
          if (!target) return

          withAtomicOperation(target, () => {
            this.fillVector(target)
          })
          canvas.fire('object:modified', { target })
        },
        pathFillSelectedVector() {
          const target = getSingleActiveObject()
          if (!target) return

          withAtomicOperation(target, () => {
            this.pathFillVector(target)
          })
          canvas.fire('object:modified', { target })
        },
        clearFillForSelectedVector() {
          const target = getSingleActiveObject()
          if (!target) return

          withAtomicOperation(target, () => {
            this.clearFillForVector(target)
          })
          canvas.fire('object:modified', { target })
        },
        setFillSpace(val, commit = false) {
          const target = getSingleActiveObject()
          if (!target) return

          target.set({
            _fill_space_: val
          })

          if (target instanceof fabric.Group) {
            target._objects.forEach((o: fabric.FabricObject) => {
              o.set({
                _fill_space_: val
              })
            })
          }

          // 强制触发缓存更新以确保间距变化立即可见
          if (target) {
            // 清除缓存标志，强制下次渲染重新计算
            if (target._cacheCanvas) {
              target.dirty = true;
              (target as any).clearCache && (target as any).clearCache()
            }

            // 如果是组，也要清除所有子对象的缓存
            if (target instanceof fabric.Group) {
              target._objects.forEach((o: fabric.FabricObject) => {
                if (o._cacheCanvas) {
                  o.dirty = true;
                  (o as any).clearCache && (o as any).clearCache()
                }
              })
            }
          }

          if (commit) {
            canvas.fire('selection:updated')
            canvas.fire('object:modified', { target })
          }

          canvas.requestRenderAll()
        },
        setFillAngle(val: number, commit = false) {
          const target = getSingleActiveObject()
          if (!target) return

          target.set({
            _fill_angle_: val
          })

          if (target instanceof fabric.Group) {
            target._objects.forEach((o: fabric.FabricObject) => {
              o.set({
                _fill_angle_: val
              })
            })
          }

          // 强制触发缓存更新以确保角度变化立即可见
          if (target) {
            // 清除缓存标志，强制下次渲染重新计算
            if (target._cacheCanvas) {
              target.dirty = true
              ;(target as any).clearCache && (target as any).clearCache()
            }

            // 如果是组，也要清除所有子对象的缓存
            if (target instanceof fabric.Group) {
              target._objects.forEach((o: fabric.FabricObject) => {
                if (o._cacheCanvas) {
                  o.dirty = true
                  ;(o as any).clearCache && (o as any).clearCache()
                }
              })
            }
          }

          if (commit) {
            canvas.fire('selection:updated')
            canvas.fire('object:modified', { target })
          }
          canvas.requestRenderAll()
        },
      }
    }
  }
)
