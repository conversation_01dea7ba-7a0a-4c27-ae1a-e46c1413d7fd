import * as fabric from 'fabric'
import React, { ReactNode, useEffect, useRef } from 'react'
import { StatelessFabricCoreContext } from './types/base'
import { WORKSPACE_SIZE_PIXELS } from '@/libs/fabricjs-react/constants'

// @ts-ignore
import { TCanvasOptions } from 'fabric/dist/src/canvas/CanvasOptions'
import clsx from 'clsx'
import { PageHeaderHeight } from '@/constants/styles'

export interface Props {
  className?: string
  onReady?: (context: Pick<StatelessFabricCoreContext, 'width' | 'height' | 'canvas' | 'workspaceEl'>) => void
  canvasOption?: TCanvasOptions

  absoluteSlot?: ReactNode | undefined
}

export const FabricCanvas: React.FC<Props> = ({
  className, onReady, canvasOption, absoluteSlot
}) => {
  const canvasEl = useRef(null)
  const canvasParentEl = useRef<HTMLDivElement | null>(null)

  const initialized = useRef(false)

  useEffect(() => {
    if (initialized.current) return

    fabric.config.textureSize = 2048
    fabric.FabricObject.ownDefaults.borderColor = '#f18d00'

    const canvas = new fabric.Canvas(canvasEl.current!, canvasOption)
    canvas.skipOffscreen = false

    const setCurrentDimensions = () => {
      canvas
        .set({
          width: canvasParentEl.current?.clientWidth || 0,
          height: canvasParentEl.current?.clientHeight || 0
        })
        .renderAll()
    }

    const resizeCanvas = () => setCurrentDimensions()

    setCurrentDimensions()

    window.addEventListener('resize', resizeCanvas, false)

    onReady?.({ canvas, workspaceEl: canvasParentEl.current!, height: WORKSPACE_SIZE_PIXELS, width: WORKSPACE_SIZE_PIXELS })
    initialized.current = true

    return () => {
      initialized.current = false
      void canvas.dispose()
      window.removeEventListener('resize', resizeCanvas)
    }
  }, [])

  return (
    <div
      ref={canvasParentEl}
      id="workspace"
      className={clsx(className, 'relative')}
      style={{ height: `calc(100vh-${PageHeaderHeight}px)` }}
    >
      <canvas ref={canvasEl} className="w-screen" />

      {absoluteSlot}
    </div>
  )
}
