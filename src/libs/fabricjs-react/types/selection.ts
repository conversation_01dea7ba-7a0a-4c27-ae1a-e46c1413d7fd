import { ObjectType } from '@/libs/fabricjs-react/constants'
import * as fabric from 'fabric'

export type NonNullFabricSelection = { __updated_at__: number, __version__: number, isLocked: boolean } & (
  | { type: ObjectType.IMAGE, object: fabric.FabricImage }
  | { type: ObjectType.TEXT, object: fabric.FabricText }
  | { type: ObjectType.GROUP, object: fabric.Group }
  | { type: ObjectType.VECTOR_GROUP, object: fabric.Group }
  | { type: ObjectType.VECTOR, object: fabric.FabricObject }
  | { type: ObjectType.OBJECTS, object: fabric.ActiveSelection }
)

export type FabricSelection = NonNullFabricSelection | null
