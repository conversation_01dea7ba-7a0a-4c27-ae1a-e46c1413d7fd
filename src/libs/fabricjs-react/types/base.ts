import { Canvas, Rect } from 'fabric'
import { FabricEditorUtilities } from '../fabric-core'
import * as fabric from 'fabric'
import { TFunction } from 'i18next'

export type UtilitiesConstraint = Record<any, (...args: any[]) => any>

export type FabricPlugin<Utilities = NonNullable<UtilitiesConstraint>> = {
  utilities: Utilities,
  onInit?: () => void
}

export type FabricState = Partial<{
  workspace: Rect
  zoom: number
  isDrawingMode: boolean
  isDragging: boolean
  fontFamilies: string[]
  canvasCache: any
  activeObjectCache: fabric.FabricObject

  undoCount: number
  redoCount: number

  workspaceWidth: number
  workspaceHeight: number
}>

export type StatelessFabricCoreContext = {
  width: number
  height: number
  canvas: Canvas
  workspaceEl: HTMLDivElement
  t: TFunction
}

export type FabricCoreContext = StatelessFabricCoreContext & {
  state: FabricState
  getUtilities: () => FabricEditorUtilities
}

export type PluginBuilder<Utilities> = (context: FabricCoreContext) => FabricPlugin<Utilities>

export function createPlugin<Utilities>(props: PluginBuilder<Utilities>) {
  return props
}
