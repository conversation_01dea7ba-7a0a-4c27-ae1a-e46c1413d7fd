import * as fabric from 'fabric'
import { Canvas } from 'fabric'
import { ObjectType, POSITION_MAX_X, POSITION_MAX_Y, RULER_SIZE, WORKSPACE_BORDER_WIDTH } from '../constants'
import { <PERSON>uffer } from 'buffer'
import { FabricSelection } from '@/libs/fabricjs-react'
import { VectorText } from '@/libs/fabricjs-react/custom_objects'

type EventPositions = [fabric.Point | null, fabric.Point | null]

export function getEventPositions(e: MouseEvent | TouchEvent): EventPositions {
  if ('clientX' in e) {
    return [new fabric.Point(
      e.clientX,
      e.clientY,
    ), null]
  }

  const { touches } = e
  if (touches.length <= 2) {
    const pts: fabric.Point[] = []
    for (let i = 0; i < 2; i++) {
      const touch = touches.item(i)
      if (!touch) continue
      const { clientX, clientY } = touch

      pts.push(new fabric.Point(
        clientX,
        clientY,
      ))
    }

    return pts as EventPositions
  }

  return [null, null]
}

export function moveViewportTo(canvas: Canvas, x: number | ((x: number) => number), y: number | ((y: number) => number)) {
  const vpt = [...canvas.viewportTransform]

  const zoom = canvas.getZoom()
  const maxX = (POSITION_MAX_X + WORKSPACE_BORDER_WIDTH * 1.5) * zoom + RULER_SIZE /*+ WORKSPACE_BORDER_WIDTH*/
  const maxY = (POSITION_MAX_Y + WORKSPACE_BORDER_WIDTH * 1.5) * zoom + RULER_SIZE /*+ WORKSPACE_BORDER_WIDTH*/

  const targetX = typeof x === 'function' ? x(vpt[4]) : x
  const targetY = typeof y === 'function' ? y(vpt[5]) : y

  vpt[4] = Math.min(maxX, targetX)
  vpt[5] = Math.min(maxY, targetY)

  canvas.setViewportTransform(vpt as any)
  canvas.renderAll()

  return { x: targetX, y: targetY }
}

export function getTypeOfFabricObject(object: fabric.FabricObject) {
  if (object.isType(VectorText.type)) {
    return ObjectType.TEXT
  }

  if (object instanceof fabric.Group) {
    if (object._objects.every(item => !item.isType(fabric.FabricImage.type, fabric.FabricText.type))) {
      return ObjectType.VECTOR_GROUP
    }

    return ObjectType.GROUP
  }

  if (object instanceof fabric.FabricImage) {
    return ObjectType.IMAGE
  }

  return ObjectType.VECTOR
}

export function wrapWithSvgRoot(content: string, width: number, height: number) {
  return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}" width="${width}" height="${height}">\n${content}\n</svg>`
}

export function shouldExportAsBitmap(object: fabric.FabricObject) {
  return object instanceof fabric.FabricImage
}

export function getImageSize(image: fabric.FabricImage) {
  return Buffer.from(image.getSrc(), 'base64').length
}

export function withAtomicOperation<OperationReturnType extends (unknown | Promise<unknown>)>(
  target: fabric.FabricObject | fabric.FabricObject[], operation: () => OperationReturnType
): OperationReturnType {
  const _toggleLock = (target: fabric.FabricObject | fabric.FabricObject[], val: boolean) => {
    if (Array.isArray(target)) {
      target.forEach(item => _toggleLock(item, val))
    } else if (target instanceof fabric.Group) {
      target.__should_ignore_by_history__ = val
      target._objects.forEach(item => _toggleLock(item, val))
    } else {
      target.__should_ignore_by_history__ = val
    }
  }

  _toggleLock(target, true)
  const returnResult = operation()

  if (returnResult instanceof Promise) {
    // @ts-ignore
    return returnResult
      .then(result => {
        _toggleLock(target, false)
        return result
      })
  } else {
    _toggleLock(target, false)
    return returnResult
  }
}

export function shouldShowObjectsGroupOperations(selection?: FabricSelection) {
  return selection && [ObjectType.GROUP, ObjectType.OBJECTS, ObjectType.VECTOR_GROUP].includes(selection.type)
}

export function getValidObjects(canvas: fabric.Canvas) {
  return canvas.getObjects()
    .filter(o => !o.__is_system_object__)
    .filter(o => !(o instanceof fabric.Group) || o._objects.length > 0) // 当对象类型为 Group 时，要求其包含至少一个对象
    .filter(o => o.visible)
}

