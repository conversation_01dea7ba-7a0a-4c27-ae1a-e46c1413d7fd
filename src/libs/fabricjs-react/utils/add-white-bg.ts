import * as fabric from 'fabric'

export async function applyFilterWithWhiteBgIfNeeded(
  image: fabric.Image,
  filter: any
): Promise<void> {
  const width = image.width!
  const height = image.height!

  // Step 1: 检查是否含有透明区域
  const checkAlpha = (): boolean => {
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    const ctx = canvas.getContext('2d')!
    ctx.drawImage(image.getElement() as CanvasImageSource, 0, 0)
    const { data } = ctx.getImageData(0, 0, width, height)
    for (let i = 0; i < data.length; i += 4) {
      if (data[i + 3] < 250) return true
    }
    return false
  }

  const hasAlpha = checkAlpha()

  // 如果没有透明区域，直接加滤镜即可
  if (!hasAlpha) {
    image.filters.push(filter as any)
    image.applyFilters()
    return
  }

  // Step 2: 保存原始透明通道（用于后续还原透明区域）
  const originalEl = image.__originalElement || image._element
  const originalCanvas = document.createElement('canvas')
  originalCanvas.width = width
  originalCanvas.height = height
  const originalCtx = originalCanvas.getContext('2d')!
  originalCtx.drawImage(originalEl as CanvasImageSource, 0, 0)
  const originalAlphaData = originalCtx.getImageData(0, 0, width, height).data

  // Step 3: 创建带白底的临时图像
  const whiteBgCanvas = document.createElement('canvas')
  whiteBgCanvas.width = width
  whiteBgCanvas.height = height
  const whiteBgCtx = whiteBgCanvas.getContext('2d')!
  whiteBgCtx.fillStyle = '#ffffff'
  whiteBgCtx.fillRect(0, 0, width, height)
  whiteBgCtx.drawImage(originalEl as CanvasImageSource, 0, 0)

  // Step 4: 创建 fabric.Image，应用滤镜
  const whiteBgImage = new fabric.Image(whiteBgCanvas)
  image.filters.push(filter as any)
  whiteBgImage.applyFilters()

  // Step 5: 去除“原本透明区域中的白色”像素
  const resultCanvas = whiteBgImage.getElement() as HTMLCanvasElement
  const resultCtx = resultCanvas.getContext('2d')!
  const resultImageData = resultCtx.getImageData(0, 0, width, height)
  const resultData = resultImageData.data

  for (let i = 0; i < resultData.length; i += 4) {
    const origAlpha = originalAlphaData[i + 3]
    const isOriginallyTransparent = origAlpha < 250
    const isWhite =
        resultData[i] > 250 &&
        resultData[i + 1] > 250 &&
        resultData[i + 2] > 250

    if (isOriginallyTransparent && isWhite) {
      resultData[i + 3] = 0 // set alpha to transparent
    }
  }

  resultCtx.putImageData(resultImageData, 0, 0)

  // Step 6: 替换原图像的内容
  image.setElement(resultCanvas)

}