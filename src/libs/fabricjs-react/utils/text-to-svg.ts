import opentype, { Font } from 'opentype.js'

export type TextToSVGOptions = Partial<{
  x: number
  y: number
  fontSize: number
  letterSpacing: number
  tracking: number
  anchor: string
  attributes: any
}>

function parseAnchorOption(anchor: string) {
  const hMatches = anchor.match(/left|center|right/gi) || []
  const horizontal = hMatches.length === 0 ? 'left' : hMatches[0]

  const vMatches = anchor.match(/baseline|top|bottom|middle/gi) || []
  const vertical = vMatches.length === 0 ? 'baseline' : vMatches[0]

  return { horizontal, vertical }
}

function isCharAvailableInFont(font: opentype.Font, char: string): boolean {
  const glyph = font.charToGlyph(char)
  return glyph.name !== '.notdef'
}

export class TextToSvgConvertor {
  constructor(public font: Font, public fallbackFont: Font) {
  }

  public toVector(text: string, options: TextToSVGOptions = {}) {
    const attributes = Object.keys(options.attributes || {})
      .map(key => `${key}="${options.attributes[key]}"`)
      .join(' ')

    const { d, height, cursorX } = this._getD(text, options)

    const path = attributes
      ? `<path ${attributes} d="${d}"/>`
      : `<path d="${d}"/>`

    return {
      path,
      width: cursorX,
      height
    }
  }

  private _getHeight(fontSize: number) {
    const fontScale = 1 / this.font.unitsPerEm * fontSize
    return (this.font.ascender - this.font.descender) * fontScale
  }

  private _getD(text: string, options: TextToSVGOptions = {}) {
    const fontSize = options.fontSize || 72
    const kerning = 'kerning' in options ? options.kerning : true
    const letterSpacing = options.letterSpacing || 0
    const tracking = options.tracking || 0

    let cursorX = options.x || 0
    const y = options.y || 0
    const anchor = parseAnchorOption(options.anchor || '')

    const height = this._getHeight(fontSize)
    const ascender = this.font.ascender * (fontSize / this.font.unitsPerEm)

    let baselineY = y
    switch (anchor.vertical) {
      case 'baseline': baselineY = y; break
      case 'top':      baselineY = y + ascender; break
      case 'middle':   baselineY = y + ascender - height / 2; break
      case 'bottom':   baselineY = y + ascender - height; break
    }

    const paths: opentype.Path[] = []

    let prevGlyph: opentype.Glyph | null = null
    let prevFont: opentype.Font | null = null

    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      const fontToUse = isCharAvailableInFont(this.font, char) ? this.font : this.fallbackFont
      const fontScale = 1 / fontToUse.unitsPerEm * fontSize
      const glyph = fontToUse.charToGlyph(char)

      if (kerning && prevGlyph && prevFont === fontToUse) {
        const kerningValue = fontToUse.getKerningValue(prevGlyph, glyph)
        cursorX += kerningValue * fontScale
      }

      const path = fontToUse.getPath(char, cursorX, baselineY, fontSize, {
        kerning: false, letterSpacing, tracking
      })
      paths.push(path)
      if (glyph.advanceWidth) {
        const advanceWidth = glyph.advanceWidth * fontScale
        cursorX += advanceWidth
      }

      if (letterSpacing) {
        cursorX += letterSpacing * fontSize
      } else if (tracking) {
        cursorX += (tracking / 1000) * fontSize
      }

      prevGlyph = glyph
      prevFont = fontToUse
    }

    return {
      d: paths.map(path => path.toPathData(4)).join(' '),
      height,
      cursorX
    }
  }
}
