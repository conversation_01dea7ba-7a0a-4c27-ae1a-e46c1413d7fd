import * as fabric from 'fabric'
import {
  OBJECT_LOCKING_PROPERTIES,
  WORKSPACE_SIZE_PIXELS
} from '@/libs/fabricjs-react/constants'

export function isObjectLocked(target: fabric.FabricObject) {
  if (target instanceof fabric.ActiveSelection) {
    return target._objects.some(o => isObjectLocked(o))
  }

  return ['lockMovementX', 'lockMovementY', 'lockScalingY', 'lockScalingX'].every(prop => target[prop])
}

export function setObjectLocked(target: fabric.FabricObject, locked: boolean) {
  OBJECT_LOCKING_PROPERTIES.forEach(prop => {
    target[prop] = locked
  })

  if ('editable' in target) {
    target.editable = !locked
  }

  if ('erasable' in target) {
    target.erasable = !locked
  }
}

export function toggleObjectLocking(target: fabric.FabricObject) {
  const isLocked = isObjectLocked(target)
  return setObjectLocked(target, !isLocked)
}

export function compareObject(o1: fabric.FabricObject | undefined, o2: fabric.FabricObject | undefined, propertiesToMatch?: string[]) {
  if (!(o1 && o2)) return false

  propertiesToMatch ||= ['x', 'y', 'width', 'height', 'scaleX', 'scaleY']
  return !propertiesToMatch.some(property => o1[property] !== o2[property])
}

export function getObjectBounding(object: fabric.FabricObject, transform?: fabric.TMat2D) {
  const { aCoords: { tr, tl, br, bl } } = object

  const coords = [tr, tl, br, bl].map(c => transform ? c.transform(transform) : c)

  // console.log(coords)

  const left = Math.min(...coords.map(c => c.x))
  const top = Math.min(...coords.map(c => c.y))
  const right = Math.max(...coords.map(c => c.x))
  const bottom = Math.max(...coords.map(c => c.y))

  return {
    left,
    top,
    right,
    bottom,
    width: right - left,
    height: bottom - top,
  }
}

export function calcObjectsBounding(objects: fabric.FabricObject[], matrix?: fabric.TMat2D) {
  const bounds = objects.map(o => getObjectBounding(o, matrix))

  // 选区最大范围
  const left = Math.min(...bounds.map(b => b.left))
  const top = Math.min(...bounds.map(b => b.top))
  const right = Math.max(...bounds.map(b => b.right))
  const bottom = Math.max(...bounds.map(b => b.bottom))

  return {
    left,
    top,
    right,
    bottom,
    width: right - left,
    height: bottom - top,
  }
}

export function relocateObject(target: fabric.FabricObject, center: fabric.Point) {
  const w = target.getScaledWidth()
  const h = target.getScaledHeight()
  const originScale = target.scaleX
  const maxW = WORKSPACE_SIZE_PIXELS * 0.75
  const maxH = WORKSPACE_SIZE_PIXELS * 0.75
  const minW = WORKSPACE_SIZE_PIXELS * 0.25
  const minH = WORKSPACE_SIZE_PIXELS * 0.25

  if (w > maxW || h > maxH) {
    const scaleW = maxW / w
    const scaleH = maxH / h
    target.scale(originScale * Math.min(scaleW, scaleH))
  } else if (w < minW && h < minH) {
    const scaleW = minW / w
    const scaleH = minH / h
    target.scale(originScale * Math.max(scaleW, scaleH))
  }

  target.setXY(center, 'center', 'center')
}

export function convertToSystemObject<T extends fabric.FabricObject>(o: T) {
  return o
    .set('selectable', false)
    .set('hasControls', false)
    .set('evented', false)
    .set('hoverCursor', 'default')
    .set('__is_system_object__', true)
}

/**
 * 计算使对象边界不超出坐标轴第一象限的最小的 x 和 y
 */
export function findMinimumXAndY(target: fabric.FabricObject): { x: number, y: number } {
  target.setCoords()
  const { aCoords: { tl, tr, bl, br } } = target
  const coords = [tl, tr, bl, br]
  const minX = Math.min(...coords.map(c => c.x))
  const minY = Math.min(...coords.map(c => c.y))

  return {
    x: tl.x - minX,
    y: tl.y - minY
  }
}

export function getRotatedBoundingSize(target: fabric.FabricObject) {
  const _getDistance = (p1: fabric.Point, p2: fabric.Point) => {
    const { x: x1, y: y1 } = p1
    const { x: x2, y: y2 } = p2

    return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2))
  }

  const { aCoords: { tl, tr, bl } } = target

  return {
    width: _getDistance(tl, tr),
    height: _getDistance(tl, bl),
  }
}

export function extractObjects(...objs: fabric.FabricObject[]) {
  const result: fabric.FabricObject[] = []

  for (const obj of objs) {
    if ('_objects' in obj && Array.isArray(obj._objects)) {
      result.push(...obj._objects)
    } else {
      result.push(obj)
    }
  }

  return result
}
