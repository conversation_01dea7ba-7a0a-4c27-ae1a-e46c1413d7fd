import opentype from 'opentype.js'
import Font_NotoSansSCRegular from '@/assets/fonts/NotoSansSC-Regular.ttf'
import { LOCAL_STATIC_FONTS } from '@/libs/fabricjs-react/constants'
import localforage from 'localforage'
import { loadFontFormBase64 } from '@/utils/font'

export class FontResolver {

  public static fallbackFont: opentype.Font | null
  private static _pendingRequest: Promise<Response> | null

  _cachedFont: opentype.Font | null
  _cachedFontFamily: string | null

  get cachedFontFamily() {
    return this._cachedFontFamily
  }

  public async loadFont(fontFamily: string): Promise<[opentype.Font, opentype.Font]> {
    this._cachedFontFamily = fontFamily
    return [
      this._cachedFont = await this._loadFontByFamily(fontFamily),
      await FontResolver.loadFallbackFont()
    ]
  }

  public static async loadFallbackFont(): Promise<opentype.Font> {
    if (FontResolver.fallbackFont) {
      return FontResolver.fallbackFont
    }

    if (FontResolver._pendingRequest) {
      console.warn('Fallback Font is loading yet')
      return Promise.reject()
    }

    FontResolver._pendingRequest = new Promise(resolve => setTimeout(resolve, 1000)).then(() => fetch(Font_NotoSansSCRegular))

    return (FontResolver.fallbackFont = await FontResolver._pendingRequest
      .then(res => res.arrayBuffer())
      .then(buf => opentype.parse(buf))
      .finally(() => FontResolver._pendingRequest = null)) as opentype.Font
  }

  private async _loadFontByFamily(fontFamily: string): Promise<opentype.Font> {
    try {
      const isLocalFont = LOCAL_STATIC_FONTS.some(font => font.fontFamily === fontFamily)

      if (isLocalFont) {
        const localFont = LOCAL_STATIC_FONTS.find(font => font.fontFamily === fontFamily)
        if (localFont?.localUri) {
          const buffer = await fetch(localFont.localUri).then(r => r.arrayBuffer())
          return opentype.parse(buffer)
        }
      }

      const cachedFontData = await localforage.getItem<ArrayBuffer>(fontFamily)
      if (cachedFontData) {
        return opentype.parse(cachedFontData)
      }

      const targetFont = window.injected?.fonts?.[fontFamily]

      if (targetFont) {
        return loadFontFormBase64(targetFont)
      }

      return FontResolver.loadFallbackFont()
    } catch (e) {
      return FontResolver.loadFallbackFont()
    }
  }
}
