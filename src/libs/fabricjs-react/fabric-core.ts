import {
  <PERSON><PERSON>ricS<PERSON>,
  Plug<PERSON>B<PERSON>er,
  StatelessFabricCoreContext, UtilitiesConstraint
} from './types/base'
import { DragPlugin, DragUtilities } from './plugins/drag.plugin'
import { TextPlugin, TextUtilities } from './plugins/text.plugin'
import { ExportPlugin, ExportUtilities } from './plugins/export.plugin'
import { LayerPlugin, LayerUtilities } from './plugins/layer.plugin'
import { VectorPlugin, VectorUtilities } from './plugins/vector.plugin'
import { HistoryPlugin, HistoryUtilities } from './plugins/history.plugin'
import { WorkspacePlugin, WorkspaceUtilities } from './plugins/workspace.plugin'
import { ZoomPlugin } from './plugins/zoom.plugin'
import { RulerPlugin, RulerUtilities } from './plugins/ruler.plugin'
import { ControlPlugin } from './plugins/control.plugin'
import { InsertPlugin, InsertUtilities } from './plugins/insert.plugin'
import { ObjectPlugin, ObjectUtilities } from './plugins/object.plugin'
import { BitmapPlugin, BitmapUtilities } from './plugins/bitmap.plugin'
import { BrushPlugin, BrushUtilities } from '@/libs/fabricjs-react/plugins/brush.plugin'
import { AlignPlugin, AlignUtilities } from './plugins/align.plugin'

import * as fabric from 'fabric'

export class FabricEditorImpl {

  private readonly _state: FabricState
  private _utilities = {}

  private constructor(
    public context: StatelessFabricCoreContext,
    private stateChangeCallback: (v: FabricState) => void
  ) {
    this._state = this._createObservableState()
  }

  public get state() {
    return this._state
  }

  public get canvas() {
    return this.context.canvas
  }

  static createInstance(
    context: StatelessFabricCoreContext,
    stateChangeCallback: (v: FabricState) => void,
  ) {
    return new FabricEditorImpl(context, stateChangeCallback)
      .use(WorkspacePlugin)
      .use(HistoryPlugin)
      .use(ZoomPlugin)
      .use(DragPlugin)
      .use(RulerPlugin)
      .use(TextPlugin)
      .use(AlignPlugin)
      .use(ControlPlugin)
      .use(LayerPlugin)
      .use(VectorPlugin)
      .use(InsertPlugin)
      .use(ObjectPlugin)
      .use(BitmapPlugin)
      .use(BrushPlugin)
      .use(ExportPlugin) as unknown as FabricCore
  }

  use(pluginBuilder: PluginBuilder<any>) {
    const plugin = pluginBuilder({
      ...this.context,
      state: this._state,
      getUtilities: () => this._utilities as FabricEditorUtilities
    })

    this._utilities = {
      ...this._utilities,
      ...plugin.utilities
    }

    Object
      .entries(plugin.utilities as UtilitiesConstraint)
      .forEach(([key, value]) => {
        this[key] = value.bind(this)
      })

    if (plugin.onInit) {
      plugin.onInit()
    }

    return this
  }

  private _createObservableState() {
    const cb = this.stateChangeCallback

    return new Proxy<FabricState>({}, {
      get(target, prop): any {
        return Reflect.get(target, prop)
      },
      set(target: FabricState, prop: string | symbol, newValue: any): boolean {
        const r = Reflect.set(target, prop, newValue)

        /*if (r)*/
        cb(target)
        return r
      }
    })
  }
}


export interface FabricEditorUtilities
  extends WorkspaceUtilities,
  // FreeDrawUtilities,
  DragUtilities,
  TextUtilities,
  AlignUtilities,
  ExportUtilities,
  LayerUtilities,
  VectorUtilities,
  HistoryUtilities,
  InsertUtilities,
  ObjectUtilities,
  BitmapUtilities,
  BrushUtilities,
  RulerUtilities {

}

export interface FabricCore extends FabricEditorUtilities {
  get state(): FabricState
  get canvas(): fabric.Canvas
  use(builder: PluginBuilder<any>): this
}

export const EmptyFabricCore = new Proxy({}, {
  get(target: any, prop: string) {
    return (...args: any[]) => {
      throw new Error(`\`FabricCore\` is not initialized yet. (Calling ${prop})`)
    }
  }
}) as FabricCore
