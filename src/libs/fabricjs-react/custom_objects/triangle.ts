import * as fabric from 'fabric'
import { ObjectEvents, TClassProperties } from 'fabric'

type UniqueCustomTriangleProps = {
  width: number
  height: number
}

type SerializedCustomTriangleProps = fabric.SerializedObjectProps & UniqueCustomTriangleProps

type CustomTriangleProps = fabric.FabricObjectProps & UniqueCustomTriangleProps

export class CustomTriangle<
  Props extends fabric.TOptions<CustomTriangleProps> = Partial<CustomTriangleProps>,
  SP extends SerializedCustomTriangleProps = SerializedCustomTriangleProps,
  ES extends ObjectEvents = ObjectEvents
> extends fabric.FabricObject<Props, SP, ES> {

  declare width: number
  declare height: number

  erasable = true
  static type = 'CustomTriangle'

  static ownDefaults = {
    width: 0,
    height: 0
  }

  static getDefaults() {
    return {
      ...super.getDefaults(), ...CustomTriangle.ownDefaults
    }
  }

  constructor(props: Props) {
    super()
    Object.assign(this, CustomTriangle.ownDefaults)
    super.setOptions(props)
  }

  _render(ctx: CanvasRenderingContext2D) {
    ctx.beginPath()
    ctx.moveTo(
      0,
      -this.height / 2 + this.strokeWidth / 2
    )
    ctx.lineTo(
      -this.width / 2 + this.strokeWidth / 2,
      +this.height / 2
    )
    ctx.lineTo(
      +this.width / 2 - this.strokeWidth / 2,
      +this.height / 2
    )
    ctx.closePath()

    ctx.restore()
    this._renderPaintInOrder(ctx)
  }

  _renderPathCommands(ctx: CanvasRenderingContext2D) {
    ctx.moveTo(0, -this.height / 2 + this.strokeWidth / 2)
    ctx.lineTo(-this.width / 2 + this.strokeWidth / 2, +this.height / 2)
    ctx.lineTo(+this.width / 2 - this.strokeWidth / 2, +this.height / 2)
    ctx.closePath()
  }

  toObject<
    T extends Omit<Props & TClassProperties<this>, keyof SP>,
    K extends keyof T = never
  >(propertiesToInclude: K[] = []): Pick<T, K> & SP {
    return super.toObject(['width', 'height', ...propertiesToInclude])
  }

  _toSVG(): string[] {
    const points = [
      [ 0, -this.height / 2 + this.strokeWidth / 2 ],
      [ -this.width / 2 + this.strokeWidth / 2, +this.height / 2 ],
      [ +this.width / 2 - this.strokeWidth / 2, +this.height / 2 ],
    ].map(([x, y]) => `${x} ${y}`).join(',')

    return [
      '<polygon ',
      'COMMON_PARTS',
      'points="',
      points,
      '" />'
    ]
  }
}

fabric.classRegistry.setClass(CustomTriangle)
fabric.classRegistry.setSVGClass(CustomTriangle)
