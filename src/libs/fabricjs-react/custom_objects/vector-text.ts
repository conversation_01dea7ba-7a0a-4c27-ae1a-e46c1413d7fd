import * as fabric from 'fabric'
import { TEXT_FILL_STYLES, VECTOR_FILL_MODES } from '@/libs/fabricjs-react/constants'
import { TextToSvgConvertor } from '@/libs/fabricjs-react/utils/text-to-svg'
import { wrapWithSvgRoot } from '@/libs/fabricjs-react/utils'
import { pick } from 'lodash'
import { FontResolver } from '@/libs/fabricjs-react/utils/font-resolver'

type CacheIdentity = Pick<
  fabric.TextProps,
  | 'fontSize'
  | 'fontStyle'
  | 'fill'
  | 'fontFamily'
  | 'fontWeight'
  | 'strokeWidth'
  | 'textAlign'
> & { text: string }

export class VectorText<
  Props extends fabric.TOptions<fabric.TextProps> = Partial<fabric.TextProps>,
  SProps extends fabric.SerializedTextProps = fabric.SerializedTextProps,
  EventSpec extends fabric.ObjectEvents = fabric.ObjectEvents
> extends fabric.FabricText<Props, SProps, EventSpec> {

  static type = 'VectorText'

  declare preview: boolean

  _cachedIdentity: CacheIdentity | null
  _cachedVector: fabric.Group | null
  _cachedConvertor: TextToSvgConvertor | null

  private static readonly IDENTITY_FIELDS: (keyof CacheIdentity)[] = [
    'text',
    'fontSize',
    'fontStyle',
    'fill',
    'fontWeight',
    'fontFamily',
    'strokeWidth',
    'textAlign',
  ]

  private readonly _fontResolver = new FontResolver()

  override _render(ctx: CanvasRenderingContext2D) {
    if (this.preview) return super._render(ctx)

    const currentCacheIdentity = this.#calcCacheIdentity()
    const shouldUseCache = this.#compareCacheIdentity(this._cachedIdentity, currentCacheIdentity)

    if (this._cachedVector && shouldUseCache) {
      return this.#renderWithCtx(ctx, this._cachedVector)
    }

    this.#convertTextToPath()
      .then(group => {
        this.#renderWithCtx(ctx, group)
        this._cachedVector = group
        this._cachedIdentity = currentCacheIdentity
        this.canvas?.requestRenderAll()
      })
  }

  public async toVectorGroup(withTransform = false): Promise<fabric.Group> {
    return this.#convertTextToPath(withTransform)
  }

  public async toSVGAsync(reviver?: (markup: string[]) => void): Promise<string> {
    const group = this._cachedVector || await this.#convertTextToPath()
    const clone = await group.clone()
    clone.remove(...clone._objects.filter(o => o.isType(fabric.Rect.type)))
    const decorationSVG = clone.toSVG()
    const transform = this.getSvgTransform()

    const markup = [
      `<g ${transform}>`,
      decorationSVG,
      '</g>',
    ]

    if (reviver) reviver(markup)
    return markup.join('\n')
  }

  #calcCacheIdentity(): CacheIdentity {
    return pick(this, VectorText.IDENTITY_FIELDS)
  }

  #compareCacheIdentity(a: CacheIdentity | null, b: CacheIdentity | null) {
    if (!a || !b) return false

    for (const field of VectorText.IDENTITY_FIELDS) {
      if (a[field] !== b[field]) {
        return false
      }
    }

    return true
  }

  async #loadConvertor(): Promise<TextToSvgConvertor> {
    if (this._fontResolver.cachedFontFamily === this.fontFamily && this._cachedConvertor) {
      return this._cachedConvertor
    }

    const [targetFont, fallbackFont] = await this._fontResolver.loadFont(this.fontFamily)
    return this._cachedConvertor = new TextToSvgConvertor(targetFont, fallbackFont)
  }

  async #convertTextToPath(withTransform = false) {
    const {
      textLines, fontSize, fill, fontWeight,
      fontStyle, textAlign, width: originalWidth
    } = this

    const strokeWidthFactor = (fontWeight === 'bold' ? 1.5 : 1) * Math.min(1, fontSize / 120)
    const isFillMode = fill === TEXT_FILL_STYLES.fill

    const convertor = await this.#loadConvertor()

    const vectors = textLines.map(
      text => convertor.toVector(text, {
        fontSize,
        x: 0,
        y: 0,
        anchor: 'left top',
      })
    )

    const textLineVectors: fabric.FabricObject[] = []
    for (let index = 0; index < vectors.length; index++) {
      const { path, height } = vectors[index]
      const [textObject, rectObject] = await fabric
        .loadSVGFromString(
          wrapWithSvgRoot(
            path + `\n<rect width="${originalWidth}" height="${height}" fill="#00000000" />`,
            originalWidth,
            height
          )
        )
        .then(output => output.objects.filter((o): o is fabric.FabricObject => Boolean(o)))

      const leftOffset = textAlign === 'left'
        ? 0
        : textAlign === 'right'
          ? originalWidth - textObject.width
          : (originalWidth - textObject.width) / 2

      const topOffset = 0
      const strokeWidth = 4 * strokeWidthFactor

      textObject.set(({
        fill: isFillMode ? VECTOR_FILL_MODES.FILL : VECTOR_FILL_MODES.NO_FILL,
        strokeWidth,
        stroke: 'black',
        // stroke: import.meta.env.DEV ? 'red' : 'black',
        left: leftOffset,
        top: this.height / vectors.length * index + topOffset,
        skewX: fontStyle === 'italic' ? -15 : 0,
        strokeUniform: true,
        objectCaching: false
      }))

      textLineVectors.push(textObject)
      textLineVectors.push(rectObject)
    }

    if (withTransform) {
      const filteredVectors = textLineVectors.filter(item => !(item instanceof fabric.Rect))
      //有位置信息
      const textGroup = new fabric.Group(filteredVectors, {
        left: this.left,
        top: this.top,
        originX: this.originX,
        originY: this.originY,
        angle: this.angle,
        scaleX: this.scaleX,
        scaleY: this.scaleY
      })
      // 仅在 withTransform 为 true 的情况下添加 isVectorTextGroup 标识
      ;(textGroup as any).isVectorTextGroup = true
      textGroup.toObject = (function (toObject) {
        return function (...args) {
          return {
            ...toObject.call(this, ...args),
            isVectorTextGroup: true,
          }
        }
      })(textGroup.toObject)
      return textGroup
    } else {
      //没位置信息
      return new fabric.Group(textLineVectors, {
        left: 0,
        top: 0,
        originX: 'center',
        originY: 'center',
        selectable: false,
        evented: false,
      })
    }
  }

  #renderWithCtx(ctx: CanvasRenderingContext2D, el: fabric.FabricObject) {
    el.canvas = this.canvas
    ctx.save()
    this.canvas?._renderObjects(ctx, [el])
    ctx.restore()
  }
}

fabric.classRegistry.setClass(VectorText)
fabric.classRegistry.setSVGClass(VectorText)
