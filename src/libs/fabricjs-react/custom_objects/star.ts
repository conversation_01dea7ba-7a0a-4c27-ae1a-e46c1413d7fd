import * as fabric from 'fabric'
import { ObjectEvents, TClassProperties } from 'fabric'

type UniqueStarProps = {
  size: number
  angles: number
}

type SerializedStarProps = fabric.SerializedObjectProps & UniqueStarProps

type StarProps = fabric.FabricObjectProps & UniqueStarProps

export class Star<
  Props extends fabric.TOptions<StarProps> = Partial<StarProps>,
  SProps extends SerializedStarProps = SerializedStarProps,
  EventSpec extends ObjectEvents = ObjectEvents
> extends fabric.FabricObject<Props, SProps, EventSpec>
  implements StarProps {
  declare size: number
  declare angles: number

  erasable = true

  static type = 'Star'

  static ownDefaults = {
    size: 100,
    angles: 5
  } as Partial<TClassProperties<Star>>

  static getDefaults() {
    return { ...super.getDefaults(), ...Star.ownDefaults }
  }

  constructor(options?: Props) {
    super()
    Object.assign(this, Star.ownDefaults)
    super.setOptions(options)
  }

  _render(ctx: CanvasRenderingContext2D) {
    ctx.beginPath()
    for (let i = 0; i < this.angles; i++) {
      const outerPoint = this.outerPointXY(i)
      const innerPoint = this.innerPointXY(i)
      ctx.lineTo(outerPoint.x, outerPoint.y)
      ctx.lineTo(innerPoint.x, innerPoint.y)
    }
    ctx.closePath()

    ctx.restore()
    this._renderPaintInOrder(ctx)
  }

  _set(key: string, value: any) {
    super._set(key, value)
    if (key === 'size') {
      this.size = value
      this.set({ width: value, height: value })
    }

    return this
  }

  _toSVG() {
    const points = new Array(this.angles)
      .fill(undefined)
      .map((_, i) => {
        const { x: ox, y: oy } = this.outerPointXY(i)
        const { x: ix, y: iy } = this.innerPointXY(i)

        return `${ox} ${oy},${ix} ${iy}`
      })
      .join(',')

    return [
      '<polygon ',
      'COMMON_PARTS',
      'points="',
      points,
      '" />'
    ]
  }

  toObject<
    T extends Omit<Props & TClassProperties<this>, keyof SProps>,
    K extends keyof T = never
  >(propertiesToInclude: K[] = []): Pick<T, K> & SProps {
    return super.toObject(['size', 'angle', ...propertiesToInclude])
  }

  private outerPointXY(index: number) {
    const degreePerAngle = 360 / this.angles
    const R = (this.size - this.strokeWidth * 1.2) / 2

    return {
      x: Math.cos((degreePerAngle / 4 + index * degreePerAngle) / 180 * Math.PI) * R,
      y: -Math.sin((degreePerAngle / 4 + index * degreePerAngle) / 180 * Math.PI) * R
    }
  }

  private innerPointXY(index: number) {
    const degreePerAngle = 360 / this.angles
    const r = (this.size - this.strokeWidth * 1.2) / 2 * 0.4

    return {
      x: Math.cos((degreePerAngle * 3 / 4 + index * degreePerAngle) / 180 * Math.PI) * r,
      y: -Math.sin((degreePerAngle * 3 / 4 + index * degreePerAngle) / 180 * Math.PI) * r
    }
  }

  _renderPathCommands(ctx: CanvasRenderingContext2D) {
    for (let i = 0; i < this.angles; i++) {
      const outerPoint = this.outerPointXY(i)
      const innerPoint = this.innerPointXY(i)
      ctx.lineTo(outerPoint.x, outerPoint.y)
      ctx.lineTo(innerPoint.x, innerPoint.y)
    }
    ctx.closePath()
  }
}

fabric.classRegistry.setClass(Star)
fabric.classRegistry.setSVGClass(Star)
