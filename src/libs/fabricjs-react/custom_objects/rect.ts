import * as fabric from 'fabric'
import { ObjectEvents, TClassProperties } from 'fabric'

type UniqueMyRectProps = {
  width: number
  height: number
}

type SerializedMyRectProps = fabric.SerializedObjectProps & UniqueMyRectProps

type MyRectProps = fabric.FabricObjectProps & UniqueMyRectProps

export class CustomRect<
  Props extends fabric.TOptions<MyRectProps> = Partial<MyRectProps>,
  SP extends SerializedMyRectProps = SerializedMyRectProps,
  ES extends ObjectEvents = ObjectEvents
> extends fabric.FabricObject<Props, SP, ES> {

  declare width: number
  declare height: number

  erasable = true
  static type = 'MyRect'

  static ownDefaults = {
    width: 0,
    height: 0
  }

  static getDefaults() {
    return {
      ...super.getDefaults(), ...CustomRect.ownDefaults
    }
  }

  constructor(props: Props) {
    super()
    Object.assign(this, CustomRect.ownDefaults)
    super.setOptions(props)
  }

  _render(ctx: CanvasRenderingContext2D) {
    ctx.beginPath()
    ctx.moveTo(
      -this.width / 2,
      -this.height / 2
    )
    ctx.lineTo(
      this.width / 2,
      -this.height / 2
    )
    ctx.lineTo(
      this.width / 2,
      this.height / 2
    )
    ctx.lineTo(
      -this.width / 2,
      this.height / 2
    )
    ctx.closePath()

    ctx.restore()
    this._renderPaintInOrder(ctx)
  }

  _renderPathCommands(ctx: CanvasRenderingContext2D) {
    ctx.moveTo(-this.width / 2, -this.height / 2)
    ctx.lineTo(this.width / 2, -this.height / 2)
    ctx.lineTo(this.width / 2, this.height / 2)
    ctx.lineTo(-this.width / 2, this.height / 2)
    ctx.closePath()
  }

  toObject<
    T extends Omit<Props & TClassProperties<this>, keyof SP>,
    K extends keyof T = never
  >(propertiesToInclude: K[] = []): Pick<T, K> & SP {
    return super.toObject(['width', 'height', ...propertiesToInclude])
  }

  _toSVG() {
    const { width, height } = this
    return [
      '<rect ',
      'COMMON_PARTS',
      `x="${-width / 2}" y="${
        -height / 2
      }" width="${width}" height="${height}" />\n`,
    ]
  }
}

fabric.classRegistry.setClass(CustomRect)
fabric.classRegistry.setSVGClass(CustomRect)
