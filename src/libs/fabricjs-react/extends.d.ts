import * as fabric from 'fabric'
import { ImageFilters } from '@/libs/fabricjs-react/types/filters'
import { WorkMode } from '@/libs/shared/types'
import { ImageSource } from 'fabric/dist/src/shapes/Image'

interface VectorFillParameters {
  _fill_angle_: number | null
  _fill_space_: number | null
}

declare module 'fabric' {

  type FabricObjectExtension = {
    __is_system_object__: boolean
    __should_ignore_by_history__: boolean
  }

  export interface CustomEvents {
    'history:undo': { objects: number }
    'history:redo': { objects: number }
    'history:append': { json: string }
    'workspace:initialized': void
    'eraser:disabled': void
    'eraser:enabled': void
    'before:eraser:enabled': void
    'workspace:changed': { workMode: WorkMode; diameter: number }
    'workspace:length:changed': { workMode: WorkMode; length: number }
  }

  export interface CanvasEvents extends /*fabric.CanvasEvents,*/ CustomEvents {
  }

  export interface FabricObject<
    Props extends TOptions<any> = Partial<any>,
    SProps = any,
    EventSpec extends ObjectEvents = ObjectEvents
  > extends fabric.FabricObject, VectorFillParameters, FabricObjectExtension {
    _renderPathFill(ctx: CanvasRenderingContext2D): void;
  }

  export type Object = FabricObject

  export interface Group extends fabric.Group, VectorFillParameters, FabricObjectExtension {
    _origin_text_?: fabric.FabricText,
    isVectorTextGroup?: boolean
  }

  export interface FabricObject  {
    isVectorTextGroup?: boolean;
  }
  export interface FabricImage extends fabric.FabricImage, FabricObjectExtension {
    // erasable: boolean
    __filter_type__: ImageFilters | null,
    __originalElement?: HTMLImageElement | HTMLCanvasElement | null | ImageSource
  }
}
