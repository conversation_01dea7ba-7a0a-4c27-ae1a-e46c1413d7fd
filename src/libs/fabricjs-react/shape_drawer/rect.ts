import { <PERSON>hape<PERSON>rawer } from './abstract'
import * as fabric from 'fabric'
import { TPointerEventInfo } from 'fabric'
import { CustomRect } from '@/libs/fabricjs-react/custom_objects'

export class RectDrawer extends ShapeDrawer {

  constructor(canvas: fabric.Canvas, private square?: boolean) {
    super(canvas)
  }

  onMouseDown(e: fabric.TPointerEventInfo) {
    super.onMouseDown(e)

    this._target = new CustomRect({
      left: this._starPoint.x,
      top: this._starPoint.y,
      width: 0,
      height: 0,
      fill: 'rgba(0,0,0,0)',
      stroke: ShapeDrawer.defaultConfig.strokeColor,
      strokeWidth: this._brushSize,
      erasable: true,
    })

    this._canvas._objects.push(this._target)
    this._canvas.requestRenderAll()
  }

  onMouseMove(e: TPointerEventInfo): void {
    const currentPoint = e.scenePoint

    const width = Math.abs(this._starPoint.x - currentPoint.x)
    const height = Math.abs(this._starPoint.y - currentPoint.y)
    const size = Math.min(width, height)

    let left = Math.min(this._starPoint.x, currentPoint.x)
    if (this.square) {
      left = Math.max(
        left,
        this._starPoint.x - size,
      )
    }

    let top = Math.min(this._starPoint.y, currentPoint.y)
    if (this.square) {
      top = Math.max(
        top,
        this._starPoint.y - size,
      )
    }

    this._target.set({
      left,
      top,
      width: this.square ? size : width,
      height: this.square ? size : height,
    })

    this._canvas.requestRenderAll()
  }
}

