import { <PERSON>hape<PERSON>rawer } from './abstract'
import { <PERSON><PERSON>, TPointerEventInfo } from 'fabric'
import { Star } from '@/libs/fabricjs-react/custom_objects'

export class StarDrawer extends ShapeDrawer {
  public constructor(canvas: Canvas) {
    super(canvas)
  }

  onMouseDown(e: TPointerEventInfo) {
    super.onMouseDown(e)

    this._target = new Star({
      left: this._starPoint.x,
      top: this._starPoint.y,
      fill: 'rgba(0,0,0,0)',
      stroke: ShapeDrawer.defaultConfig.strokeColor,
      strokeWidth: this._brushSize,
      size: 0,
    })

    this._canvas._objects.push(this._target)
    this._canvas.requestRenderAll()
  }

  onMouseMove(e: TPointerEventInfo): void {
    const currentPoint = e.scenePoint

    const width = Math.abs(this._starPoint.x - currentPoint.x)
    const height = Math.abs(this._starPoint.y - currentPoint.y)
    const size = Math.min(width, height)

    const left = Math.max(
      Math.min(this._starPoint.x, currentPoint.x),
      this._starPoint.x - size,
    )
    const top = Math.max(
      Math.min(this._starPoint.y, currentPoint.y),
      this._starPoint.y - size,
    )

    this._target.set({
      left,
      top,
      size
    })

    this._canvas.requestRenderAll()
  }
}
