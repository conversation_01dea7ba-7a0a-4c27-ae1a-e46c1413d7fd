import { <PERSON>hape<PERSON>rawer } from './abstract'
import { <PERSON>vas, TPointerEventInfo } from 'fabric'
import * as fabric from 'fabric'

export class CircleDrawer extends ShapeDrawer {

  public constructor(canvas: Canvas) {
    super(canvas)
  }

  onMouseDown(e: TPointerEventInfo) {
    super.onMouseDown(e)

    this._target = new fabric.Ellipse({
      left: this._starPoint.x,
      top: this._starPoint.y,
      fill: 'rgba(0,0,0,0)',
      stroke: ShapeDrawer.defaultConfig.strokeColor,
      strokeWidth: this._brushSize,
      erasable: true,
    })

    this._canvas._objects.push(this._target)
    this._canvas.requestRenderAll()
  }

  onMouseMove(e: TPointerEventInfo): void {
    const currentPoint = e.scenePoint

    const left = Math.min(this._starPoint.x, currentPoint.x)
    const top = Math.min(this._starPoint.y, currentPoint.y)
    const width = Math.abs(this._starPoint.x - currentPoint.x)
    const height = Math.abs(this._starPoint.y - currentPoint.y)

    this._target.set({
      left,
      top,
      rx: width / 2,
      ry: height / 2
    })

    this._canvas.requestRenderAll()
  }
}

if (fabric.Ellipse) {
  (fabric.Ellipse.prototype as any)._renderPathCommands = function(ctx) {
    ctx.ellipse(0, 0, this.rx, this.ry, 0, 0, 2 * Math.PI)
    ctx.closePath()
  }
}
