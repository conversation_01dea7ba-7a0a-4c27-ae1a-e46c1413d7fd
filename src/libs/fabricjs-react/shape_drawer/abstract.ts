import * as fabric from 'fabric'

export abstract class ShapeDrawer {

  public static defaultConfig = {
    brushSize: 4,
    strokeColor: 'rgba(0,0,0,1)'
  }

  protected _starPoint: fabric.Point
  protected _target: fabric.FabricObject
  protected _canvas: fabric.Canvas
  protected _brushSize: number = ShapeDrawer.defaultConfig.brushSize

  private _handles: Record<string, any> = {
    down: () => void 0,
    move: () => void 0,
    up: () => void 0,
  }

  protected constructor(canvas: fabric.Canvas) {
    this._canvas = canvas
    this._handles = {
      down: this.onMouseDown.bind(this),
      move: this.onMouseMove?.bind(this),
      up: this.onMouseUp?.bind(this),
    }

    this._canvas.on('mouse:down', this._handles.down)
    this._canvas.on('mouse:move', this._handles.move)
    this._canvas.on('mouse:up', this._handles.up)
  }

  public setBrushSize(size: number) {
    this._brushSize = size
  }

  protected onMouseDown(e: fabric.TPointerEventInfo) {
    this._starPoint = e.scenePoint
  }

  abstract onMouseMove?(e: fabric.TPointerEventInfo): void

  onMouseUp?(e: fabric.TPointerEventInfo) {
    if (this._target) {
      this._canvas._objects.pop()
      this._canvas.add(this._target)
    }
  }

  public dispose() {
    this._canvas.off('mouse:down', this._handles.down)
    this._canvas.off('mouse:move', this._handles.move)
    this._canvas.off('mouse:up', this._handles.up)
  }
}
