import { <PERSON><PERSON>pe<PERSON>rawer } from './abstract'
import { <PERSON><PERSON>, TPointerEventInfo } from 'fabric'
import { CustomTriangle } from '@/libs/fabricjs-react/custom_objects'

export class TriangleDrawer extends ShapeDrawer {

  public constructor(canvas: Canvas) {
    super(canvas)
  }

  onMouseDown(e: TPointerEventInfo) {
    super.onMouseDown(e)

    this._target = new CustomTriangle({
      left: this._starPoint.x,
      top: this._starPoint.y,
      width: 0,
      height: 0,
      fill: 'rgba(0,0,0,0)',
      stroke: ShapeDrawer.defaultConfig.strokeColor,
      strokeWidth: this._brushSize,
      erasable: true,
    })

    this._canvas._objects.push(this._target)
    this._canvas.requestRenderAll()
  }

  onMouseMove(e: TPointerEventInfo): void {
    const currentPoint = e.scenePoint

    const left = Math.min(this._starPoint.x, currentPoint.x)
    const top = Math.min(this._starPoint.y, currentPoint.y)
    const width = Math.abs(this._starPoint.x - currentPoint.x)
    const height = Math.abs(this._starPoint.y - currentPoint.y)

    this._target.set({
      left,
      top,
      width,
      height
    })

    this._canvas.requestRenderAll()
  }
}

