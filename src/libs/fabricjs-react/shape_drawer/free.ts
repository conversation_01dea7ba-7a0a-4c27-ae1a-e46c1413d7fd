import { ShapeDrawer } from './abstract'
import * as fabric from 'fabric'
import { EraserBrush } from '@erase2d/fabric'

// @ts-ignore
export class FreeDrawer extends ShapeDrawer {

  constructor(canvas: fabric.Canvas) {
    super(canvas)

    const brush = new fabric.PencilBrush(canvas)
    brush.color = ShapeDrawer.defaultConfig.strokeColor

    canvas.freeDrawingBrush = brush
    canvas.on('path:created', this.onPathCreated.bind(this))
  }

  setBrushSize(size: number) {
    super.setBrushSize(size)

    if (this._canvas.freeDrawingBrush) {
      this._canvas.freeDrawingBrush.width = size
    }
  }

  dispose() {
    if (
      this._canvas.freeDrawingBrush instanceof fabric.PencilBrush
      && !(this._canvas.freeDrawingBrush instanceof EraserBrush)
    ) {
      this._canvas.freeDrawingBrush = undefined
    }
    this._canvas.off('path:created', this.onPathCreated)
  }

  private onPathCreated(e) {
    e.path.set({ erasable: true })
    this._canvas.requestRenderAll()
  }
}
