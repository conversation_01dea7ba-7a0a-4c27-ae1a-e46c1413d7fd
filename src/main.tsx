import ReactDOM from 'react-dom/client'
import App from './App'
import './App.css'
import { ThemeProvider } from '@mui/material'
import theme from './theme'
import { CanvasMenuProvider } from './contexts/canvas-menu'
// import VConsole from 'vconsole'
import { createBrowserRouter, Outlet, RouterProvider } from 'react-router-dom'
import { I18nProvider, LanguageCodes } from '@/i18n'
import { ErrorBoundary } from 'react-error-boundary'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import clsx from 'clsx'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'
import { GeneralOperations } from '@/components/operations'
import localforage from 'localforage'

localforage.config({
  driver: localforage.INDEXEDDB,
  name: 'react-fabric-editor',
})

if (location.search.includes('v_console=1')) {
  import('vconsole').then(({ default: VConsole }) => {
    new VConsole()
  })
}

function ErrorFallback({ error, resetErrorBoundary }) {
  const { locale } = useURLSearchConfig()
  const { t } = useTranslation()

  const map = {
    [LanguageCodes['en-US']]: ['Oops! We are sorry that Materials Creative Engine got some problems.',  'You can click the button below to exit the page and re-enter.'],
    [LanguageCodes['zh-CN']]: ['非常抱歉，素材创作引擎发生故障。', '您可以点击下方的按钮退出页面后重新进入'],
  }
  const messages: string[] = map[locale] || map['en-US']

  return (
    <div className="px-6 w-full h-full bg-white text-gray-500 text-sm font-semibold">
      <div className="w-full h-full flex-col flex justify-center items-center">

        <div className="flex flex-col gap-4 mb-12">
          {messages.map((message, i) => (<div key={i}>{message}</div>))}
        </div>

        <button
          className={clsx('border border-primary rounded-full text-primary w-full py-3')}
          onClick={() => sendMessageToNative(MessageAction.GO_BACK_HOME)}
        >
          {t('common.quit')}
        </button>
      </div>

    </div>
  )
}

const router = createBrowserRouter([
  {
    element: (
      <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => void 0}>
        <Outlet />
      </ErrorBoundary>
    ),
    children: [
      {
        path: '/painting',
        element: <GeneralOperations.Painting />
      },
      {
        path: '*',
        element: <App />
      }
    ]
  }
], { basename: `/fabric/${import.meta.env.VITE_APP_VERSION}` })

ReactDOM.createRoot(document.getElementById('root')!).render(
  <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => void 0}>
    <I18nProvider>
      <ThemeProvider theme={theme}>
        <CanvasMenuProvider>
          <RouterProvider router={router} />
        </CanvasMenuProvider>
      </ThemeProvider>
    </I18nProvider>
  </ErrorBoundary>
)
