import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { Material, MaterialModule, MaterialParameter } from '@/datasource/api/material'
import { isNil } from 'lodash'

type ParametersQueryProps = {
  material: Material | null | undefined
  deviceModelId: string | null
}

const useQueryParameters = (data: ParametersQueryProps): UseQueryResult<MaterialParameter[]> => {
  return useQuery({
    queryKey: ['PARAMETERS', data],
    queryFn: () => {
      if (!data.material || isNil(data.deviceModelId)) return Promise.resolve([])

      return MaterialModule
        .listParameters(data.material.id, data.deviceModelId)
        .then(r => r.sort((a, b) => a.type - b.type))
    },
  })
}

export default useQueryParameters
