import SocialModule from '@/datasource/api/social'
import { PostDataParams } from '@/datasource/api/social/types'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'

export const RESOURCES_LIST_QUERY_KEY = 'RESOURCES_LIST_QUERY_KEY'
export const useInfiniteQueryResources = (data: PostDataParams) => {
  return useInfiniteScroll({
    key: RESOURCES_LIST_QUERY_KEY,
    filters: {
      typeIds: data.typeIds,
      categoryIds: data.categoryIds,
      sortType: data.sortType,
      top: data.top,
    },
    requestFn: SocialModule.postView.list,
    pageSize: 12,
  })
}
