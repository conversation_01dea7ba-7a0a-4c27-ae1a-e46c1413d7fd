import { CategoryParams } from '@/datasource/api/social/types'
import SocialModule from '@/datasource/api/social'
import { useQuery } from '@tanstack/react-query'


const useQueryCategory = (data: CategoryParams) => {
  return useQuery({
    queryKey: ['CATEGORY_LIST', data],
    queryFn: () => {
      if (!data.parentId?.toString()) return null

      return SocialModule.category.list(data)
    }
  })
}

export default useQueryCategory
