import { useMemo } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import * as fabric from 'fabric'
import { WORKSPACE_SIZE_PIXELS } from '@/libs/fabricjs-react/constants'
import { getObjectBounding } from '@/libs/fabricjs-react/utils/object'

function isObjectOverbound(object: fabric.FabricObject, maxX: number, maxY: number, transform?: fabric.TMat2D) {
  if (!object) return false

  const MINIMUM_THRESHOLD = 5e-1

  const { left, top, right, bottom } = getObjectBounding(object, transform)
  console.log({ left, top, right, bottom })

  const overbound = left < -MINIMUM_THRESHOLD
    || right > maxX + MINIMUM_THRESHOLD
    || top < -MINIMUM_THRESHOLD
    || bottom > maxY + MINIMUM_THRESHOLD

  if (overbound) {
    // console.log({ left, top, right, bottom })
  }

  return overbound
}

export const useCheckObjectOverbound = () => {
  const { selection, state } = useFabric()

  const selectionOverbound = useMemo(() => {
    if (!selection) return false

    const w = state.workspaceWidth || WORKSPACE_SIZE_PIXELS
    const h = state.workspaceHeight || WORKSPACE_SIZE_PIXELS
    const { object } = selection

    if (object instanceof fabric.ActiveSelection) {
      return object
        ._objects
        .some(o => isObjectOverbound(o, w, h, object.calcTransformMatrix()))
    }

    return isObjectOverbound(object, w, h)
  }, [selection, state])

  return { selectionOverbound }
}
