import { useFabric } from '@/contexts/fabric.context'
import { useEffect } from 'react'
import * as fabric from 'fabric'
import { isObjectLocked, setObjectLocked } from '@/libs/fabricjs-react/utils/object'

/**
 * 当选中多个对象时，若其中存在锁定的对象，则自动锁定整个选中组
 */
export const useAutoLockActiveSelection = () => {
  const { selection, editor } = useFabric()

  useEffect(() => {
    const current = selection

    if (current?.object instanceof fabric.ActiveSelection) {
      if (current.object._objects.some(o => isObjectLocked(o))) {
        setObjectLocked(current.object, true)
        editor.canvas.requestRenderAll()
      } else {
        setObjectLocked(current.object, false)
      }
    }
  }, [selection])
}
