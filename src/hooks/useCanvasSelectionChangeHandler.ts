import { useEffect, useState } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { ObjectType, VECTOR_FILL_MODES } from '@/libs/fabricjs-react/constants'
import { GeneralMenuKeys, PublicMenuKeys, TextMenuKeys, VectorMenuKeys } from '@/components/CanvasMenu'
import { FabricSelection, NonNullFabricSelection } from '@/libs/fabricjs-react'
import { useStateToRef } from '@/hooks/useStateToRef'
import { compareObject } from '@/libs/fabricjs-react/utils/object'

const isObjectsOrGroup = (selection: FabricSelection): selection is NonNullFabricSelection => {
  return !!selection && (selection.type === ObjectType.OBJECTS || selection.type === ObjectType.GROUP)
}

const isTextMenuKey = (key: unknown): key is TextMenuKeys => {
  return Object.values(TextMenuKeys).includes(key as TextMenuKeys)
}

export const useCanvasSelectionChangeHandler = () => {
  const { selection } = useFabric()
  const { inactivate, setActiveMenuKey, activeMenuKey, isEngravingPrepared, isPreviewCard } = useCanvasMenu()
  const [, setPrevSelection] = useState<FabricSelection | undefined>(selection)

  const isEngravingPreparedRef = useStateToRef(isEngravingPrepared)
  const activeMenuKeyRef = useStateToRef(activeMenuKey)
  const isPreviewCardRef = useStateToRef(isPreviewCard)

  useEffect(() => {
    setPrevSelection(prev => {
      const current = selection

      const activeMenuKey = activeMenuKeyRef.current
      const isEngravingPrepared = isEngravingPreparedRef.current
      const isPreviewCard = isPreviewCardRef.current

      if (!prev && !current) return


      // 当图层菜单启用时，或者已经处于雕刻预备状态时，不处理任何变化
      if (activeMenuKey === GeneralMenuKeys.layers || isEngravingPrepared) {
        return current
      }

      // 当预览卡片启用时，不处理任何变化
      if (isPreviewCard) {
        return current
      }

      if (!current) {
        inactivate()
        return current
      }

      if (current.type === ObjectType.TEXT && (!activeMenuKeyRef.current || !isTextMenuKey(activeMenuKeyRef.current))) {
        setActiveMenuKey(TextMenuKeys.text)
        inactivate()
        return selection
      }

      if (!prev) {
        inactivate()
      } else {
        if (prev.type !== current.type) {
          inactivate()
        }

        if (prev.type !== ObjectType.OBJECTS && current.type !== ObjectType.OBJECTS) {
          const o1 = prev.object
          const o2 = current.object

          if (!compareObject(o1, o2)) {
            inactivate()
          }
        }
      }

      if ((current.type === ObjectType.VECTOR || current.type === ObjectType.VECTOR_GROUP)) {
        if (prev && activeMenuKey === PublicMenuKeys.canvasAlign) {
          return current
        }
        if (current.object.fill === VECTOR_FILL_MODES.FILL) {
          setActiveMenuKey(VectorMenuKeys.filling)
        } else if (current.object.fill === VECTOR_FILL_MODES.NO_FILL) {
          setActiveMenuKey(VectorMenuKeys.stroke)
        } else if (current.object.fill === VECTOR_FILL_MODES.PATH_FILL) {
          setActiveMenuKey(VectorMenuKeys.pathFill)
        }
      }

      return selection
    })
  }, [selection])
}
