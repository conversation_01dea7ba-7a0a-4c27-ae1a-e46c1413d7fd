import { useTranslation } from 'react-i18next'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import { DeviceStatusData, useDeviceStatusStore } from '@/stores/device-status'
import { MessageAction, MessagePayload } from '@/libs/shared/types'
import toast from 'react-hot-toast'

export const useDeviceStatusMessageHandler = () => {
  const { t } = useTranslation()
  const { goBackwardToDesign } = useCanvasMenu()

  const { setFormItem: setDeviceStatus, reset } = useDeviceStatusStore()

  return (payload: MessagePayload<MessageAction.NOTIFY_DEVICE_STATUS>) => {
    if (payload.event === 'disconnect') {
      toast(t('device-disconnect'))
      goBackwardToDesign()
      reset()
      setDeviceStatus({
        connected: false,
        deviceModelId: null,
      })
    }

    if (payload.event === 'connect') {
      return setDeviceStatus({
        connected: true,
        deviceModelId: payload.deviceModelId
      } as any)
    }

    if (payload.event === 'working-status-update') {
      const o: Partial<DeviceStatusData> = {
        deviceCurrentState: payload.currentStatus,
        estimatedEngravingTime: payload.estimatedEngravingTime,
        engravingProgress: payload.engravingProgress,
        transmittingProgress: payload.dataTransmissionProgress,
        engravingTime: payload.engravingTime,
      }

      if (payload.dataSize) {
        o.dataSize = payload.dataSize
      }

      setDeviceStatus(o)
    }
  }
}
