import { useInfiniteQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import _ from 'lodash'
import { PageObject } from '@/datasource/request/types'

type UseInfiniteScrollProps<Params, Item> = {
  key: string;
  url?: string;
  pageSize?: number;
  filters: Params;
  requestFn: (option: Params) => Promise<PageObject<Item>>,
  enabled?: boolean
}

export function useInfiniteScroll<Params = object, Item = unknown>({
  key,
  pageSize = 10,
  filters,
  requestFn,
  enabled = true,
}: UseInfiniteScrollProps<Params, Item>) {
  const queryKey = useMemo(() => [
    key,
    ..._.values<string | string[]>(filters || {})
      .filter(o => !_.isNil(o))
  ], [key, filters])

  const queryFn = useCallback(async ({ pageParam: pageNo = 1 }) => {
    const requestParams = { pageSize, pageNo, ...filters }

    const data = await requestFn(requestParams) as PageObject
    return {
      data: data.list,
      nextPage: pageNo + 1,
      total: data.total

    }
  }, [pageSize, filters])

  const query = useInfiniteQuery({
    enabled: enabled,
    queryKey,
    queryFn,
    initialPageParam: 1,
    getNextPageParam: (lastPage, __, lastPageParam) => {
      if (!lastPageParam || lastPage.data?.length === 0) {
        return undefined
      }
      return lastPageParam + 1
    },
    getPreviousPageParam: (_, __, firstPageParam) => {
      if (!firstPageParam || firstPageParam === 1) {
        return undefined
      }
      return firstPageParam - 1
    },

  })

  const loadNext = useCallback(() => {
    query.hasNextPage && query.fetchNextPage()
  }, [query.fetchNextPage, query.hasNextPage])

  const onRefresh = useCallback(() => {
    if (!query.isFetching) {
      return query.refetch()
    }
  }, [query])

  const flattenData = useMemo<Item[]>(() => {
    return query.data?.pages.flatMap(page => page.data) || []
  }, [query.data])

  const total = useMemo<number>(() => {
    return query.data?.pages?.[0].total || 0
  }, [query.data])

  return {
    ...query,
    data: flattenData,
    onEndReached: loadNext,
    onRefresh,
    total
  }
}
