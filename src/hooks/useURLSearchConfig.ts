import { useMemo } from 'react'

import { InitialAction } from '@/libs/shared/types'
import _ from 'lodash'

type URLSearchConfig = {
  screen_height?: string
  safe_area_top?: string
  safe_area_bottom?: string
  initial_action?: InitialAction
  locale: string
  app_id?: string
  templateId?: string
  recordDataJson?: string
  deviceCanvasDataJson: string
  fileType: string
  debug?: string
}

const defaultValue: Partial<URLSearchConfig> = {
  screen_height: window.screen.height.toString(),
  safe_area_top: '0',
  safe_area_bottom: '0'
}

export function getURLSearchConfig(): URLSearchConfig {
  return _.defaults(
    Object
      .fromEntries(
        new URLSearchParams(window.location.search).entries()
      ) as unknown as URLSearchConfig,
    defaultValue
  )
}

export const useURLSearchConfig = () => {
  return useMemo(() => {
    return _.defaults(
      Object
        .fromEntries(
          new URLSearchParams(window.location.search).entries()
        ) as unknown as URLSearchConfig,
      defaultValue
    )
  }, [])
}
