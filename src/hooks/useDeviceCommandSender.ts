import { useCallback, useMemo } from 'react'
import { sendMessageToNative } from '@/utils/message'
import { useFabric } from '@/contexts/fabric.context'
import { MessageAction, PreviewMode, WorkMode } from '@/libs/shared/types'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'
import { pick } from 'lodash'
import { ObjectTypeContainingSituation } from '@/libs/fabricjs-react/constants'

export const useDeviceCommandSender = () => {
  const { editor } = useFabric()
  const parameters = useEngravingParametersStore()
  const { length, diameter, workMode } = useEngravingParametersStore()

  const { derivedState: { objectTypeContainingSituation: contained } } = useFabric()
  const { containVector, containBitmap } = useMemo(() => {
    return {
      containVector: !!(contained & ObjectTypeContainingSituation.VECTOR_ONLY),
      containBitmap: !!(contained & ObjectTypeContainingSituation.BITMAP_ONLY)
    }
  }, [contained])

  const sendEngravingCommand = useCallback(async (opts?: { isRepeat?: boolean }) => {
    const { isRepeat } = opts || {}

    const isRotation = parameters.workMode !== WorkMode.PLANE && parameters.workMode !== WorkMode.HANDHELD
    const layers = await editor.exportLayers(isRotation)
    const canvasData = await editor.exportCanvasAsJson()

    sendMessageToNative(MessageAction.START_ENGRAVING, {
      ...pick(parameters, [
        'workMode', 'diameter', 'length', 'bitmapPower', 'bitmapDPI', 'bitmapMarkTime', 'vectorSpeed', 'vectorPower', 'numberOfEngraving', 'unit'
      ]),
      layers,
      canvasData,
      materialName: parameters.material?.name,
      containVector,
      containBitmap,
      materialInfo: parameters.material,
      parameterInfo: parameters.parameter,
      isRepeat
    })
  }, [editor, parameters])

  const sendPreviewCommand = useCallback(async previewMode => {
    const isRotation = workMode !== WorkMode.PLANE && workMode !== WorkMode.HANDHELD
    const layers = await editor.exportLayers(isRotation, true)

    return sendMessageToNative(MessageAction.START_PREVIEW, {
      length: length,
      diameter: diameter,
      layers,
      workMode: workMode,
      previewMode: previewMode ?? PreviewMode.PREVIEW_SINGLE_RECT
    })
  }, [editor, workMode, length, diameter])

  const sendStopPreviewCommand = useCallback(async () => {
    return sendMessageToNative(MessageAction.STOP_PREVIEW)
  }, [])

  const sendStopEngravingCommand = useCallback(async () => {
    return sendMessageToNative(MessageAction.STOP_ENGRAVING)
  }, [])

  const sendPauseEngravingCommand = useCallback(async () => {
    return sendMessageToNative(MessageAction.PAUSE_ENGRAVING)
  }, [])

  const sendResumeEngravingCommand = useCallback(async () => {
    return sendMessageToNative(MessageAction.RESUME_ENGRAVING)
  }, [])

  return {
    sendEngravingCommand,
    sendPreviewCommand,
    sendStopPreviewCommand,
    sendStopEngravingCommand,
    sendPauseEngravingCommand,
    sendResumeEngravingCommand
  }
}
