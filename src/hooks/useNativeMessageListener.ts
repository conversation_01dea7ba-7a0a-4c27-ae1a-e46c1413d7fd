import { useEffect } from 'react'
import { listenMessageFromNative, sendMessageToNative } from '@/utils/message'
import { Buffer } from 'buffer'
import { useFabric } from '@/contexts/fabric.context'
import { FabricCore } from '@/libs/fabricjs-react/fabric-core'
import { useCanvasMenu } from '@/contexts/canvas-menu'
import {
  BitmapMenuKeys,
  EngravingMenuKeys,
  EngravingParameterMenuKeys,
  GeneralMenuKeys, OtherMenuKeys
} from '@/components/CanvasMenu'
import { ExportedLayerType, LengthUnit, MessageAction, MessageData, MessagePayload, WorkMode } from '@/libs/shared/types'
import { values } from 'lodash'
import { useDeviceStatusStore } from '@/stores/device-status'
import { useStateToRef } from '@/hooks/useStateToRef'
import { isDeviceWorking } from '@/utils/device'
import { useDeviceStatusMessageHandler } from '@/hooks/useDeviceStatusMessageHandler'
import { readBlobAsDataUrl } from '@/utils/string'
import { loadFontFormBase64 } from '@/utils/font'
import { safeParseJson, safeParseSvgDocument, sleep } from '@/utils'
import { useGlobalLoading } from '@/contexts/loading'
import { useTranslation } from 'react-i18next'
import toast from 'react-hot-toast'
import { useFontManager } from '@/contexts/font-manager.context'
import { useDeviceHandleStatusStore } from '@/stores/handle-status'
import { useEngravingParametersStore } from '@/stores/engraving-paramters'
import { useDeviceDeviceFaultKeyStore } from '@/stores/device-fault'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'

const DECIMAL_PLACES_BY_UNIT = {
  [LengthUnit.mm]: 0,
  [LengthUnit.inch]: 1
}

const toFixed = (value: number, decimalPlace: number) => Number(value.toFixed(decimalPlace))

async function loadFromBase64(editor: FabricCore, base64Data: string) {
  const rawData = Buffer.from(base64Data, 'base64').toString('utf-8')
  const parsedJson = safeParseJson(rawData)

  if (parsedJson) {
    return editor.addObjectsFromJSON(parsedJson)
  }

  const svgDoc = safeParseSvgDocument(rawData)

  if (!svgDoc) {
    return
  }

  return editor.addVectorFromString(rawData)
}

async function downloadFromUrl(uri: string) {
  const response = await fetch(uri)
  const contentType = response.headers.get('Content-Type')

  if (!contentType) throw new Error('Empty Content-Type of response')

  if (contentType === 'text/plain' || contentType === 'image/svg+xml') {
    return {
      contentType,
      data: await response.text()
    }
  }

  if (contentType.startsWith('image/')) {
    return {
      contentType,
      data: await readBlobAsDataUrl(await response.blob())
    }
  }

  throw new Error(`Unknown Content-Type: ${contentType}`)
}

async function loadOnlineFile(editor: FabricCore, uri: string) {
  const { contentType, data } = await downloadFromUrl(uri)

  if (contentType === 'text/plain') {
    if (data.startsWith('{') && data.endsWith('}')) {
      const json = safeParseJson(data)

      if (json) {
        await editor.addObjectsFromJSON(json)
      }
    }

    return
  }

  if (contentType === 'image/svg+xml') {
    return editor.addVectorFromString(data)
  }

  if (contentType.startsWith('image/')) {
    return editor.addBitmapFromUrl(data)
  }
}

export const useNativeMessageListener = () => {
  const { t } = useTranslation()
  const { setLoading } = useGlobalLoading()
  const { mountFontToApp } = useFontManager()
  const { initialized, editor } = useFabric()
  const { deviceCurrentState } = useDeviceStatusStore()

  const { debug } = useURLSearchConfig()
  const deviceStatusMessageHandler = useDeviceStatusMessageHandler()
  const { activeMenuKey, inactivate, goBackwardToDesign, setActiveMenuKey, prepareToEngrave } = useCanvasMenu()

  const activeMenuKeyRef = useStateToRef(activeMenuKey)
  const deviceCurrentStatusRef = useStateToRef(deviceCurrentState)

  const { setFormItem } = useEngravingParametersStore()
  const { setHandleStatus } = useDeviceHandleStatusStore()
  const { setDeviceFaultKey, resetDeviceFaultKey } = useDeviceDeviceFaultKeyStore()

  useEffect(() => {
    if (!initialized) return

    return listenMessageFromNative(async v => {
      const data = safeParseJson<MessageData>(v.data)

      if (debug) console.info(data)

      if (!data) return

      switch (data.action) {
        case MessageAction.IMPORT_IMAGE: {
          return editor
            .addBitmapFromUrl(data.payload)
            .catch(e => {
              console.error(e)
              toast(t('hints.failed-to-load-image'))
            })
            .finally(() => sendMessageToNative(MessageAction.RESOURCE_LOAD_END))
        }

        case MessageAction.LOAD_ONLINE_FILE: {
          setLoading(true)
          return sleep(0)
            .then(() => loadOnlineFile(editor, data.payload))
            .finally(() => setLoading(false))
        }

        case MessageAction.IMPORT_FILE: {
          return sleep(0)
            .then(() => {
              inactivate()
              return loadFromBase64(editor, data.payload)
            })
            .then(() => editor.zoomToMinimum())  // 完成后调整画布缩放
            .finally(() => {
              sendMessageToNative(MessageAction.RESOURCE_LOAD_END)
              editor.canvas.discardActiveObject()
            })
        }

        case MessageAction.LOAD_FONTS: {
          return sleep(1)
            .then(() => Promise.all(
              (data.payload as MessagePayload<MessageAction.LOAD_FONTS>)
                .map(data => {
                  return mountFontToApp(loadFontFormBase64(data), data)
                })
            ))
            .then(fontFamilies => {
              if (fontFamilies.length === 1) {
                editor.setFontFamily(fontFamilies[0])
              }
              editor.canvas.requestRenderAll()
              sendMessageToNative(MessageAction.FONTS_LOAD_END, fontFamilies)
            })
            .catch(e => {
              console.warn(e)
              sendMessageToNative(MessageAction.FONTS_LOAD_END, null)
              return toast(t('hints.failed-to-load-font'))
            })
        }

        case MessageAction.NOTIFY_DEVICE_STATUS: {
          return deviceStatusMessageHandler(data.payload)
        }

        case MessageAction.TRIGGER_BACKWARD: {
          if (isDeviceWorking(deviceCurrentStatusRef.current)) return

          const menu = activeMenuKeyRef.current

          if (menu === GeneralMenuKeys.draw || menu === BitmapMenuKeys.crop) {
            return inactivate()
          }

          if (menu === EngravingMenuKeys.engrave || menu === EngravingMenuKeys.preview) {
            return prepareToEngrave()
          }

          if (menu === EngravingMenuKeys.parameter) {
            return goBackwardToDesign()
          }

          if (menu && values(EngravingParameterMenuKeys).map(String).includes(menu)) {
            return setActiveMenuKey(EngravingMenuKeys.parameter)
          }

          return sendMessageToNative(
            MessageAction.BACKWARD,
            editor.getCanvasDataVersion()?.toString()
          )
        }

        case MessageAction.REPLACE_IMAGE_SRC: {
          setLoading(true)
          return downloadFromUrl(data.payload)
            .then(({ data }) => editor.setCurrentImageSource(data))
            .finally(() => setLoading(false))
        }

        case MessageAction.CAN_TO_ENGRAVE: {
          // 可以跳转进入参数设置页面
          editor.canvas.discardActiveObject()
          prepareToEngrave()
          return
        }

        case MessageAction.HAND_MODE_STATUS: {
          // 保存手持状态的值
          setHandleStatus({ handleStatus: data.payload === 'true' })
          return
        }

        case MessageAction.UPDATE_DEVICE_CANVAS_CONFIG: {
          // 和app端的值同步
          const { workMode, diameter, length, lengthUnit } = data.payload
          setFormItem('workMode', workMode)
          setFormItem('unit', lengthUnit)
          editor.setRulerUnit(lengthUnit)

          if (workMode === WorkMode.HANDHELD) {
            setHandleStatus({ handleStatus: true })
          }

          if (workMode === WorkMode.ROTATION) {
            const diameterPixels = editor.unitToPixels(diameter)
            setFormItem('diameter', diameter)
            editor.canvas.fire('workspace:changed', {
              workMode,
              diameter: toFixed(editor.pixelsToUnit(diameterPixels), DECIMAL_PLACES_BY_UNIT[lengthUnit])
            })
          } else if (workMode === WorkMode.TABLET) {
            const lengthPixels = editor.unitToPixels(length)
            setFormItem('length', length)
            editor.canvas.fire('workspace:length:changed', {
              workMode,
              length: toFixed(editor.pixelsToUnit(lengthPixels), DECIMAL_PLACES_BY_UNIT[lengthUnit])
            })
          } else {
            setFormItem('unit', lengthUnit)
            editor.canvas.fire('workspace:length:changed', {
              workMode,
              length: toFixed(editor.pixelsToUnit(lengthUnit === LengthUnit.mm ? 100 : 3.9), DECIMAL_PLACES_BY_UNIT[lengthUnit])
            })
          }
          return
        }

        case MessageAction.IMPORT_MATERIAL_TYPE: {
          switch (data.payload) {
            case ExportedLayerType.vector:{
              return setActiveMenuKey(OtherMenuKeys.vector)
            }

            case ExportedLayerType.bitmap:{
              return setActiveMenuKey(OtherMenuKeys.bitmap)
            }
          }
          return
        }

        case MessageAction.UPDATE_DEVICE_FAULT: {
          //同步报错信息
          resetDeviceFaultKey()
          setDeviceFaultKey({ deviceFaultKey: data.payload })
          return
        }

      }
    })
  }, [initialized, editor])
}

