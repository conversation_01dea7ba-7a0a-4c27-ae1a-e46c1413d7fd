import { useEffect, useRef } from 'react'
import { useFabric } from '@/contexts/fabric.context'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'
import { debounce } from 'lodash'
import * as fabric from 'fabric'

export const useCanvasAutoSaver = () => {
  const { editor } = useFabric()
  const lastJsonRef = useRef<string>('')
  const fallbackTimerRef = useRef<NodeJS.Timeout | number | null>(null)

  useEffect(() => {
    if (!editor?.canvas || !editor.exportCanvasAsJson) return

    if (!(editor.canvas instanceof fabric.Canvas)) {
      return
    }

    const canvas = editor.canvas

    const save = async () => {
      try {
        const data = await editor.exportCanvasAsJson(false)

        // 空画布不保存
        if (!data.objects || data.objects.length === 0) return

        const json = JSON.stringify(data)

        // 内容未变不保存
        // TODO: 性能消耗问题？
        if (json === lastJsonRef.current) return

        lastJsonRef.current = json
        sendMessageToNative(MessageAction.SAVE_DRAFT, { ...data })
      } catch (e) {
        console.warn('自动保存失败', e)
      }
    }

    // 用 lodash.debounce 实现 1 秒防抖保存
    const debouncedSave = debounce(save, 1_000)

    // 添加事件监听
    canvas.on('object:added', debouncedSave)
    canvas.on('object:removed', debouncedSave)
    canvas.on('object:modified', debouncedSave)

    // 启动每 10 秒兜底保存
    fallbackTimerRef.current = setInterval(save, 10_000)

    // 清理函数
    return () => {
      canvas.off('object:added', debouncedSave)
      canvas.off('object:removed', debouncedSave)
      canvas.off('object:modified', debouncedSave)

      if (fallbackTimerRef.current) {
        clearInterval(fallbackTimerRef.current as unknown as number)
      }
      debouncedSave.cancel()
    }
  }, [editor])
}
