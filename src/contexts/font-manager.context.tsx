import React, { FC, PropsWithChildren, useContext, useEffect } from 'react'
import { Font } from 'opentype.js'
import { LOCAL_STATIC_FONTS, REMOTE_STATIC_FONT_BASE_URL, REMOTE_STATIC_DOMESTIC_FONTS, REMOTE_STATIC_INTL_FONTS } from '@/libs/fabricjs-react/constants'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { isNil } from 'lodash'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'
import * as localforage from 'localforage'
import { QueryKeys } from '@/constants/query-keys'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'

export type FontConfig = {
  label: string
  fontFamily: string
  type?: 'otf' | 'ttf'
  isCustom?: boolean
  localUri?: string

}

export type FontManagerContextValue = {
  fonts: FontConfig[]
  mountFontToApp(font: Font, base64: string): Promise<string>
  removeFont(fontFamily: string): void
}

const LOCAL_FONTS_SESSION_KEY = 'LOCAL_FONTS'

function getLocalFonts() {
  return JSON.parse(sessionStorage.getItem(LOCAL_FONTS_SESSION_KEY) || '[]') as string[]
}

export const FontManagerContext = React.createContext<FontManagerContextValue>({} as any)

export const FontManagerProvider: FC<PropsWithChildren> = ({ children }) => {
  const { app_id } = useURLSearchConfig()
  const queryClient = useQueryClient()

  const REMOTE_STATIC_FONTS = app_id === 'com.hymlaser.laserEngraving' ? REMOTE_STATIC_DOMESTIC_FONTS : REMOTE_STATIC_INTL_FONTS
  const { data: fonts, refetch: refetchFonts } = useQuery<FontConfig[]>({
    queryKey: ['FONT_FAMILIES'],
    queryFn: () => {
      return [
        ...LOCAL_STATIC_FONTS as FontConfig[],
        ...REMOTE_STATIC_FONTS as FontConfig[],
        ...getLocalFonts().map<FontConfig>(font => ({ label: font, fontFamily: font, isCustom: true }))
      ]
    }
  })

  const mountFontToApp = async (font: Font, base64: string) => {
    const fontFamily = font.names.fontFamily.zh || font.names.fontFamily.en
    const localFonts = getLocalFonts()
    localFonts.push(fontFamily)

    if (!window.injected) {
      window.injected = {
        fonts: { [fontFamily]: base64 }
      }
    } else if (!window.injected.fonts) {
      window.injected.fonts = { [fontFamily]: base64 }
    } else {
      window.injected.fonts[fontFamily] = base64
    }

    sessionStorage.setItem(LOCAL_FONTS_SESSION_KEY, JSON.stringify(localFonts))

    const style = document.createElement('style')
    style.innerHTML = `
      @font-face {
        font-family: '${fontFamily}'; 
        src: url(data:font/ttf;base64,${base64}) format('truetype');
      }
    `
    document.head.append(style)

    void refetchFonts()

    return fontFamily
  }

  const removeFont = (target?: string) => {
    if (isNil(target)) return

    const current = getLocalFonts()
    const index = current.indexOf(target)
    sendMessageToNative(MessageAction.REMOVE_FONT, target)

    if (index !== -1) {
      sessionStorage.setItem(LOCAL_FONTS_SESSION_KEY, JSON.stringify(current.toSpliced(index)))
      void refetchFonts()
    }
  }

  useEffect(() => {
    if (!fonts) return

    (async () => {
      for (const font of fonts) {
        const key = font.fontFamily
        if (font.isCustom || font.localUri) continue

        const fontCache = await localforage.getItem<ArrayBuffer>(key)
        if (fontCache) {
          document.fonts.add(new FontFace(key, fontCache))
          continue
        }

        void fetch(`${REMOTE_STATIC_FONT_BASE_URL}/${font.fontFamily}.${font.type}`)
          .then(response => response.arrayBuffer())
          .then(buffer => {
            localforage.setItem(key, buffer)
            document.fonts.add(new FontFace(key, buffer))
            queryClient.refetchQueries({
              queryKey: [QueryKeys.REMOTE_FONT_AVAILABLE, key]
            })
          })
          .catch(e => {
            console.warn(`字体${key}加载失败: ${e.message}`)
          })
      }
    })()

  }, [fonts])

  return (
    <FontManagerContext.Provider
      value={{
        fonts: fonts || [],
        mountFontToApp,
        removeFont
      }}
    >
      {children}
    </FontManagerContext.Provider>
  )
}

export const useFontManager = () => useContext(FontManagerContext)
