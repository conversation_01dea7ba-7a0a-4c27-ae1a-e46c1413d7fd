import { createContext, PropsWithChildren, useContext, useMemo, useState } from 'react'
import { EngravingMenuKeys, GeneralMenuKeys, MenuKeys, PublicMenuKeys } from '@/components/CanvasMenu'
import { useDeviceStatusStore } from '@/stores/device-status'
import { InitialAction } from '@/libs/shared/types'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import { values } from 'lodash'

type CanvasMenuContextProps = {
  activeMenuKey: MenuKeys | null,
  isEngravingPrepared: boolean
  isPreviewCard: boolean
  arrangementMode: boolean

  setActiveMenuKey(index: MenuKeys | null): void
  inactivate(): void
  prepareToEngrave(): void
  startToEngrave(): void
  goBackwardToDesign(): void
  setArrangementMode: (v: boolean) => void
}

const CanvasMenuContext = createContext<CanvasMenuContextProps>({} as any)

export function CanvasMenuProvider({ children }: PropsWithChildren) {
  const { initial_action } = useURLSearchConfig()
  const { reset: resetDeviceStatus } = useDeviceStatusStore()

  const [activeMenuKey, setActiveMenuKey] = useState<MenuKeys | null>(() => {
    switch (initial_action) {
      case InitialAction.PAINTING: return GeneralMenuKeys.draw
      case InitialAction.TEXT_CONTENT: return GeneralMenuKeys.content
      default: return null
    }
  })

  const [arrangementMode, setArrangementMode] = useState(false)

  return (
    <CanvasMenuContext.Provider
      value={{
        activeMenuKey,
        isEngravingPrepared: useMemo(() => !!activeMenuKey && values(EngravingMenuKeys).map(String).includes(activeMenuKey), [activeMenuKey]),
        isPreviewCard: useMemo(() => activeMenuKey === PublicMenuKeys.previewCard, [activeMenuKey]),
        arrangementMode,

        setActiveMenuKey,
        inactivate: () => setActiveMenuKey(null),

        prepareToEngrave() {
          resetDeviceStatus()
          setActiveMenuKey(EngravingMenuKeys.parameter)
        },
        startToEngrave() {
          resetDeviceStatus()
          setActiveMenuKey(EngravingMenuKeys.engrave)
        },
        goBackwardToDesign() {
          resetDeviceStatus()
          setActiveMenuKey(null)
        },

        setArrangementMode
      }}
    >
      {children}
    </CanvasMenuContext.Provider>
  )
}

export function useCanvasMenu() {
  return useContext(CanvasMenuContext)
}
