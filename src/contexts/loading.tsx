import React, { PropsWithChildren, useContext, useState } from 'react'
import { ZIndices } from '@/constants/z-index'
import { useTranslation } from 'react-i18next'
import { Loading } from '@/components/ui/Loading'
import { sleep } from '@/utils'

export const LoadingContext = React.createContext({
  setLoading: (_loading: boolean, _text?: string) => {},
  withLoading<T>(_: (() => Promise<T>), _text?: string): Promise<T> {
    return Promise.resolve(null as T)
  }
})

export const LoadingProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { t } = useTranslation()

  const [loading, _setLoading] = useState(false)
  const [text, setText] = useState('')

  const setLoading = (val: boolean, _text?: string) => {
    _setLoading(val)
    setText(_text || t('loading'))
  }

  function withLoading<T>(fun: (() => Promise<T>), _text?: string) {
    setLoading(true, _text)
    return sleep(100)
      .then(fun)
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <LoadingContext.Provider
      value={{
        setLoading,
        withLoading
      }}
    >
      {children}

      {loading && (
        <div
          className="bg-white/30 absolute w-screen h-screen flex flex-col justify-center items-center"
          style={{ zIndex: ZIndices.Loading }}
        >
          <Loading />
          <div className="text=-sm text-primary/80 text-center">{text}</div>
        </div>
      )}
    </LoadingContext.Provider>
  )
}

export const useGlobalLoading = () => {
  return useContext(LoadingContext)
}
