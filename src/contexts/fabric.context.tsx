import React, { createContext, FC, useCallback, useContext, useEffect, useRef, useState } from 'react'
import { EmptyFabricCore, FabricCore, FabricEditorImpl } from '@/libs/fabricjs-react/fabric-core'
import { FabricState, StatelessFabricCoreContext } from '@/libs/fabricjs-react/types/base'
import * as fabric from 'fabric'
import { EraserBrush } from '@erase2d/fabric'
import { ObjectType, ObjectTypeContainingSituation } from '@/libs/fabricjs-react/constants'
import { getTypeOfFabricObject, shouldExportAsBitmap } from '@/libs/fabricjs-react/utils'
import _, { throttle } from 'lodash'
import { FabricSelection, NonNullFabricSelection } from '@/libs/fabricjs-react'
import { useTranslation } from 'react-i18next'
import { sendMessageToNative } from '@/utils/message'
import { MessageAction } from '@/libs/shared/types'
import { findMinimumXAndY, isObjectLocked } from '@/libs/fabricjs-react/utils/object'

export interface FabricContextValues {
  initialized: boolean

  editor: FabricCore
  selection?: FabricSelection
  state: FabricState

  derivedState: {
    objectTypeContainingSituation: ObjectTypeContainingSituation,
    layersBoundarySize: { width: number; height: number }
  }
}

type InitializeFn = (context: Omit<StatelessFabricCoreContext, 't'>) => void

const useLayersBoundarySize = (initialized: boolean, editor: FabricCore) => {
  const [boundarySize, setBoundarySize] = useState({ width: 0, height: 0 })

  useEffect(() => {
    if (!initialized) return

    const { canvas } = editor

    const handler = () => {
      const objects = canvas.getObjects()
        .filter(o => !o.__is_system_object__)
        .filter(o => !(o instanceof fabric.Group) || o._objects.length)
        .filter(o => o.visible)

      Promise
        .all(objects.map(object => object.clone()))
        .then(clones => {
          const group = new fabric.Group(clones)
          const { width, height, scaleX, scaleY } = group
          setBoundarySize({ width: width * scaleX, height: height * scaleY })

          group.dispose()
          clones.forEach(o => o.dispose())
        })
    }

    const disposers = [
      canvas.on('object:added', handler),
      canvas.on('object:modified', handler),
      canvas.on('object:resizing', handler),
      canvas.on('object:scaling', handler),
      canvas.on('object:removed', handler),
      canvas.on('selection:updated', handler),
      canvas.on('selection:cleared', handler),
    ]
    handler()

    return () => {
      disposers.forEach(dispose => dispose())
    }
  }, [editor])

  return boundarySize
}

const useObjectTypeContainingSituation = (initialized: boolean, editor: FabricCore) => {
  const [objectTypeContainingSituation, setObjectTypeContainingSituation] = useState(ObjectTypeContainingSituation.NOTHING)

  useEffect(() => {
    if (!initialized) return

    const { canvas } = editor

    const handler = () => {
      const objects = canvas.getObjects()
        .filter(o => !o.__is_system_object__)
        .filter(o => !(o instanceof fabric.Group) || o._objects.length)
        .filter(o => o.visible)

      setObjectTypeContainingSituation(
        objects.reduce((result, next) => {
          if (shouldExportAsBitmap(next)) {
            return result | ObjectTypeContainingSituation.BITMAP_ONLY
          } else {
            return result | ObjectTypeContainingSituation.VECTOR_ONLY
          }
        }, ObjectTypeContainingSituation.NOTHING)
      )
    }

    const disposers = [
      canvas.on('object:added', handler),
      canvas.on('object:modified', handler),
      canvas.on('object:removed', handler),
    ]
    handler()

    return () => {
      disposers.forEach(dispose => dispose())
    }
  }, [initialized, editor])

  return objectTypeContainingSituation
}

const useLimitObjectMovingArea = (initialized: boolean, editor: FabricCore) => {
  useEffect(() => {
    if (!initialized) return

    const handler = ({ target }: { target: fabric.FabricObject }) => {
      const { x, y } = findMinimumXAndY(target)

      if (target.left < x) target.left = x
      if (target.top < y) target.top = y
    }

    const disposers = [
      editor.canvas.on('object:moving', handler),
      editor.canvas.on('object:rotating', handler),
      editor.canvas.on('object:scaling', handler)
    ]

    return () => {
      disposers.forEach(dispose => dispose())
    }
  }, [initialized])
}

export const FabricContext = createContext<FabricContextValues>({} as any)

export const FabricProvider: FC<{ children: (initialize: InitializeFn) => React.ReactElement }> = ({
  children
}) => {
  const { t } = useTranslation()
  const [editor, setEditor] = useState<FabricCore>(EmptyFabricCore)
  const [initialized, setInitialized] = useState(false)
  const [selection, setSelection] = useState<FabricSelection>(null)
  const [context, setContext] = useState<null | Omit<StatelessFabricCoreContext, 't'>>(null)
  const [editorState, setEditorState] = useState<FabricState & { __v: number }>({
    __v: 0
  })

  const eventListenerDisposerRef = useRef<VoidFunction>()

  const stateChangeListener = useRef((newVal: FabricState) => {
    setEditorState(prev => {
      _.set(newVal, '__v', prev.__v + 1)
      return newVal as FabricState & { __v: number }
    })
  })

  const bindEvents = (canvas: fabric.Canvas) => {
    const selectionHandler = (updateVersion = false) => {
      const objs = canvas.getActiveObjects()
      const isUsingEraser = canvas.isDrawingMode && canvas.freeDrawingBrush instanceof EraserBrush

      // 在画布上启用橡皮擦开始擦除时，将会自动取消选中; 为了保持菜单显示，故屏蔽该情况下的状态更新
      if (isUsingEraser) return

      setSelection(prevSelection => {
        if (objs.length === 0) return null

        const prevVersion = prevSelection?.__version__ || 0
        const __version__ = updateVersion ? prevVersion + 1 : prevVersion
        const __updated_at__ = Date.now()

        const object = objs.length === 1
          ? objs[0]
          : canvas._activeObject as fabric.ActiveSelection

        const type = objs.length === 1
          ? getTypeOfFabricObject(object)
          : ObjectType.OBJECTS

        const isLocked = isObjectLocked(object)

        return {
          type,
          object,
          isLocked,
          __updated_at__,
          __version__
        } as NonNullFabricSelection
      })
    }

    const disposers = [
      canvas.on('selection:cleared', () => selectionHandler(true)),
      canvas.on('selection:created', () => selectionHandler(true)),
      canvas.on('selection:updated', () => selectionHandler(true)),
      canvas.on('object:moving', throttle(() => selectionHandler(false), 50)),
      canvas.on('object:modified', throttle(() => selectionHandler(false), 50)),
    ]

    return () => disposers.forEach(disposer => disposer())
  }

  const objectTypeContainingSituation = useObjectTypeContainingSituation(initialized, editor)

  const initialize = useCallback<InitializeFn>(val => {
    if (context) return

    setContext(() => {
      const newContext = val

      setInitialized(true)

      setEditor(FabricEditorImpl.createInstance(
        { ...newContext, t },
        stateChangeListener.current
      ))

      eventListenerDisposerRef.current = bindEvents(newContext.canvas)

      sendMessageToNative(MessageAction.PREPARED)

      // 同步app的设备相关数据
      sendMessageToNative(MessageAction.GET_DEVICE_CONFIG)

      sendMessageToNative(MessageAction.DEVICE_IF_FAULT)
      return newContext
    })
  }, [context])

  const layersBoundarySize = useLayersBoundarySize(initialized, editor)

  useLimitObjectMovingArea(initialized, editor)

  useEffect(() => {
    return () => {
      eventListenerDisposerRef.current?.()
    }
  }, [])

  return (
    <FabricContext.Provider
      value={{
        initialized,
        state: editorState,
        selection,
        editor,
        derivedState: {
          objectTypeContainingSituation,
          layersBoundarySize
        }
      }}
    >
      {children(initialize)}
    </FabricContext.Provider>
  )
}

export const useFabric = () => useContext(FabricContext)
