import { MessageAction, MessageData, MessagePayload } from '@/libs/shared/types'

export function sendMessageToNative<Action extends MessageAction>(action: Action, payload?: MessagePayload<Action>) {
  window.ReactNativeWebView?.postMessage(JSON.stringify({
    action, payload
  } satisfies MessageData))
}

export function listenMessageFromNative(handler: (e: Event & { data: any }) => void) {
  document.addEventListener('message', handler)
  window.addEventListener('message', handler)

  return () => {
    document.removeEventListener('message', handler)
    window.removeEventListener('message', handler)
  }
}
