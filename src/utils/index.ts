export function sleep(timeout: number): Promise<void> {
  return new Promise(resolve => {
    setTimeout(resolve, timeout)
  })
}

export function safeParseJson<T extends Record<string, any> = Record<string, any>>(source?: any): T | null {
  if (!source) return null
  if (typeof source === 'object') return source

  try {
    return JSON.parse(source)
  } catch (e) {
    return null
  }
}

export function safeParseSvgDocument(src: string): Document | null {
  try {
    return new DOMParser().parseFromString(src, 'image/svg+xml')
  } catch (e) {
    return null
  }
}
