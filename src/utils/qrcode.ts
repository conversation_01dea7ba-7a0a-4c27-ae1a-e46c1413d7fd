import * as QRCode from 'qrcode'
import { wrapWithSvgRoot } from '@/libs/fabricjs-react/utils'

export function generateQrcodeSvg(content: string) {
  const { modules } = QRCode.create(content)

  const scale = 8

  const paths = Array.from(modules.data)
    .map((value, index) => {
      if (!value) return undefined

      const y = Math.floor(index / modules.size) * scale
      const x = (index % modules.size) * scale

      return `<rect x="${x}" y="${y}" width="${scale}" height="${scale}" />`
    })
    .filter(Boolean)

  return wrapWithSvgRoot(paths.join(''), modules.size * scale, modules.size * scale)
}
