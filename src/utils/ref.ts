import React, { createRef } from 'react'

const map = new Map<string, React.RefObject<unknown>>()

function setRef<T>(key: string): React.RefObject<T> | undefined {
  if (!key) {
    console.warn('useDynamicRefs: Cannot set ref without key ')
    return
  }
  const ref = createRef<T>()
  map.set(key, ref)
  return ref
}

function getRef<T>(key: string): React.RefObject<T> | undefined  {
  if (!key)  {
    console.warn('useDynamicRefs: Cannot get ref without key')
    return
  }
  return map.get(key) as React.RefObject<T>
}

function useDynamicRefs<T>(): [
  (key: string) => undefined | React.RefObject<T>,
  (key: string) => undefined | React.RefObject<T>,
] {
  return [getRef, setRef]
}

export default useDynamicRefs
