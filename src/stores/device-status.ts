import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { DeviceWorkingStatus } from '@/libs/shared/types'

export type DeviceStatusData = {
  connected: boolean
  deviceModelId: string | null

  deviceCurrentState: DeviceWorkingStatus

  engravingProgress: number
  estimatedEngravingTime: number
  transmittingProgress: number
  estimatedTransmittingTime: number
  engravingTime: number
  dataSize: number

  engraveStartTime: number | null
}

type DeviceStatusStore = DeviceStatusData & {
  reset(): void
  setFormItem(data: Partial<DeviceStatusData>): void
}

const DEFAULT_STATE: Partial<DeviceStatusData> = {
  deviceCurrentState: DeviceWorkingStatus.IDLE,

  engravingProgress: 0,
  estimatedEngravingTime: 0,
  transmittingProgress: 0,
  estimatedTransmittingTime: 0,

  engraveStartTime: null,
}

export const useDeviceStatusStore = create<DeviceStatusStore>()(
  immer<DeviceStatusStore>(
    set => {
      return ({
        ...DEFAULT_STATE as any,

        connected: false,
        deviceModelId: null,

        reset() {
          set(DEFAULT_STATE)
        },

        setFormItem(entries) {
          set(prev => ({
            ...prev,
            ...entries
          }))
        },
      })
    }
  )
)
