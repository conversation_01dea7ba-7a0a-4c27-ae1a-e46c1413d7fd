import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

export type HandleStatusData = {
  handleStatus: boolean
}

type DeviceHandleStatusStore = HandleStatusData & {
  setHandleStatus: (data: Partial<HandleStatusData>) => void
  resetHandleStatus: () => void
}

const DEFAULT_STATE: HandleStatusData = {
  handleStatus: false
}

export const useDeviceHandleStatusStore = create<DeviceHandleStatusStore>()(
  immer(set => ({
    ...DEFAULT_STATE,

    setHandleStatus: data => {
      set(state => {
        Object.assign(state, data)
      })
    },

    resetHandleStatus: () => {
      set(DEFAULT_STATE)
    },
  }))
)
