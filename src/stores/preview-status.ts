import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { DeviceStatusCode } from '@/libs/shared/types'

export type DevicePreviewData = {
  devicePreviewStatus: DeviceStatusCode
}

type DevicePreviewStore = DevicePreviewData & {
  setPreview: (data: Partial<DevicePreviewData>) => void
  resetPreview: () => void
}

const DEFAULT_STATE: DevicePreviewData = {
  devicePreviewStatus: DeviceStatusCode.IDLE
}

export const useDevicePreviewStore = create<DevicePreviewStore>()(
  immer(set => ({
    ...DEFAULT_STATE,

    setPreview: data => {
      set(state => {
        Object.assign(state, data)
      })
    },

    resetPreview: () => {
      set(DEFAULT_STATE)
    },
  }))
)
