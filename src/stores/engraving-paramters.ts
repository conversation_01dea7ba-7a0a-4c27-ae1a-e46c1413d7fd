import { create } from 'zustand'
import { ExportedLayerType, LengthUnit, PreviewMode, WorkMode } from '@/libs/shared/types'
import { immer } from 'zustand/middleware/immer'
import { Material, MaterialParameter } from '@/datasource/api/material'
import _ from 'lodash'
import { FormDataWithSetter } from '@/utils/typings'

export type EngravingParametersFormData = {
  workMode: WorkMode
  diameter: number
  unit: LengthUnit
  length: number

  material?: Material
  parameter?: MaterialParameter
  parameterName?: string
  objectType?: ExportedLayerType

  numberOfEngraving: number

  vectorSpeed: number
  vectorPower: number
  bitmapMarkTime: number
  bitmapPower: number
  bitmapDPI?: any

  previewMode?: PreviewMode
}

type EngravingParametersStore = {
  reset(): void
} & FormDataWithSetter<EngravingParametersFormData>

const DEFAULT_PARAMETERS = {
  diameter: 60,
  unit: LengthUnit.mm,
  length: 100,

  objectType: undefined,
  material: undefined,
  parameter: undefined,
  numberOfEngraving: 1,

  vectorSpeed: 0,
  vectorPower: 0,
  bitmapMarkTime: 0,
  bitmapPower: 0,
  bitmapDPI: undefined,
} satisfies Partial<EngravingParametersFormData>

export const useEngravingParametersStore = create<EngravingParametersStore>()(
  immer(
    set => ({
      workMode: WorkMode.PLANE,
      previewMode: PreviewMode.PREVIEW_SINGLE_RECT,
      ...DEFAULT_PARAMETERS,

      setFormItem(key, value) {
        set(prev => {
          _.set(prev, key, value)
        })
      },
      reset() {
        set(prev => ({
          ...prev,
          ...DEFAULT_PARAMETERS
        }))
      }
    })
  )
)
