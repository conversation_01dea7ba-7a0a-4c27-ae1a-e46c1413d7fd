import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

export type DeviceFaultKeyData = {
  deviceFaultKey: string[]
}

type DeviceDeviceFaultKeyStore = DeviceFaultKeyData & {
  setDeviceFaultKey: (data: Partial<DeviceFaultKeyData>) => void
  resetDeviceFaultKey: () => void
}

const DEFAULT_STATE: DeviceFaultKeyData = {
  deviceFaultKey: []
}

export const useDeviceDeviceFaultKeyStore = create<DeviceDeviceFaultKeyStore>()(
  immer(set => ({
    ...DEFAULT_STATE,

    setDeviceFaultKey: data => {
      set({
        deviceFaultKey: [...(data.deviceFaultKey ?? [])]
      })
    },

    resetDeviceFaultKey: () => {
      set(DEFAULT_STATE)
    },
  }))
)
