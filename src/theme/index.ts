import { createTheme, ThemeOptions } from '@mui/material'
import { green } from '@mui/material/colors'

const BottomNavigation: ThemeOptions['components'] = {
  MuiBottomNavigation: {
    styleOverrides: {
      root: () => ({
        width: '100%',
        overflow: 'scroll'
      })
    }
  },
  MuiBottomNavigationAction: {
    styleOverrides: {
      root: {
        minWidth: 0
      },
      label: {
        whiteSpace: 'nowrap'
      }
    }
  }
}

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#F18D00',
      200: '#F8C67F'
    },
    secondary: {
      main: green[500],
    },
    grey: {
      50: '#eeeeee',
      100: '#F7F7F7',
      200: '#e5e5e5',
      300: '#cccccc',
      400: '#999999',
      500: '#666666',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#333333',
    }
  },

  components: {
    ...BottomNavigation,
    // Name of the component
    MuiButtonBase: {
      defaultProps: {
        // The props to change the default for.
        disableRipple: true, // No more ripple, on the whole application 💣!
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          '& > .MuiInputBase-input.Mui-disabled': {
            color: 'rgb(0,0,0,.87)',
            WebkitTextFillColor: 'unset'
          }
        }
      }
    }
  },
})

export default theme
