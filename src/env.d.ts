/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

import { WebviewInjectedWindow } from '@/libs/shared/types'

declare global {
  interface Window extends WebviewInjectedWindow {
    ReactNativeWebView?: {
      injectedObjectJson: () => any
      postMessage: (message: any) => void
    }
  }

  // defined in vite.config.ts
  const __FABRICJS_VERSION__: string
}

declare module 'i18next' {
  type TFunction = (key: string, params?: Record<string, any>) => string
}

export {}
