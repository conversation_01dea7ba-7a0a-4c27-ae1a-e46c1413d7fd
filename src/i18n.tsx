import translationEn from '@/assets/locales/en-US.json'
import translationZh from '@/assets/locales/zh-CN.json'
import translationJa from '@/assets/locales/ja-JP.json'
import { useURLSearchConfig } from '@/hooks/useURLSearchConfig'
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import { FC, PropsWithChildren, useEffect, useState } from 'react'

export enum LanguageCodes {
  'en-US' = 'en-US',
  'zh-CN' = 'zh-CN',
  'ja-JP' = 'ja-JP',
}

const DEFAULT_LANGUAGE = LanguageCodes['zh-CN']

const resources = {
  [LanguageCodes['en-US']]: { translation: translationEn },
  [LanguageCodes['zh-CN']]: { translation: translationZh },
  [LanguageCodes['ja-JP']]: { translation: translationJa }
}

export const LanguageStorageKey = 'language'

const useI18nHook = () => {
  const { locale } = useURLSearchConfig()

  const initI18n = async () => {
    await (i18n as any).use(initReactI18next).init({
      compatibilityJSON: 'v3',
      resources,
      lng: locale,
      fallbackLng: DEFAULT_LANGUAGE,
      interpolation: {
        escapeValue: false,
      },
    })
  }

  return {
    initI18n
  }
}

export const I18nProvider: FC<PropsWithChildren> = ({ children }) => {
  const [ready, setReady] = useState(false)
  const { initI18n } = useI18nHook()

  useEffect(() => {
    initI18n().then(() => setReady(true))
  }, [])

  if (!ready) return null

  return children
}


export default useI18nHook
