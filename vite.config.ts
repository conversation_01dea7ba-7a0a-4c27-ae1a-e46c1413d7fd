import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from 'url'
import svgr from 'vite-plugin-svgr'
import { dependencies } from './package.json'
import { config as configDotenv } from 'dotenv'
import tailwindcss from '@tailwindcss/vite'

configDotenv()

// https://vitejs.dev/config/
export default defineConfig({
  base: `/fabric/${process.env.VITE_APP_VERSION}`,
  build: {
    outDir: `dist/${process.env.VITE_APP_VERSION}`,
  },
  plugins: [
    react(),
    svgr(),
    tailwindcss(),
  ],
  resolve: {
    alias: [
      { find: '@', replacement: fileURLToPath(new URL('./src', import.meta.url)) }
    ]
  },
  define: {
    '__FABRICJS_VERSION__': JSON.stringify(dependencies.fabric.toString().replace('^', ''))
  }
})
