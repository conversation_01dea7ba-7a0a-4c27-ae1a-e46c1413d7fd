{"compilerOptions": {"allowJs": true, "esModuleInterop": true, "incremental": true, "target": "es2022", "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "resolveJsonModule": true, "composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "strict": false, "noEmit": true, "strictNullChecks": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["vite.config.ts", "src/**/*.ts", "src/**/*.tsx"]}