{"name": "react-fabric-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev:domestic": "vite --host --mode domestic", "dev:intl": "vite --host --mode intl", "build:domestic": "tsc -b && vite build --mode domestic", "build:intl": "tsc -b && vite build --mode intl", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@erase2d/fabric": "^1.1.6", "@hello-pangea/dnd": "^16.6.0", "@mui/base": "5.0.0-beta.58", "@mui/material": "^5.16.6", "@mui/styles": "^5.16.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.59.13", "axios": "^1.7.7", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fabric": "6.7.0", "i18next": "^23.15.2", "immer": "^10.1.1", "jsbarcode": "^3.11.6", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "opentype.js": "^1.3.4", "qrcode": "^1.5.4", "qs": "^6.13.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-i18next": "^15.0.2", "react-image-crop": "^11.0.6", "react-infinite-scroll-component": "^6.1.0", "react-mobile-picker": "1.0.1", "react-responsive-masonry": "^2.4.1", "react-router-dom": "^6.27.0", "sass": "^1.77.8", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "uuid": "^10.0.0", "vconsole": "^3.15.1", "zustand": "4.5.5"}, "devDependencies": {"@stylistic/eslint-plugin": "^2.4.0", "@types/jsbarcode": "^3.11.4", "@types/lodash": "^4.17.7", "@types/node": "^22.1.0", "@types/opentype.js": "^1.3.8", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.16", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.5.2", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-unused-imports": "^4.0.1", "tw-animate-css": "^1.3.4", "typescript": "^5.7.2", "url": "^0.11.4", "vite": "^6.3.5", "vite-plugin-svgr": "^4.2.0"}}